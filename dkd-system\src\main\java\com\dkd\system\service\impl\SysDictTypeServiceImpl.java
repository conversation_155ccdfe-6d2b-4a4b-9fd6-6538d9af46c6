package com.dkd.system.service.impl;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.dkd.common.constant.UserConstants;
import com.dkd.common.core.domain.entity.SysDictData;
import com.dkd.common.core.domain.entity.SysDictType;
import com.dkd.common.exception.ServiceException;
import com.dkd.common.utils.DictUtils;
import com.dkd.common.utils.StringUtils;
import com.dkd.system.mapper.SysDictDataMapper;
import com.dkd.system.mapper.SysDictTypeMapper;
import com.dkd.system.service.ISysDictTypeService;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
// 定义字典类型服务实现类
@Service
public class SysDictTypeServiceImpl implements ISysDictTypeService
{
    // 注入字典类型数据库操作Mapper
    @Autowired
    private SysDictTypeMapper dictTypeMapper;

    // 注入字典数据数据库操作Mapper，用于关联字典类型和数据
    @Autowired
    private SysDictDataMapper dictDataMapper;

    /**
     * 项目启动时初始化字典缓存
     * 使用@PostConstruct注解确保在Bean初始化完成后执行
     */
    @PostConstruct
    public void init()
    {
        loadingDictCache();
    }

    /**
     * 根据条件分页查询字典类型列表
     * 
     * @param dictType 查询条件封装对象
     * @return 符合条件的字典类型列表
     */
    @Override
    public List<SysDictType> selectDictTypeList(SysDictType dictType)
    {
        return dictTypeMapper.selectDictTypeList(dictType);
    }

    /**
     * 查询所有字典类型
     * 
     * @return 全部字典类型列表
     */
    @Override
    public List<SysDictType> selectDictTypeAll()
    {
        return dictTypeMapper.selectDictTypeAll();
    }

    /**
     * 根据字典类型查询对应的字典数据
     * 优先从缓存获取，不存在则从数据库获取并写入缓存
     * 
     * @param dictType 字典类型编码
     * @return 对应的字典数据集合
     */
    @Override
    public List<SysDictData> selectDictDataByType(String dictType)
    {
        // 尝试从缓存获取数据
        List<SysDictData> dictDatas = DictUtils.getDictCache(dictType);
        if (StringUtils.isNotEmpty(dictDatas))
        {
            return dictDatas;
        }
        
        // 缓存不存在则查询数据库
        dictDatas = dictDataMapper.selectDictDataByType(dictType);
        if (StringUtils.isNotEmpty(dictDatas))
        {
            // 查询结果存在，则更新缓存
            DictUtils.setDictCache(dictType, dictDatas);
            return dictDatas;
        }
        return null;
    }

    /**
     * 根据字典类型ID查询详细信息
     * 
     * @param dictId 字典类型ID
     * @return 字典类型实体对象
     */
    @Override
    public SysDictType selectDictTypeById(Long dictId)
    {
        return dictTypeMapper.selectDictTypeById(dictId);
    }

    /**
     * 根据字典类型编码查询详细信息
     * 
     * @param dictType 字典类型编码
     * @return 字典类型实体对象
     */
    @Override
    public SysDictType selectDictTypeByType(String dictType)
    {
        return dictTypeMapper.selectDictTypeByType(dictType);
    }

    /**
     * 批量删除字典类型
     * 删除前校验该类型是否有关联数据，避免误删
     * 
     * @param dictIds 需要删除的字典类型ID数组
     */
    @Override
    public void deleteDictTypeByIds(Long[] dictIds)
    {
        for (Long dictId : dictIds)
        {
            // 查询待删除的字典类型
            SysDictType dictType = selectDictTypeById(dictId);
            
            // 校验该类型下是否存在字典数据
            if (dictDataMapper.countDictDataByType(dictType.getDictType()) > 0)
            {
                throw new ServiceException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
            }
            
            // 数据库中删除记录
            dictTypeMapper.deleteDictTypeById(dictId);
            
            // 同步清除缓存
            DictUtils.removeDictCache(dictType.getDictType());
        }
    }

    /**
     * 加载所有字典数据到缓存
     * 系统启动或需要刷新缓存时调用
     */
    @Override
    public void loadingDictCache()
    {
        // 创建查询参数：只加载启用状态的数据
        SysDictData dictData = new SysDictData();
        dictData.setStatus("0");
        
        // 查询全部字典数据并按类型分组
        Map<String, List<SysDictData>> dictDataMap = dictDataMapper.selectDictDataList(dictData).stream()
                .collect(Collectors.groupingBy(SysDictData::getDictType));
        
        // 按字典排序字段排序后写入缓存
        for (Map.Entry<String, List<SysDictData>> entry : dictDataMap.entrySet())
        {
            DictUtils.setDictCache(entry.getKey(), 
                entry.getValue().stream()
                     .sorted(Comparator.comparing(SysDictData::getDictSort))
                     .collect(Collectors.toList()));
        }
    }

    /**
     * 清空所有字典缓存
     */
    @Override
    public void clearDictCache()
    {
        DictUtils.clearDictCache();
    }

    /**
     * 重置字典缓存
     * 先清空再重新加载
     */
    @Override
    public void resetDictCache()
    {
        clearDictCache();   // 清空现有缓存
        loadingDictCache(); // 重新加载缓存
    }

    /**
     * 新增字典类型
     * 成功插入后同步更新Redis缓存
     * 
     * @param dict 待新增的字典类型
     * @return 插入影响行数
     */
    @Override
    public int insertDictType(SysDictType dict)
    {
        int row = dictTypeMapper.insertDictType(dict);
        if (row > 0)
        {
            // 插入成功则更新缓存
            DictUtils.setDictCache(dict.getDictType(), null);
        }
        return row;
    }

    /**
     * 修改字典类型
     * 若修改了dictType需同步更新字典数据表中的对应type
     * 
     * @param dict 待更新的字典类型
     * @return 更新影响行数
     */
    @Override
    @Transactional
    public int updateDictType(SysDictType dict)
    {
        // 查询旧的字典类型
        SysDictType oldDict = dictTypeMapper.selectDictTypeById(dict.getDictId());
        
        // 更新字典数据表中的dict_type字段
        dictDataMapper.updateDictDataType(oldDict.getDictType(), dict.getDictType());
        
        // 执行更新操作
        int row = dictTypeMapper.updateDictType(dict);
        if (row > 0)
        {
            // 查询最新的字典数据
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(dict.getDictType());
            
            // 更新缓存
            DictUtils.setDictCache(dict.getDictType(), dictDatas);
        }
        return row;
    }

    /**
     * 校验字典类型名称唯一性
     * 
     * @param dict 待校验的字典类型
     * @return 是否唯一标识
     */
    @Override
    public boolean checkDictTypeUnique(SysDictType dict)
    {
        // 获取当前要校验的dictId，为空默认赋值-1L
        Long dictId = StringUtils.isNull(dict.getDictId()) ? -1L : dict.getDictId();
        
        // 查询是否存在相同dictType的记录
        SysDictType dictType = dictTypeMapper.checkDictTypeUnique(dict.getDictType());
        
        // 存在且不是当前记录，说明不唯一
        if (StringUtils.isNotNull(dictType) && dictType.getDictId().longValue() != dictId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
}








// @Service
// public class SysDictTypeServiceImpl implements ISysDictTypeService
// {
//     @Autowired
//     private SysDictTypeMapper dictTypeMapper;

//     @Autowired
//     private SysDictDataMapper dictDataMapper;

//     /**
//      * 项目启动时，初始化字典到缓存
//      */
//     @PostConstruct
//     public void init()
//     {
//         loadingDictCache();
//     }

//     /**
//      * 根据条件分页查询字典类型
//      * 
//      * @param dictType 字典类型信息
//      * @return 字典类型集合信息
//      */
//     @Override
//     public List<SysDictType> selectDictTypeList(SysDictType dictType)
//     {
//         return dictTypeMapper.selectDictTypeList(dictType);
//     }

//     /**
//      * 根据所有字典类型
//      * 
//      * @return 字典类型集合信息
//      */
//     @Override
//     public List<SysDictType> selectDictTypeAll()
//     {
//         return dictTypeMapper.selectDictTypeAll();
//     }

//     /**
//      * 根据字典类型查询字典数据
//      * 
//      * @param dictType 字典类型
//      * @return 字典数据集合信息
//      */
//     @Override
//     public List<SysDictData> selectDictDataByType(String dictType)
//     {
//         List<SysDictData> dictDatas = DictUtils.getDictCache(dictType);
//         if (StringUtils.isNotEmpty(dictDatas))
//         {
//             return dictDatas;
//         }
//         dictDatas = dictDataMapper.selectDictDataByType(dictType);
//         if (StringUtils.isNotEmpty(dictDatas))
//         {
//             DictUtils.setDictCache(dictType, dictDatas);
//             return dictDatas;
//         }
//         return null;
//     }

//     /**
//      * 根据字典类型ID查询信息
//      * 
//      * @param dictId 字典类型ID
//      * @return 字典类型
//      */
//     @Override
//     public SysDictType selectDictTypeById(Long dictId)
//     {
//         return dictTypeMapper.selectDictTypeById(dictId);
//     }

//     /**
//      * 根据字典类型查询信息
//      * 
//      * @param dictType 字典类型
//      * @return 字典类型
//      */
//     @Override
//     public SysDictType selectDictTypeByType(String dictType)
//     {
//         return dictTypeMapper.selectDictTypeByType(dictType);
//     }

//     /**
//      * 批量删除字典类型信息
//      * 
//      * @param dictIds 需要删除的字典ID
//      */
//     @Override
//     public void deleteDictTypeByIds(Long[] dictIds)
//     {
//         for (Long dictId : dictIds)
//         {
//             SysDictType dictType = selectDictTypeById(dictId);
//             if (dictDataMapper.countDictDataByType(dictType.getDictType()) > 0)
//             {
//                 throw new ServiceException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
//             }
//             dictTypeMapper.deleteDictTypeById(dictId);
//             DictUtils.removeDictCache(dictType.getDictType());
//         }
//     }

//     /**
//      * 加载字典缓存数据
//      */
//     @Override
//     public void loadingDictCache()
//     {
//         SysDictData dictData = new SysDictData();
//         dictData.setStatus("0");
//         Map<String, List<SysDictData>> dictDataMap = dictDataMapper.selectDictDataList(dictData).stream().collect(Collectors.groupingBy(SysDictData::getDictType));
//         for (Map.Entry<String, List<SysDictData>> entry : dictDataMap.entrySet())
//         {
//             DictUtils.setDictCache(entry.getKey(), entry.getValue().stream().sorted(Comparator.comparing(SysDictData::getDictSort)).collect(Collectors.toList()));
//         }
//     }

//     /**
//      * 清空字典缓存数据
//      */
//     @Override
//     public void clearDictCache()
//     {
//         DictUtils.clearDictCache();
//     }

//     /**
//      * 重置字典缓存数据
//      */
//     @Override
//     public void resetDictCache()
//     {
//         clearDictCache();
//         loadingDictCache();
//     }

//     /**
//      * 新增保存字典类型信息
//      * 
//      * @param dict 字典类型信息
//      * @return 结果
//      */
//     @Override
//     public int insertDictType(SysDictType dict)
//     {
//         int row = dictTypeMapper.insertDictType(dict);
//         if (row > 0)
//         {
//             DictUtils.setDictCache(dict.getDictType(), null);
//         }
//         return row;
//     }

//     /**
//      * 修改保存字典类型信息
//      * 
//      * @param dict 字典类型信息
//      * @return 结果
//      */
//     @Override
//     @Transactional
//     public int updateDictType(SysDictType dict)
//     {
//         SysDictType oldDict = dictTypeMapper.selectDictTypeById(dict.getDictId());
//         dictDataMapper.updateDictDataType(oldDict.getDictType(), dict.getDictType());
//         int row = dictTypeMapper.updateDictType(dict);
//         if (row > 0)
//         {
//             List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(dict.getDictType());
//             DictUtils.setDictCache(dict.getDictType(), dictDatas);
//         }
//         return row;
//     }

//     /**
//      * 校验字典类型称是否唯一
//      * 
//      * @param dict 字典类型
//      * @return 结果
//      */
//     @Override
//     public boolean checkDictTypeUnique(SysDictType dict)
//     {
//         Long dictId = StringUtils.isNull(dict.getDictId()) ? -1L : dict.getDictId();
//         SysDictType dictType = dictTypeMapper.checkDictTypeUnique(dict.getDictType());
//         if (StringUtils.isNotNull(dictType) && dictType.getDictId().longValue() != dictId.longValue())
//         {
//             return UserConstants.NOT_UNIQUE;
//         }
//         return UserConstants.UNIQUE;
//     }
// }
