package com.dkd.web.controller.monitor;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.system.domain.SysOperLog;
import com.dkd.system.service.ISysOperLogService;

/**
 * 操作日志记录
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理监控模块操作日志相关的HTTP请求
@RestController
// 设置基础请求路径为"/monitor/operlog"
@RequestMapping("/monitor/operlog")
public class SysOperlogController extends BaseController
{
    // 注入操作日志业务层接口
    @Autowired
    private ISysOperLogService operLogService;

    /**
     * 查询操作日志列表接口
     * 需要'monitor:operlog:list'权限
     * 请求路径：GET /monitor/operlog/list
     */
    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysOperLog operLog)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询操作日志数据
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出操作日志数据接口
     * 需要'monitor:operlog:export'权限
     * 请求路径：POST /monitor/operlog/export
     */
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOperLog operLog)
    {
        // 调用服务层方法查询需要导出的操作日志数据
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        // 创建Excel工具类实例
        ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "操作日志");
    }

    /**
     * 删除指定ID的操作日志接口
     * 需要'monitor:operlog:remove'权限
     * 请求路径：DELETE /monitor/operlog/{operIds}
     */
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/{operIds}")
    public AjaxResult remove(@PathVariable Long[] operIds)
    {
        // 调用服务层方法删除指定ID的操作日志
        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    /**
     * 清空所有操作日志接口
     * 需要'monitor:operlog:remove'权限
     * 请求路径：DELETE /monitor/operlog/clean
     */
    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        // 调用服务层方法清空操作日志数据
        operLogService.cleanOperLog();
        // 返回操作成功结果
        return success();
    }
}





// @RestController
// @RequestMapping("/monitor/operlog")
// public class SysOperlogController extends BaseController
// {
//     @Autowired
//     private ISysOperLogService operLogService;

//     @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysOperLog operLog)
//     {
//         startPage();
//         List<SysOperLog> list = operLogService.selectOperLogList(operLog);
//         return getDataTable(list);
//     }

//     @Log(title = "操作日志", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('monitor:operlog:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysOperLog operLog)
//     {
//         List<SysOperLog> list = operLogService.selectOperLogList(operLog);
//         ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
//         util.exportExcel(response, list, "操作日志");
//     }

//     @Log(title = "操作日志", businessType = BusinessType.DELETE)
//     @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
//     @DeleteMapping("/{operIds}")
//     public AjaxResult remove(@PathVariable Long[] operIds)
//     {
//         return toAjax(operLogService.deleteOperLogByIds(operIds));
//     }

//     @Log(title = "操作日志", businessType = BusinessType.CLEAN)
//     @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
//     @DeleteMapping("/clean")
//     public AjaxResult clean()
//     {
//         operLogService.cleanOperLog();
//         return success();
//     }
// }
