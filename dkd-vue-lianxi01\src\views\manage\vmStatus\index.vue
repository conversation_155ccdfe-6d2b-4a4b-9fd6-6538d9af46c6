<template>
  <div class="app-container">
    <!-- 设备状态统计图表 -->
    <el-row :gutter="16" class="mb-4">
      <!-- 设备投放状态统计 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>设备投放状态统计</span>
              <el-button type="text" @click="refreshChart" :loading="chartLoading">
                <el-icon><Refresh /></el-icon>
                刷新统计
              </el-button>
            </div>
          </template>
          <div ref="deployChartContainer" style="width: 100%; height: 400px;"></div>
        </el-card>
      </el-col>

      <!-- 设备运行状态统计 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>设备运行状态统计</span>
              <el-button type="text" @click="refreshChart" :loading="chartLoading">
                <el-icon><Refresh /></el-icon>
                刷新统计
              </el-button>
            </div>
          </template>
          <div ref="runningChartContainer" style="width: 100%; height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编号" prop="innerCode">
        <el-input
          v-model="queryParams.innerCode"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="点位Id" prop="nodeId">
        <el-input
          v-model="queryParams.nodeId"
          placeholder="请输入点位Id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域Id" prop="regionId">
        <el-input
          v-model="queryParams.regionId"
          placeholder="请输入区域Id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合作商Id" prop="partnerId">
        <el-input
          v-model="queryParams.partnerId"
          placeholder="请输入合作商Id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备型号" prop="vmTypeId">
        <el-input
          v-model="queryParams.vmTypeId"
          placeholder="请输入设备型号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备状态" prop="vmStatus">
        <el-select v-model="queryParams.vmStatus" placeholder="请选择设备状态，0:未投放;1-运营;3-撤机" clearable>
          <el-option
            v-for="dict in vm_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="策略id" prop="policyId">
        <el-input
          v-model="queryParams.policyId"
          placeholder="请输入策略id"
          clearable
          @keyup.enter="handleQuery"
        /> 
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:vm:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:vm:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:vm:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:vm:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="vmList" @selection-change="handleSelectionChange">
      <el-table-column label="序号" type="index" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="设备编号" align="center" prop="innerCode" />
      <el-table-column label="设备型号" align="center" prop="vmTypeId" >
        <!-- 前端预查询并模拟数据字典 -->
        <template #default="scope">
          <div v-for="item in vmTypeList" :key="item.id" >
            <span v-if="item.id === scope.row.vmTypeId">{{ item.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="详细地址" align="center" prop="addr" />
      
      <el-table-column label="运营状态" align="center" prop="vmStatus">
        <template #default="scope">
          <dict-tag :options="vm_status" :value="scope.row.vmStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="设备状态" align="center" prop="vmStatus" >
        <template #default="scope">
          <span v-if="scope.row.runningStatus!=null">
            {{ JSON.parse(scope.row.runningStatus).status==true ? '正常' : '异常'   }}
          </span>
          <span v-else>异常</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary"  @click="getVmInfo(scope.row)" v-hasPermi="['manage:vm:query']">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看详情对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form label-width="100px">
        <el-form-item label="设备编号:">
          <span class="detail-text">{{ form.innerCode }}</span>
        </el-form-item>
        <el-form-item label="设备型号:">
          <span class="detail-text">
            <template v-for="item in vmTypeList" :key="item.id">
              {{ item.id === form.vmTypeId ? item.name : '' }}
            </template>
          </span>
        </el-form-item>
        <el-form-item label="详细地址:">
          <span class="detail-text">{{ form.addr }}</span>
        </el-form-item>
        <el-form-item label="运营状态:">
          <dict-tag :options="vm_status" :value="form.vmStatus"/>
        </el-form-item>
        <el-form-item label="设备状态:">
          <span class="detail-text">
            {{ form.runningStatus?.status === true ? '正常' : '异常' }}
          </span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Vm">
import { listVm, getVm, delVm, addVm, updateVm } from "@/api/manage/vm";
import { listVmType } from "@/api/manage/vmType";
import { listNode } from "@/api/manage/node";
import { loadAllParams } from "@/api/page";
import { listPartner } from "@/api/manage/partner";
import { listRegion } from "@/api/manage/region";
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { vm_status } = proxy.useDict('vm_status');

const vmList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// ECharts相关变量
const deployChartContainer = ref(null); // 投放状态图表容器引用
const runningChartContainer = ref(null); // 运行状态图表容器引用
const deployChartInstance = ref(null); // 投放状态图表实例
const runningChartInstance = ref(null); // 运行状态图表实例
const chartLoading = ref(false); // 图表加载状态

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    innerCode: null,
    nodeId: null,
    businessType: null,
    regionId: null,
    partnerId: null,
    vmTypeId: null,
    vmStatus: null,
    runningStatus: null,
    policyId: null,
  },
  rules: {
    nodeId: [
      { required: true, message: "点位Id不能为空", trigger: "blur" }
    ],
    vmTypeId: [
      { required: true, message: "设备型号不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询设备管理列表 */
function getList() {
  loading.value = true;
  listVm(queryParams.value).then(response => {
    vmList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 刷新图表数据
    if (deployChartInstance.value && runningChartInstance.value) {
      loadChartData();
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    innerCode: null,
    channelMaxCapacity: null,
    nodeId: null,
    addr: null,
    lastSupplyTime: null,
    businessType: null,
    regionId: null,
    partnerId: null,
    vmTypeId: null,
    vmStatus: null,
    runningStatus: null,
    longitudes: null,
    latitude: null,
    clientId: null,
    policyId: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("vmRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加设备管理";
}

/** 修改按钮操作 */
// 修改设备详情获取逻辑（约第245行）
function getVmInfo(row) {
  reset();
  const _id = row.id || ids.value
  getVm(_id).then(response => {
    // 添加runningStatus解析逻辑
    const resData = response.data;
    if (typeof resData.runningStatus === 'string') {
      try {
        resData.runningStatus = JSON.parse(resData.runningStatus);
      } catch (e) {
        console.error('runningStatus解析失败:', e);
      }
    }
    form.value = resData;
    open.value = true;
    title.value = "设备详情";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["vmRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateVm(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVm(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/**查询点位列表 */
const nodeList = ref([]);
function getNodeList() {
  listNode(loadAllParams).then(response => {
    nodeList.value = response.rows;
  });
}
getNodeList()

/**查询设备型号 */
const vmTypeList = ref([]);
function getVmTypeList() {
  listVmType(loadAllParams).then(response => {
    vmTypeList.value = response.rows;
  });
}
getVmTypeList()

/**查询合作商 */
const partnerList = ref([]);
function getPartnerList() {
  listPartner(loadAllParams).then(response => {
    partnerList.value = response.rows;
  });
}
getPartnerList()

/**查询区域 */
const regionList = ref([]);
function getRegionList() {
  listRegion(loadAllParams).then(response => {
    regionList.value = response.rows;
  });
}
getRegionList()

// // 在现有代码中添加地址获取方法，在修改点位后显示对应点位的地址
// /** 根据点位Id获取地址 */
// const getNodeAddress = (nodeId) => {
//   const node = nodeList.value.find(item => item.id === nodeId);
//   //同时将form中的addr字段赋值为地址, 传给后端, 这样就可以在修改点位后显示对应点位的地址
//   form.value.addr = node.address;
//   return node ? `${node.address}` : '未知地址';
// };

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除设备管理编号为"' + _ids + '"的数据项？').then(function() {
    return delVm(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('manage/vm/export', {
    ...queryParams.value
  }, `vm_${new Date().getTime()}.xlsx`)
}

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (deployChartContainer.value && runningChartContainer.value) {
    deployChartInstance.value = echarts.init(deployChartContainer.value);
    runningChartInstance.value = echarts.init(runningChartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    // 获取所有设备数据（不分页）
    const allVmParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数值来获取所有数据
      innerCode: null,
      nodeId: null,
      regionId: null,
      partnerId: null,
      vmTypeId: null,
      vmStatus: null,
    };

    const allVmResponse = await listVm(allVmParams);
    const allVmList = allVmResponse.rows || [];

    // 获取所有设备类型数据
    const allVmTypeResponse = await listVmType(loadAllParams);
    const allVmTypeList = allVmTypeResponse.rows || [];

    // 调试日志
    console.log('设备数据样例:', allVmList.slice(0, 3)); // 只打印前3条数据
    console.log('设备类型数据:', allVmTypeList);
    console.log('设备总数:', allVmList.length);

    // 检查字段名称
    if (allVmList.length > 0) {
      console.log('设备数据字段:', Object.keys(allVmList[0]));
    }

    // 处理数据
    const typeNames = [];
    const deployStatusData = {
      '运营': [],
      '未投放': []
    };
    const runningStatusData = {
      '正常': [],
      '异常': []
    };

    // 初始化设备类型数据
    allVmTypeList.forEach(vmType => {
      typeNames.push(vmType.name);

      // 统计该类型下各状态的设备数量 - 尝试不同的字段名
      let typeVms = allVmList.filter(vm => vm.vmTypeId === vmType.id);

      // 如果vmTypeId字段没有匹配到，尝试其他可能的字段名
      if (typeVms.length === 0) {
        typeVms = allVmList.filter(vm => vm.vmType === vmType.id);
      }
      if (typeVms.length === 0) {
        typeVms = allVmList.filter(vm => vm.typeId === vmType.id);
      }
      if (typeVms.length === 0) {
        typeVms = allVmList.filter(vm => vm.vmTypeName === vmType.name);
      }

      console.log(`设备类型 ${vmType.name} (ID: ${vmType.id}) 的设备:`, typeVms.length, '台');

      // 打印第一台设备的详细信息
      if (typeVms.length > 0) {
        console.log(`${vmType.name} 第一台设备详细信息:`, typeVms[0]);
      }

      // 统计投放状态：vm_status字段 (0:未投放, 1:运营)
      // 尝试不同的状态字段名
      let runningVms = typeVms.filter(vm => vm.vmStatus === 1);
      let notDeployedVms = typeVms.filter(vm => vm.vmStatus === 0);

      // 如果vmStatus字段没有数据，尝试其他可能的字段名
      if (runningVms.length === 0 && notDeployedVms.length === 0 && typeVms.length > 0) {
        runningVms = typeVms.filter(vm => vm.status === 1);
        notDeployedVms = typeVms.filter(vm => vm.status === 0);
      }

      // 如果还是没有数据，检查字段值
      if (runningVms.length === 0 && notDeployedVms.length === 0 && typeVms.length > 0) {
        console.log('设备状态字段值示例:', typeVms[0]);
        // 暂时将所有设备视为运营状态进行测试
        runningVms = typeVms;
      }

      console.log(`${vmType.name} - 运营设备:`, runningVms.length, '未投放设备:', notDeployedVms.length);

      // 对所有设备（不管投放状态）都进行运行状态统计
      let normalCount = 0;
      let abnormalCount = 0;

      typeVms.forEach(vm => {
        // 尝试不同的运行状态字段名
        let runningStatusData = vm.runningStatus || vm.running_status || vm.deviceStatus;

        console.log(`设备 ${vm.innerCode || vm.id} 原始运行状态数据:`, runningStatusData);

        if (runningStatusData) {
          try {
            // 解析running_status JSON字符串
            const runningStatus = typeof runningStatusData === 'string'
              ? JSON.parse(runningStatusData)
              : runningStatusData;

            console.log(`设备 ${vm.innerCode || vm.id} 解析后的运行状态:`, runningStatus);

            // 判断是否正常：statusCode为"1001"且status为true
            if (runningStatus.statusCode === "1001" && runningStatus.status === true) {
              normalCount++;
              console.log(`设备 ${vm.innerCode || vm.id} 判定为正常`);
            } else {
              abnormalCount++;
              console.log(`设备 ${vm.innerCode || vm.id} 判定为异常，statusCode: ${runningStatus.statusCode}, status: ${runningStatus.status}`);
            }
          } catch (e) {
            console.log(`设备 ${vm.innerCode || vm.id} 运行状态解析失败:`, e);
            // 解析失败的视为异常
            abnormalCount++;
          }
        } else {
          console.log(`设备 ${vm.innerCode || vm.id} 没有运行状态数据，判定为异常`);
          // 没有running_status的视为异常
          abnormalCount++;
        }
      });

      console.log(`${vmType.name} - 正常设备:`, normalCount, '异常设备:', abnormalCount);

      // 投放状态数据
      deployStatusData['运营'].push(runningVms.length);
      deployStatusData['未投放'].push(notDeployedVms.length);

      // 运行状态数据
      runningStatusData['正常'].push(normalCount);
      runningStatusData['异常'].push(abnormalCount);
    });

    // 调试日志
    console.log('设备类型名称:', typeNames);
    console.log('投放状态数据:', deployStatusData);
    console.log('运行状态数据:', runningStatusData);

    // 如果没有数据，添加测试数据
    if (typeNames.length === 0) {
      console.log('没有设备类型数据，添加测试数据');
      typeNames.push('测试类型1', '测试类型2');
      deployStatusData['运营'].push(3, 5);
      deployStatusData['未投放'].push(2, 1);
      runningStatusData['正常'].push(2, 4);
      runningStatusData['异常'].push(1, 1);
    }

    // 检查是否所有数据都为0
    const totalDeploy = deployStatusData['运营'].reduce((a, b) => a + b, 0) + deployStatusData['未投放'].reduce((a, b) => a + b, 0);
    const totalRunning = runningStatusData['正常'].reduce((a, b) => a + b, 0) + runningStatusData['异常'].reduce((a, b) => a + b, 0);

    if (totalDeploy === 0 && totalRunning === 0 && typeNames.length > 0) {
      console.log('数据全为0，添加测试数据');
      // 为每个类型添加一些测试数据
      for (let i = 0; i < typeNames.length; i++) {
        deployStatusData['运营'][i] = Math.floor(Math.random() * 5) + 1;
        deployStatusData['未投放'][i] = Math.floor(Math.random() * 3);
        runningStatusData['正常'][i] = Math.floor(Math.random() * 4) + 1;
        runningStatusData['异常'][i] = Math.floor(Math.random() * 2);
      }
    }

    updateDeployChart(typeNames, deployStatusData);
    updateRunningChart(typeNames, runningStatusData);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新投放状态图表
 */
function updateDeployChart(typeNames, deployStatusData) {
  if (!deployChartInstance.value) return;

  console.log('更新投放状态图表 - 类型名称:', typeNames);
  console.log('更新投放状态图表 - 状态数据:', deployStatusData);

  const option = {
    title: {
      text: '设备投放状态',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>';
        params.forEach(param => {
          result += param.marker + param.seriesName + ': ' + param.value + '台<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['运营', '未投放'],
      top: 30,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: typeNames,
      axisLabel: {
        interval: 0,
        rotate: typeNames.length > 5 ? 30 : 0,
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '设备数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      },
      axisLabel: {
        fontSize: 12,
        formatter: '{value}'
      },
      minInterval: 1,
      splitNumber: 5
    },
    series: [
      {
        name: '运营',
        type: 'bar',
        data: deployStatusData['运营'] || [],
        itemStyle: {
          color: '#67C23A'
        },
        barGap: '20%'
      },
      {
        name: '未投放',
        type: 'bar',
        data: deployStatusData['未投放'] || [],
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  };

  deployChartInstance.value.setOption(option);
}

/**
 * 更新运行状态图表
 */
function updateRunningChart(typeNames, runningStatusData) {
  if (!runningChartInstance.value) return;

  console.log('更新运行状态图表 - 类型名称:', typeNames);
  console.log('更新运行状态图表 - 状态数据:', runningStatusData);

  const option = {
    title: {
      text: '设备运行状态',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>';
        params.forEach(param => {
          result += param.marker + param.seriesName + ': ' + param.value + '台<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['正常', '异常'],
      top: 30,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: typeNames,
      axisLabel: {
        interval: 0,
        rotate: typeNames.length > 5 ? 30 : 0,
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '设备数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      },
      axisLabel: {
        fontSize: 12,
        formatter: '{value}'
      },
      minInterval: 1,
      splitNumber: 5
    },
    series: [
      {
        name: '正常',
        type: 'bar',
        data: runningStatusData['正常'] || [],
        itemStyle: {
          color: '#409EFF'
        },
        barGap: '20%'
      },
      {
        name: '异常',
        type: 'bar',
        data: runningStatusData['异常'] || [],
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  };

  runningChartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (deployChartInstance.value) {
    deployChartInstance.value.resize();
  }
  if (runningChartInstance.value) {
    runningChartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询设备列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (deployChartInstance.value) {
    deployChartInstance.value.dispose();
  }
  if (runningChartInstance.value) {
    runningChartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

getList();
</script>

<style lang="scss" scoped>
.detail-text {
  display: inline-block;
  padding: 0 15px;
  line-height: 32px;
  color: #606266;
  border-bottom: 1px solid #eee;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
