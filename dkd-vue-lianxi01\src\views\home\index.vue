<template>
  <!-- 智能售货机管理系统欢迎界面 -->
  <div class="welcome-container">
    <!-- 主横幅区域 -->
    <div class="hero-section">
      <!-- 背景渐变 -->
      <div class="hero-background">
        <div class="gradient-overlay"></div>
        <div class="floating-elements">
          <div class="floating-circle circle-1"></div>
          <div class="floating-circle circle-2"></div>
          <div class="floating-circle circle-3"></div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="hero-content">
        <div class="hero-left">
          <div class="welcome-badge">
            <span class="badge-text">{{ onlineUsers }}人正在使用</span>
          </div>

          <h1 class="hero-title">
            {{ welcomeMessage }}
            <br>
            <span class="highlight" >欢迎使用智能售货机管理系统</span>
          </h1>

          <p class="hero-subtitle">
            专业的智能售货机运营管理平台，让每一台设备都发挥最大价值
          </p>

          <div class="user-info">
            <el-avatar :size="60" :src="userStore.avatar" class="user-avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-details">
              <h3>{{ userStore.name || '管理员' }}</h3>
              <p>{{ currentTime }}</p>
            </div>
          </div>
        </div>

        <!-- 智能售货机3D展示 -->
        <div class="hero-right">
          <div class="vending-machine-showcase">
            <!-- 主售货机 -->
            <div class="main-machine">
              <div class="machine-frame">
                <div class="machine-header">
                  <div class="brand-logo">
                    <el-icon><Shop /></el-icon>
                    <span>智能售货机</span>
                  </div>
                  <div class="status-light online"></div>
                </div>

                <div class="machine-screen">
                  <div class="screen-glow"></div>
                  <div class="screen-content">
                    <div class="welcome-text">欢迎使用</div>
                    <div class="system-name">智能售货机</div>
                    <div class="status-bar">
                      <span class="status-dot"></span>
                      <span>系统正常运行</span>
                    </div>
                  </div>
                </div>

                <div class="product-display">
                  <div class="product-grid">
                    <div class="product-item" v-for="i in 12" :key="i">
                      <div class="product-icon">
                        <el-icon v-if="i % 3 === 0"><Coffee /></el-icon>
                        <el-icon v-else-if="i % 3 === 1"><IceCreamRound /></el-icon>
                        <el-icon v-else><Coin /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="machine-controls">
                  <div class="payment-area">
                    <div class="coin-slot">
                      <el-icon><Coin /></el-icon>
                    </div>
                    <div class="card-reader"></div>
                  </div>
                  <div class="pickup-door">
                    <div class="door-handle"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 装饰性小售货机 -->
            <div class="mini-machines">
              <div class="mini-machine mini-1">
                <div class="mini-screen"></div>
                <div class="mini-products"></div>
              </div>
              <div class="mini-machine mini-2">
                <div class="mini-screen"></div>
                <div class="mini-products"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计数据展示区域 -->
    <!-- <div class="stats-section">
      <div class="stats-container">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ stats.totalDevices }}</div>
            <div class="stat-label">设备总数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.onlineDevices }}</div>
            <div class="stat-label">在线设备</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.totalSales }}</div>
            <div class="stat-label">今日销售</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.totalRevenue }}</div>
            <div class="stat-label">总收益(元)</div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 特色功能展示区域 -->
    <div class="features-section">
      <div class="features-container">
        <h2 class="section-title">核心功能</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <h3>智能监控</h3>
            <p>实时监控设备状态，及时发现异常情况，确保设备正常运营</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><PieChart /></el-icon>
            </div>
            <h3>数据分析</h3>
            <p>深度分析销售数据，洞察消费趋势，优化商品配置策略</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Location /></el-icon>
            </div>
            <h3>点位管理</h3>
            <p>科学规划设备点位，最大化覆盖目标用户群体</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Lock /></el-icon>
            </div>
            <h3>安全保障</h3>
            <p>多重安全防护机制，保障设备和资金安全</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Home">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'
import {
  User, Shop, Coffee, IceCreamRound, Coin, Monitor,
  PieChart, Location, Lock
} from '@element-plus/icons-vue'

// 获取路由实例和用户状态
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const currentTime = ref('')
const timeInterval = ref(null)
const onlineUsers = ref(9626) // 模拟在线用户数

// 模拟统计数据
const stats = ref({
  totalDevices: 1526,
  onlineDevices: 1432,
  totalSales: 200,
  totalRevenue: 15680
})

// 计算属性：个性化欢迎信息
const welcomeMessage = computed(() => {
  const hour = new Date().getHours()
  const name = userStore.name || '管理员'

  if (hour < 6) {
    return `夜深了，${name}！`
  } else if (hour < 12) {
    return `早上好，${name}！`
  } else if (hour < 18) {
    return `下午好，${name}！`
  } else {
    return `晚上好，${name}！`
  }
})

// 更新当前时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 生命周期钩子
onMounted(() => {
  updateTime()
  timeInterval.value = setInterval(updateTime, 1000)

  // 模拟在线用户数变化
  setInterval(() => {
    onlineUsers.value = Math.floor(Math.random() * 100) + 9500
  }, 5000)
})

onUnmounted(() => {
  if (timeInterval.value) {
    clearInterval(timeInterval.value)
  }
})
</script>

<style scoped lang="scss">
.welcome-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0066CC 0%, #4A90E2 100%);
  overflow-x: hidden;
}

// 主横幅区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 0 40px;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .gradient-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(0, 102, 204, 0.9) 0%,
        rgba(74, 144, 226, 0.9) 100%);
    }

    .floating-elements {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .floating-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;

        &.circle-1 {
          width: 200px;
          height: 200px;
          top: 10%;
          left: 10%;
          animation-delay: 0s;
        }

        &.circle-2 {
          width: 150px;
          height: 150px;
          top: 60%;
          right: 15%;
          animation-delay: 2s;
        }

        &.circle-3 {
          width: 100px;
          height: 100px;
          bottom: 20%;
          left: 20%;
          animation-delay: 4s;
        }
      }
    }
  }

  .hero-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    gap: 60px;
  }

  .hero-left {
    flex: 1;
    color: white;

    .welcome-badge {
      display: inline-block;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      padding: 8px 20px;
      border-radius: 25px;
      margin-bottom: 30px;
      border: 1px solid rgba(255, 255, 255, 0.3);

      .badge-text {
        font-size: 14px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .hero-title {
      font-size: 3.5rem;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 20px;

      .highlight {
        background: linear-gradient(45deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        white-space: nowrap;
        display: inline-block;
        min-width: 600px;
      }
    }

    .hero-subtitle {
      font-size: 1.2rem;
      line-height: 1.6;
      margin-bottom: 40px;
      color: rgba(255, 255, 255, 0.8);
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 20px;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 20px;
      border-radius: 15px;
      border: 1px solid rgba(255, 255, 255, 0.2);

      .user-avatar {
        border: 3px solid rgba(255, 255, 255, 0.3);
      }

      .user-details {
        h3 {
          margin: 0 0 5px 0;
          font-size: 1.2rem;
          font-weight: 600;
        }

        p {
          margin: 0;
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }

  .hero-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 售货机展示区域
.vending-machine-showcase {
  position: relative;

  .main-machine {
    .machine-frame {
      width: 300px;
      height: 500px;
      background: linear-gradient(145deg, #2c3e50, #34495e);
      border-radius: 20px;
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;

      .machine-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: linear-gradient(90deg, #3498db, #2980b9);

        .brand-logo {
          display: flex;
          align-items: center;
          gap: 8px;
          color: white;
          font-weight: bold;
          font-size: 1.1rem;

          .el-icon {
            font-size: 1.5rem;
          }
        }

        .status-light {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #27ae60;
          box-shadow: 0 0 10px #27ae60;
          animation: pulse 2s infinite;
        }
      }

      .machine-screen {
        margin: 20px;
        height: 120px;
        background: #1a1a1a;
        border-radius: 10px;
        position: relative;
        overflow: hidden;

        .screen-glow {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg,
            rgba(0, 255, 255, 0.1),
            rgba(255, 0, 255, 0.1));
          animation: screenGlow 3s ease-in-out infinite alternate;
        }

        .screen-content {
          position: relative;
          z-index: 2;
          padding: 20px;
          text-align: center;
          color: #00ff88;

          .welcome-text {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 8px;
          }

          .system-name {
            font-size: 0.9rem;
            margin-bottom: 15px;
            color: #88ff88;
          }

          .status-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.8rem;

            .status-dot {
              width: 6px;
              height: 6px;
              background: #00ff88;
              border-radius: 50%;
              animation: blink 1.5s infinite;
            }
          }
        }
      }

      .product-display {
        margin: 20px;

        .product-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 8px;

          .product-item {
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              transform: scale(1.05);
            }

            .product-icon {
              .el-icon {
                font-size: 1.5rem;
                color: #ffd700;
              }
            }
          }
        }
      }

      .machine-controls {
        position: absolute;
        bottom: 20px;
        left: 20px;
        right: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .payment-area {
          display: flex;
          gap: 10px;

          .coin-slot {
            width: 40px;
            height: 30px;
            background: #34495e;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px inset #2c3e50;

            .el-icon {
              color: #f39c12;
              font-size: 1.2rem;
            }
          }

          .card-reader {
            width: 60px;
            height: 30px;
            background: #2c3e50;
            border-radius: 5px;
            border: 1px solid #34495e;
          }
        }

        .pickup-door {
          width: 80px;
          height: 40px;
          background: #2c3e50;
          border-radius: 5px;
          border: 2px inset #34495e;
          position: relative;

          .door-handle {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 20px;
            background: #95a5a6;
            border-radius: 2px;
          }
        }
      }
    }
  }

  .mini-machines {
    position: absolute;

    .mini-machine {
      width: 80px;
      height: 120px;
      background: linear-gradient(145deg, #34495e, #2c3e50);
      border-radius: 8px;
      position: absolute;

      &.mini-1 {
        top: -20px;
        left: -60px;
        transform: rotate(-15deg);
        opacity: 0.7;
      }

      &.mini-2 {
        bottom: -30px;
        right: -70px;
        transform: rotate(20deg);
        opacity: 0.6;
      }

      .mini-screen {
        margin: 8px;
        height: 30px;
        background: #1a1a1a;
        border-radius: 4px;
      }

      .mini-products {
        margin: 8px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }
    }
  }
}

// 统计数据区域
.stats-section {
  background: white;
  padding: 60px 40px;

  .stats-container {
    max-width: 1200px;
    margin: 0 auto;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 40px;

      .stat-item {
        text-align: center;
        padding: 30px 20px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
          font-size: 2.5rem;
          font-weight: 700;
          color: #667eea;
          margin-bottom: 10px;
        }

        .stat-label {
          font-size: 1rem;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }
  }
}

// 特色功能区域
.features-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 80px 40px;

  .features-container {
    max-width: 1200px;
    margin: 0 auto;

    .section-title {
      text-align: center;
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 60px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background: linear-gradient(90deg, #0066CC, #4A90E2);
        border-radius: 2px;
      }
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 30px;

      .feature-card {
        background: white;
        padding: 40px 30px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
          transform: translateY(-10px);
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);

          .feature-icon {
            transform: scale(1.1);
          }
        }

        .feature-icon {
          width: 80px;
          height: 80px;
          margin: 0 auto 25px;
          background: linear-gradient(135deg, #0066CC, #4A90E2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          .el-icon {
            font-size: 2rem;
            color: white;
          }
        }

        h3 {
          font-size: 1.4rem;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 15px;
        }

        p {
          color: #6c757d;
          line-height: 1.6;
          font-size: 0.95rem;
        }
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes screenGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.7;
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-left .hero-title {
    font-size: 2.8rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 0 20px;
  }

  .hero-left .hero-title {
    font-size: 2.2rem;
  }

  .stats-section,
  .features-section {
    padding: 40px 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr !important;
    gap: 20px;
  }

  .features-grid {
    grid-template-columns: 1fr !important;
  }

  .main-machine .machine-frame {
    width: 250px;
    height: 400px;
  }
}
</style>
