package com.dkd.system.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.common.constant.Constants;
import com.dkd.common.constant.UserConstants;
import com.dkd.common.core.domain.TreeSelect;
import com.dkd.common.core.domain.entity.SysMenu;
import com.dkd.common.core.domain.entity.SysRole;
import com.dkd.common.core.domain.entity.SysUser;
import com.dkd.common.utils.SecurityUtils;
import com.dkd.common.utils.StringUtils;
import com.dkd.system.domain.vo.MetaVo;
import com.dkd.system.domain.vo.RouterVo;
import com.dkd.system.mapper.SysMenuMapper;
import com.dkd.system.mapper.SysRoleMapper;
import com.dkd.system.mapper.SysRoleMenuMapper;
import com.dkd.system.service.ISysMenuService;

/**
 * 菜单 业务层处理
 * 
 * <AUTHOR>
 */
// 定义菜单服务实现类，处理与菜单相关的业务逻辑
@Service
public class SysMenuServiceImpl implements ISysMenuService
{
    // 权限字符串模板，用于生成权限表达式
    public static final String PREMISSION_STRING = "perms[\"{0}\"]";

    // 注入菜单数据库操作Mapper
    @Autowired
    private SysMenuMapper menuMapper;

    // 注入角色数据库操作Mapper，用于角色与菜单关联
    @Autowired
    private SysRoleMapper roleMapper;

    // 注入角色菜单关联数据库操作Mapper
    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    /**
     * 根据用户ID查询系统菜单列表
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(Long userId)
    {
        // 调用带查询条件的重载方法，传入空的SysMenu对象和用户ID
        return selectMenuList(new SysMenu(), userId);
    }

    /**
     * 查询系统菜单列表（支持条件筛选）
     * 
     * @param menu 查询条件封装对象
     * @param userId 用户ID
     * @return 符合条件的菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(SysMenu menu, Long userId)
    {
        List<SysMenu> menuList = null;
        // 如果是管理员，查询所有菜单
        if (SysUser.isAdmin(userId))
        {
            menuList = menuMapper.selectMenuList(menu);
        }
        else
        {
            // 否则只查询该用户有权限的菜单
            menu.getParams().put("userId", userId);
            menuList = menuMapper.selectMenuListByUserId(menu);
        }
        return menuList;
    }

    /**
     * 根据用户ID查询其拥有的权限标识集合
     * 
     * @param userId 用户ID
     * @return 权限标识集合
     */
    @Override
    public Set<String> selectMenuPermsByUserId(Long userId)
    {
        // 从数据库查询权限字符串列表
        List<String> perms = menuMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms)
        {
            if (StringUtils.isNotEmpty(perm))
            {
                // 将逗号分隔的权限字符串拆分成独立权限并加入集合
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据角色ID查询该角色拥有的权限标识集合
     * 
     * @param roleId 角色ID
     * @return 权限标识集合
     */
    @Override
    public Set<String> selectMenuPermsByRoleId(Long roleId)
    {
        // 从数据库查询权限字符串列表
        List<String> perms = menuMapper.selectMenuPermsByRoleId(roleId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms)
        {
            if (StringUtils.isNotEmpty(perm))
            {
                // 将逗号分隔的权限字符串拆分成独立权限并加入集合
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单树结构
     * 
     * @param userId 用户ID
     * @return 树形结构的菜单列表
     */
    @Override
    public List<SysMenu> selectMenuTreeByUserId(Long userId)
    {
        List<SysMenu> menus = null;
        // 管理员查询全部菜单
        if (SecurityUtils.isAdmin(userId))
        {
            menus = menuMapper.selectMenuTreeAll();
        }
        else
        {
            // 普通用户只查询自己有权访问的菜单
            menus = menuMapper.selectMenuTreeByUserId(userId);
        }
        // 构建父子关系的树形结构
        return getChildPerms(menus, 0);
    }

    /**
     * 根据角色ID查询关联的菜单列表
     * 
     * @param roleId 角色ID
     * @return 关联的菜单ID列表
     */
    @Override
    public List<Long> selectMenuListByRoleId(Long roleId)
    {
        // 查询角色详情
        SysRole role = roleMapper.selectRoleById(roleId);
        // 查询角色关联的菜单列表
        return menuMapper.selectMenuListByRoleId(roleId, role.isMenuCheckStrictly());
    }

    /**
     * 构建前端路由所需要的菜单结构
     * 
     * @param menus 菜单列表数据
     * @return 前端路由格式的菜单列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenu> menus)
    {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenu menu : menus)
        {
            RouterVo router = new RouterVo();
            // 设置是否隐藏菜单项
            router.setHidden("1".equals(menu.getVisible()));
            // 获取路由名称
            router.setName(getRouteName(menu));
            // 获取路由路径
            router.setPath(getRouterPath(menu));
            // 获取组件信息
            router.setComponent(getComponent(menu));
            // 设置参数查询条件
            router.setQuery(menu.getQuery());
            // 设置元信息（标题、图标、是否缓存等）
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
            
            // 处理子菜单
            List<SysMenu> cMenus = menu.getChildren();
            if (StringUtils.isNotEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType()))
            {
                // 目录类型节点需要设置alwaysShow为true
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                // 递归构建子菜单
                router.setChildren(buildMenus(cMenus));
            }
            // 处理一级菜单（类型为菜单）
            else if (isMenuFrame(menu))
            {
                router.setMeta(null);  // 一级菜单不显示meta信息
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                // 子路由使用菜单本身的path
                children.setPath(menu.getPath());
                // 组件路径取自菜单配置
                children.setComponent(menu.getComponent());
                // 路由名称为首字母大写的path
                children.setName(StringUtils.capitalize(menu.getPath()));
                // 设置子路由的meta信息
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
                // 设置查询参数
                children.setQuery(menu.getQuery());
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            // 处理外链菜单
            else if (menu.getParentId().intValue() == 0 && isInnerLink(menu))
            {
                // 设置meta信息
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                // 外链根路径为/
                router.setPath("/");
                
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                // 替换内链地址中的特殊字符
                String routerPath = innerLinkReplaceEach(menu.getPath());
                // 子路由路径为处理后的路径
                children.setPath(routerPath);
                // 固定使用INNER_LINK组件
                children.setComponent(UserConstants.INNER_LINK);
                // 名称首字母大写
                children.setName(StringUtils.capitalize(routerPath));
                // 设置meta信息
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端展示所需的树形菜单结构
     * 
     * @param menus 扁平化菜单列表
     * @return 树形结构菜单列表
     */
    @Override
    public List<SysMenu> buildMenuTree(List<SysMenu> menus)
    {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        // 提取所有菜单ID到临时列表
        List<Long> tempList = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        
        for (Iterator<SysMenu> iterator = menus.iterator(); iterator.hasNext();)
        {
            SysMenu menu = (SysMenu) iterator.next();
            // 查找顶级节点
            if (!tempList.contains(menu.getParentId()))
            {
                recursionFn(menus, menu);  // 递归构建子树
                returnList.add(menu);      // 添加顶级节点
            }
        }
        
        // 如果没有找到顶级节点，则返回原始列表
        if (returnList.isEmpty())
        {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端下拉选择框使用的树形结构
     * 
     * @param menus 扁平化菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus)
    {
        // 先构建树形结构
        List<SysMenu> menuTrees = buildMenuTree(menus);
        // 转换为下拉选择专用的TreeSelect格式
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据菜单ID查询详细信息
     * 
     * @param menuId 菜单ID
     * @return 菜单实体对象
     */
    @Override
    public SysMenu selectMenuById(Long menuId)
    {
        return menuMapper.selectMenuById(menuId);
    }

    /**
     * 判断指定菜单是否存在子节点
     * 
     * @param menuId 菜单ID
     * @return true表示存在子节点，false表示不存在
     */
    @Override
    public boolean hasChildByMenuId(Long menuId)
    {
        int result = menuMapper.hasChildByMenuId(menuId);
        return result > 0;
    }

    /**
     * 查询菜单被角色引用的次数
     * 
     * @param menuId 菜单ID
     * @return true表示至少被一个角色引用，false表示未被引用
     */
    @Override
    public boolean checkMenuExistRole(Long menuId)
    {
        int result = roleMenuMapper.checkMenuExistRole(menuId);
        return result > 0;
    }

    /**
     * 新增菜单信息
     * 
     * @param menu 待新增的菜单对象
     * @return 插入影响行数
     */
    @Override
    public int insertMenu(SysMenu menu)
    {
        return menuMapper.insertMenu(menu);
    }

    /**
     * 修改菜单信息
     * 
     * @param menu 待修改的菜单对象
     * @return 修改影响行数
     */
    @Override
    public int updateMenu(SysMenu menu)
    {
        return menuMapper.updateMenu(menu);
    }

    /**
     * 删除指定ID的菜单信息
     * 
     * @param menuId 需要删除的菜单ID
     * @return 删除影响行数
     */
    @Override
    public int deleteMenuById(Long menuId)
    {
        return menuMapper.deleteMenuById(menuId);
    }

    /**
     * 校验菜单名称在同级中是否唯一
     * 
     * @param menu 待校验的菜单对象
     * @return true表示唯一，false表示已存在相同名称
     */
    @Override
    public boolean checkMenuNameUnique(SysMenu menu)
    {
        // 获取当前菜单ID，默认值为-1L
        Long menuId = StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();
        // 查询相同父菜单下的同名菜单
        SysMenu info = menuMapper.checkMenuNameUnique(menu.getMenuName(), menu.getParentId());
        // 存在且不是当前记录，说明不唯一
        if (StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 获取菜单对应的路由名称
     * 
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenu menu)
    {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 一级目录且非外链时，路由名称为空
        if (isMenuFrame(menu))
        {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取菜单对应的路由地址
     * 
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu)
    {
        String routerPath = menu.getPath();
        // 内链且非一级目录时，替换特殊字符
        if (menu.getParentId().intValue() != 0 && isInnerLink(menu))
        {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 一级目录且非外链时，添加前置斜杠
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType())
                && UserConstants.NO_FRAME.equals(menu.getIsFrame()))
        {
            routerPath = "/" + menu.getPath();
        }
        // 一级菜单且非外链时，固定返回根路径
        else if (isMenuFrame(menu))
        {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取菜单对应的组件信息
     * 
     * @param menu 菜单信息
     * @return 组件路径
     */
    public String getComponent(SysMenu menu)
    {
        // 默认布局组件
        String component = UserConstants.LAYOUT;
        // 自定义组件且非一级菜单时使用自定义组件
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu))
        {
            component = menu.getComponent();
        }
        // 无组件且非一级目录且为内链时，使用内链组件
        else if (StringUtils.isEmpty(menu.getComponent()) && menu.getParentId().intValue() != 0 && isInnerLink(menu))
        {
            component = UserConstants.INNER_LINK;
        }
        // 无组件且为parent_view组件时，使用PARENT_VIEW组件
        else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu))
        {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 判断是否为菜单内部跳转
     * 
     * @param menu 菜单信息
     * @return true表示是一级菜单且非外链
     */
    public boolean isMenuFrame(SysMenu menu)
    {
        return menu.getParentId().intValue() == 0 
               && UserConstants.TYPE_MENU.equals(menu.getMenuType())
               && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 判断是否为内链组件
     * 
     * @param menu 菜单信息
     * @return true表示是内链且路径包含http协议
     */
    public boolean isInnerLink(SysMenu menu)
    {
        return menu.getIsFrame().equals(UserConstants.NO_FRAME) 
               && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 判断是否为父视图组件
     * 
     * @param menu 菜单信息
     * @return true表示是非一级目录且类型为目录
     */
    public boolean isParentView(SysMenu menu)
    {
        return menu.getParentId().intValue() != 0 
               && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 根据父节点ID获取所有直接子节点
     * 
     * @param list 全量菜单列表
     * @param parentId 父节点ID
     * @return 包含父子关系的菜单列表
     */
    public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId)
    {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        for (Iterator<SysMenu> iterator = list.iterator(); iterator.hasNext();)
        {
            SysMenu t = (SysMenu) iterator.next();
            // 查找指定父节点的所有直接子节点
            if (t.getParentId() == parentId)
            {
                recursionFn(list, t);   // 递归构建子树
                returnList.add(t);       // 添加匹配的节点
            }
        }
        return returnList;
    }

    /**
     * 递归构造树形菜单结构
     * 
     * @param list 全量菜单列表
     * @param t 当前处理的菜单节点
     */
    private void recursionFn(List<SysMenu> list, SysMenu t)
    {
        // 获取当前节点的直接子节点
        List<SysMenu> childList = getChildList(list, t);
        t.setChildren(childList);  // 设置子节点
        
        // 对每个子节点递归处理
        for (SysMenu tChild : childList)
        {
            if (hasChild(list, tChild))  // 判断是否有下一级子节点
            {
                recursionFn(list, tChild);  // 继续递归处理
            }
        }
    }

    /**
     * 获取当前菜单的直接子菜单列表
     * 
     * @param list 全量菜单列表
     * @param t 当前菜单节点
     * @return 直接子菜单列表
     */
    private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t)
    {
        List<SysMenu> tlist = new ArrayList<SysMenu>();
        Iterator<SysMenu> it = list.iterator();
        while (it.hasNext())
        {
            SysMenu n = (SysMenu) it.next();
            // 判断是否是当前菜单的直接子节点
            if (n.getParentId().longValue() == t.getMenuId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断菜单是否有子节点
     * 
     * @param list 全量菜单列表
     * @param t 当前菜单节点
     * @return true表示有子节点，false表示没有
     */
    private boolean hasChild(List<SysMenu> list, SysMenu t)
    {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 内链域名特殊字符替换
     * 将HTTP:// 或 HTTPS:// 开头的URL转换为前端可识别的路径
     * 
     * @param path 原始路径
     * @return 替换后的路径
     */
    public String innerLinkReplaceEach(String path)
    {
        // 替换HTTP、HTTPS、WWW以及点号冒号等特殊字符
        return StringUtils.replaceEach(path, 
            new String[] { Constants.HTTP, Constants.HTTPS, Constants.WWW, ".", ":" },
            new String[] { "", "", "", "/", "/" });
    }
}








// @Service
// public class SysMenuServiceImpl implements ISysMenuService
// {
//     // 权限字符串常量模板
//     public static final String PREMISSION_STRING = "perms[\"{0}\"]";

//     // 注入菜单数据库操作Mapper
//     @Autowired
//     private SysMenuMapper menuMapper;

//     // 注入角色数据库操作Mapper，用于角色与菜单关联
//     @Autowired
//     private SysRoleMapper roleMapper;

//     // 注入角色菜单关联数据库操作Mapper
//     @Autowired
//     private SysRoleMenuMapper roleMenuMapper;

//     /**
//      * 根据用户查询系统菜单列表
//      * 
//      * @param userId 用户ID
//      * @return 菜单列表
//      */
//     @Override
//     public List<SysMenu> selectMenuList(Long userId)
//     {
//         return selectMenuList(new SysMenu(), userId);
//     }

//     /**
//      * 查询系统菜单列表
//      * 
//      * @param menu 菜单信息
//      * @return 菜单列表
//      */
//     @Override
//     public List<SysMenu> selectMenuList(SysMenu menu, Long userId)
//     {
//         List<SysMenu> menuList = null;
//         // 管理员显示所有菜单信息
//         if (SysUser.isAdmin(userId))
//         {
//             menuList = menuMapper.selectMenuList(menu);
//         }
//         else
//         {
//             menu.getParams().put("userId", userId);
//             menuList = menuMapper.selectMenuListByUserId(menu);
//         }
//         return menuList;
//     }

//     /**
//      * 根据用户ID查询权限
//      * 
//      * @param userId 用户ID
//      * @return 权限列表
//      */
//     @Override
//     public Set<String> selectMenuPermsByUserId(Long userId)
//     {
//         List<String> perms = menuMapper.selectMenuPermsByUserId(userId);
//         Set<String> permsSet = new HashSet<>();
//         for (String perm : perms)
//         {
//             if (StringUtils.isNotEmpty(perm))
//             {
//                 permsSet.addAll(Arrays.asList(perm.trim().split(",")));
//             }
//         }
//         return permsSet;
//     }

//     /**
//      * 根据角色ID查询权限
//      * 
//      * @param roleId 角色ID
//      * @return 权限列表
//      */
//     @Override
//     public Set<String> selectMenuPermsByRoleId(Long roleId)
//     {
//         List<String> perms = menuMapper.selectMenuPermsByRoleId(roleId);
//         Set<String> permsSet = new HashSet<>();
//         for (String perm : perms)
//         {
//             if (StringUtils.isNotEmpty(perm))
//             {
//                 permsSet.addAll(Arrays.asList(perm.trim().split(",")));
//             }
//         }
//         return permsSet;
//     }

//     /**
//      * 根据用户ID查询菜单
//      * 
//      * @param userId 用户名称
//      * @return 菜单列表
//      */
//     @Override
//     public List<SysMenu> selectMenuTreeByUserId(Long userId)
//     {
//         List<SysMenu> menus = null;
//         if (SecurityUtils.isAdmin(userId))
//         {
//             menus = menuMapper.selectMenuTreeAll();
//         }
//         else
//         {
//             menus = menuMapper.selectMenuTreeByUserId(userId);
//         }
//         return getChildPerms(menus, 0);
//     }

//     /**
//      * 根据角色ID查询菜单树信息
//      * 
//      * @param roleId 角色ID
//      * @return 选中菜单列表
//      */
//     @Override
//     public List<Long> selectMenuListByRoleId(Long roleId)
//     {
//         SysRole role = roleMapper.selectRoleById(roleId);
//         return menuMapper.selectMenuListByRoleId(roleId, role.isMenuCheckStrictly());
//     }

//     /**
//      * 构建前端路由所需要的菜单
//      * 
//      * @param menus 菜单列表
//      * @return 路由列表
//      */
//     @Override
//     public List<RouterVo> buildMenus(List<SysMenu> menus)
//     {
//         List<RouterVo> routers = new LinkedList<RouterVo>();
//         for (SysMenu menu : menus)
//         {
//             RouterVo router = new RouterVo();
//             router.setHidden("1".equals(menu.getVisible()));
//             router.setName(getRouteName(menu));
//             router.setPath(getRouterPath(menu));
//             router.setComponent(getComponent(menu));
//             router.setQuery(menu.getQuery());
//             router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
//             List<SysMenu> cMenus = menu.getChildren();
//             if (StringUtils.isNotEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType()))
//             {
//                 router.setAlwaysShow(true);
//                 router.setRedirect("noRedirect");
//                 router.setChildren(buildMenus(cMenus));
//             }
//             else if (isMenuFrame(menu))
//             {
//                 router.setMeta(null);
//                 List<RouterVo> childrenList = new ArrayList<RouterVo>();
//                 RouterVo children = new RouterVo();
//                 children.setPath(menu.getPath());
//                 children.setComponent(menu.getComponent());
//                 children.setName(StringUtils.capitalize(menu.getPath()));
//                 children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
//                 children.setQuery(menu.getQuery());
//                 childrenList.add(children);
//                 router.setChildren(childrenList);
//             }
//             else if (menu.getParentId().intValue() == 0 && isInnerLink(menu))
//             {
//                 router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
//                 router.setPath("/");
//                 List<RouterVo> childrenList = new ArrayList<RouterVo>();
//                 RouterVo children = new RouterVo();
//                 String routerPath = innerLinkReplaceEach(menu.getPath());
//                 children.setPath(routerPath);
//                 children.setComponent(UserConstants.INNER_LINK);
//                 children.setName(StringUtils.capitalize(routerPath));
//                 children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
//                 childrenList.add(children);
//                 router.setChildren(childrenList);
//             }
//             routers.add(router);
//         }
//         return routers;
//     }

//     /**
//      * 构建前端所需要树结构
//      * 
//      * @param menus 菜单列表
//      * @return 树结构列表
//      */
//     @Override
//     public List<SysMenu> buildMenuTree(List<SysMenu> menus)
//     {
//         List<SysMenu> returnList = new ArrayList<SysMenu>();
//         List<Long> tempList = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
//         for (Iterator<SysMenu> iterator = menus.iterator(); iterator.hasNext();)
//         {
//             SysMenu menu = (SysMenu) iterator.next();
//             // 如果是顶级节点, 遍历该父节点的所有子节点
//             if (!tempList.contains(menu.getParentId()))
//             {
//                 recursionFn(menus, menu);
//                 returnList.add(menu);
//             }
//         }
//         if (returnList.isEmpty())
//         {
//             returnList = menus;
//         }
//         return returnList;
//     }

//     /**
//      * 构建前端所需要下拉树结构
//      * 
//      * @param menus 菜单列表
//      * @return 下拉树结构列表
//      */
//     @Override
//     public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus)
//     {
//         List<SysMenu> menuTrees = buildMenuTree(menus);
//         return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
//     }

//     /**
//      * 根据菜单ID查询信息
//      * 
//      * @param menuId 菜单ID
//      * @return 菜单信息
//      */
//     @Override
//     public SysMenu selectMenuById(Long menuId)
//     {
//         return menuMapper.selectMenuById(menuId);
//     }

//     /**
//      * 是否存在菜单子节点
//      * 
//      * @param menuId 菜单ID
//      * @return 结果
//      */
//     @Override
//     public boolean hasChildByMenuId(Long menuId)
//     {
//         int result = menuMapper.hasChildByMenuId(menuId);
//         return result > 0;
//     }

//     /**
//      * 查询菜单使用数量
//      * 
//      * @param menuId 菜单ID
//      * @return 结果
//      */
//     @Override
//     public boolean checkMenuExistRole(Long menuId)
//     {
//         int result = roleMenuMapper.checkMenuExistRole(menuId);
//         return result > 0;
//     }

//     /**
//      * 新增保存菜单信息
//      * 
//      * @param menu 菜单信息
//      * @return 结果
//      */
//     @Override
//     public int insertMenu(SysMenu menu)
//     {
//         return menuMapper.insertMenu(menu);
//     }

//     /**
//      * 修改保存菜单信息
//      * 
//      * @param menu 菜单信息
//      * @return 结果
//      */
//     @Override
//     public int updateMenu(SysMenu menu)
//     {
//         return menuMapper.updateMenu(menu);
//     }

//     /**
//      * 删除菜单管理信息
//      * 
//      * @param menuId 菜单ID
//      * @return 结果
//      */
//     @Override
//     public int deleteMenuById(Long menuId)
//     {
//         return menuMapper.deleteMenuById(menuId);
//     }

//     /**
//      * 校验菜单名称是否唯一
//      * 
//      * @param menu 菜单信息
//      * @return 结果
//      */
//     @Override
//     public boolean checkMenuNameUnique(SysMenu menu)
//     {
//         Long menuId = StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();
//         SysMenu info = menuMapper.checkMenuNameUnique(menu.getMenuName(), menu.getParentId());
//         if (StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue())
//         {
//             return UserConstants.NOT_UNIQUE;
//         }
//         return UserConstants.UNIQUE;
//     }

//     /**
//      * 获取路由名称
//      * 
//      * @param menu 菜单信息
//      * @return 路由名称
//      */
//     public String getRouteName(SysMenu menu)
//     {
//         String routerName = StringUtils.capitalize(menu.getPath());
//         // 非外链并且是一级目录（类型为目录）
//         if (isMenuFrame(menu))
//         {
//             routerName = StringUtils.EMPTY;
//         }
//         return routerName;
//     }

//     /**
//      * 获取路由地址
//      * 
//      * @param menu 菜单信息
//      * @return 路由地址
//      */
//     public String getRouterPath(SysMenu menu)
//     {
//         String routerPath = menu.getPath();
//         // 内链打开外网方式
//         if (menu.getParentId().intValue() != 0 && isInnerLink(menu))
//         {
//             routerPath = innerLinkReplaceEach(routerPath);
//         }
//         // 非外链并且是一级目录（类型为目录）
//         if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType())
//                 && UserConstants.NO_FRAME.equals(menu.getIsFrame()))
//         {
//             routerPath = "/" + menu.getPath();
//         }
//         // 非外链并且是一级目录（类型为菜单）
//         else if (isMenuFrame(menu))
//         {
//             routerPath = "/";
//         }
//         return routerPath;
//     }

//     /**
//      * 获取组件信息
//      * 
//      * @param menu 菜单信息
//      * @return 组件信息
//      */
//     public String getComponent(SysMenu menu)
//     {
//         String component = UserConstants.LAYOUT;
//         if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu))
//         {
//             component = menu.getComponent();
//         }
//         else if (StringUtils.isEmpty(menu.getComponent()) && menu.getParentId().intValue() != 0 && isInnerLink(menu))
//         {
//             component = UserConstants.INNER_LINK;
//         }
//         else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu))
//         {
//             component = UserConstants.PARENT_VIEW;
//         }
//         return component;
//     }

//     /**
//      * 是否为菜单内部跳转
//      * 
//      * @param menu 菜单信息
//      * @return 结果
//      */
//     public boolean isMenuFrame(SysMenu menu)
//     {
//         return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(menu.getMenuType())
//                 && menu.getIsFrame().equals(UserConstants.NO_FRAME);
//     }

//     /**
//      * 是否为内链组件
//      * 
//      * @param menu 菜单信息
//      * @return 结果
//      */
//     public boolean isInnerLink(SysMenu menu)
//     {
//         return menu.getIsFrame().equals(UserConstants.NO_FRAME) && StringUtils.ishttp(menu.getPath());
//     }

//     /**
//      * 是否为parent_view组件
//      * 
//      * @param menu 菜单信息
//      * @return 结果
//      */
//     public boolean isParentView(SysMenu menu)
//     {
//         return menu.getParentId().intValue() != 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType());
//     }

//     /**
//      * 根据父节点的ID获取所有子节点
//      * 
//      * @param list 分类表
//      * @param parentId 传入的父节点ID
//      * @return String
//      */
//     public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId)
//     {
//         List<SysMenu> returnList = new ArrayList<SysMenu>();
//         for (Iterator<SysMenu> iterator = list.iterator(); iterator.hasNext();)
//         {
//             SysMenu t = (SysMenu) iterator.next();
//             // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
//             if (t.getParentId() == parentId)
//             {
//                 recursionFn(list, t);
//                 returnList.add(t);
//             }
//         }
//         return returnList;
//     }

//     /**
//      * 递归列表
//      * 
//      * @param list 分类表
//      * @param t 子节点
//      */
//     private void recursionFn(List<SysMenu> list, SysMenu t)
//     {
//         // 得到子节点列表
//         List<SysMenu> childList = getChildList(list, t);
//         t.setChildren(childList);
//         for (SysMenu tChild : childList)
//         {
//             if (hasChild(list, tChild))
//             {
//                 recursionFn(list, tChild);
//             }
//         }
//     }

//     /**
//      * 得到子节点列表
//      */
//     private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t)
//     {
//         List<SysMenu> tlist = new ArrayList<SysMenu>();
//         Iterator<SysMenu> it = list.iterator();
//         while (it.hasNext())
//         {
//             SysMenu n = (SysMenu) it.next();
//             if (n.getParentId().longValue() == t.getMenuId().longValue())
//             {
//                 tlist.add(n);
//             }
//         }
//         return tlist;
//     }

//     /**
//      * 判断是否有子节点
//      */
//     private boolean hasChild(List<SysMenu> list, SysMenu t)
//     {
//         return getChildList(list, t).size() > 0;
//     }

//     /**
//      * 内链域名特殊字符替换
//      * 
//      * @return 替换后的内链域名
//      */
//     public String innerLinkReplaceEach(String path)
//     {
//         return StringUtils.replaceEach(path, new String[] { Constants.HTTP, Constants.HTTPS, Constants.WWW, ".", ":" },
//                 new String[] { "", "", "", "/", "/" });
//     }
// }
