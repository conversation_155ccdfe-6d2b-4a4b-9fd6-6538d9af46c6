package com.dkd.web.controller.monitor;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.framework.web.service.SysPasswordService;
import com.dkd.system.domain.SysLogininfor;
import com.dkd.system.service.ISysLogininforService;

/**
 * 系统访问记录
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController extends BaseController
{
    // 注入登录日志业务层接口
    @Autowired
    private ISysLogininforService logininforService;

    // 注入密码服务类，用于账户解锁功能
    @Autowired
    private SysPasswordService passwordService;

    /**
     * 查询登录日志列表接口
     * 需要'monitor:logininfor:list'权限
     * 请求路径：GET /monitor/logininfor/list
     */
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysLogininfor logininfor)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询登录日志数据
        List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出登录日志数据接口
     * 需要'monitor:logininfor:export'权限
     * 请求路径：POST /monitor/logininfor/export
     */
    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLogininfor logininfor)
    {
        // 调用服务层方法查询需要导出的登录日志数据
        List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
        // 创建Excel工具类实例
        ExcelUtil<SysLogininfor> util = new ExcelUtil<SysLogininfor>(SysLogininfor.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "登录日志");
    }

    /**
     * 删除指定ID的登录日志接口
     * 需要'monitor:logininfor:remove'权限
     * 请求路径：DELETE /monitor/logininfor/{infoIds}
     */
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public AjaxResult remove(@PathVariable Long[] infoIds)
    {
        // 调用服务层方法删除指定ID的登录日志
        return toAjax(logininforService.deleteLogininforByIds(infoIds));
    }

    /**
     * 清空所有登录日志接口
     * 需要'monitor:logininfor:remove'权限
     * 请求路径：DELETE /monitor/logininfor/clean
     */
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        // 调用服务层方法清空登录日志数据
        logininforService.cleanLogininfor();
        // 返回操作成功结果
        return success();
    }

    /**
     * 解锁用户账户接口
     * 需要'monitor:logininfor:unlock'权限
     * 请求路径：GET /monitor/logininfor/unlock/{userName}
     */
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:unlock')")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{userName}")
    public AjaxResult unlock(@PathVariable("userName") String userName)
    {
        // 调用密码服务清除用户登录记录缓存
        passwordService.clearLoginRecordCache(userName);
        // 返回操作成功结果
        return success();
    }
}





// @RestController
// @RequestMapping("/monitor/logininfor")
// public class SysLogininforController extends BaseController
// {
//     @Autowired
//     private ISysLogininforService logininforService;

//     @Autowired
//     private SysPasswordService passwordService;

//     @PreAuthorize("@ss.hasPermi('monitor:logininfor:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysLogininfor logininfor)
//     {
//         startPage();
//         List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
//         return getDataTable(list);
//     }

//     @Log(title = "登录日志", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('monitor:logininfor:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysLogininfor logininfor)
//     {
//         List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
//         ExcelUtil<SysLogininfor> util = new ExcelUtil<SysLogininfor>(SysLogininfor.class);
//         util.exportExcel(response, list, "登录日志");
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
//     @Log(title = "登录日志", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{infoIds}")
//     public AjaxResult remove(@PathVariable Long[] infoIds)
//     {
//         return toAjax(logininforService.deleteLogininforByIds(infoIds));
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
//     @Log(title = "登录日志", businessType = BusinessType.CLEAN)
//     @DeleteMapping("/clean")
//     public AjaxResult clean()
//     {
//         logininforService.cleanLogininfor();
//         return success();
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:logininfor:unlock')")
//     @Log(title = "账户解锁", businessType = BusinessType.OTHER)
//     @GetMapping("/unlock/{userName}")
//     public AjaxResult unlock(@PathVariable("userName") String userName)
//     {
//         passwordService.clearLoginRecordCache(userName);
//         return success();
//     }
// }
