<template>
  <div class="app-container">
    <!-- 策略使用统计图表 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>策略使用统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新统计
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 400px;" v-loading="chartLoading"></div>
    </el-card>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="策略名称" prop="policyName">
        <el-input
          v-model="queryParams.policyName"
          placeholder="请输入策略名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:policy:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:policy:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:policy:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:policy:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="policyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" align="center" prop="policyId" />
      <el-table-column label="策略名称" align="center" prop="policyName" />
      <el-table-column label="策略方案" align="center" prop="discount" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary"  @click="getPolicyInfo(scope.row)" v-hasPermi="['manage:vm:list']">查看详情</el-button>
          <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['manage:policy:edit']">修改</el-button>
          <el-button link type="primary"  @click="handleDelete(scope.row)" v-hasPermi="['manage:policy:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改策略管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="policyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="策略名称" prop="policyName">
          <el-input v-model="form.policyName" placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="策略方案" prop="discount">
          <el-input-number :min="1" :max="100" :precision="0" v-model="form.discount" placeholder="请输入策略方案" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog :title="策略详情" v-model="open1" width="500px" append-to-body>
      <el-form-item label="策略名称" prop="policyName">
        <el-input v-model="form.policyName" placeholder="请输入策略名称" disabled />
      </el-form-item>
      <label>包含设备：</label>
      <el-table v-loading="loading" :data="vmList" >
        <el-table-column label="序号" type="index" width="55" align="center" />
        <el-table-column label="点位地址" align="center" prop="addr" />
        <el-table-column label="设备编号" align="center" prop="innerCode" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Policy">
import { listPolicy, getPolicy, delPolicy, addPolicy, updatePolicy } from "@/api/manage/policy";
import { listVm } from "@/api/manage/vm";
import { loadAllParams } from "@/api/page";
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const policyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    policyName: null,
  },
  rules: {
    policyName: [
      { required: true, message: "策略名称不能为空", trigger: "blur" }
    ],
    discount: [
      { required: true, message: "策略方案不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询策略管理列表 */
function getList() {
  loading.value = true;
  listPolicy(queryParams.value).then(response => {
    policyList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 刷新图表数据
    if (chartInstance.value) {
      loadChartData();
    }
  });
}

/** 查询策略管理详情 */
const vmList = ref([]);
const open1 = ref(false);
function getPolicyInfo(row) {
  //1. 获取策略信息
  form.value = row;
  //2. 根据策略id, 查询设备列表
  loadAllParams.policyId = row.policyId;
  listVm(loadAllParams).then(response => {
    vmList.value = response.rows;
    open1.value = true;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    policyId: null,
    policyName: null,
    discount: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("policyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.policyId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加策略管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _policyId = row.policyId || ids.value
  getPolicy(_policyId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改策略管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["policyRef"].validate(valid => {
    if (valid) {
      if (form.value.policyId != null) {
        updatePolicy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPolicy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _policyIds = row.policyId || ids.value;
  proxy.$modal.confirm('是否确认删除策略管理编号为"' + _policyIds + '"的数据项？').then(function() {
    return delPolicy(_policyIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('manage/policy/export', {
    ...queryParams.value
  }, `policy_${new Date().getTime()}.xlsx`)
}

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    // 获取所有策略数据（不分页）
    const allPolicyParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数值来获取所有数据
      policyName: null,
    };

    const allPolicyResponse = await listPolicy(allPolicyParams);
    const allPolicyList = allPolicyResponse.rows || [];

    // 获取所有设备数据（不分页）
    const allVmParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数值来获取所有数据
      innerCode: null,
    };

    const allVmResponse = await listVm(allVmParams);
    const allVmList = allVmResponse.rows || [];

    // 处理数据
    const policyData = [];

    // 统计每个策略被使用的设备数量
    allPolicyList.forEach(policy => {
      const count = allVmList.filter(vm => vm.policyId === policy.policyId).length;

      policyData.push({
        name: policy.policyName,
        value: count
      });
    });

    // 调试日志
    console.log('策略使用统计:', policyData);

    // 如果没有数据，添加默认数据
    if (policyData.length === 0) {
      policyData.push({
        name: '暂无数据',
        value: 0
      });
    }

    updateChart(policyData);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(policyData) {
  if (!chartInstance.value) return;

  // 定义颜色数组
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#5470c6'];

  const option = {
    title: {
      text: '策略使用统计',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} 台 ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      left: 'center',
      textStyle: {
        fontSize: 12
      },
      itemWidth: 14,
      itemHeight: 14
    },
    series: [
      {
        name: '策略使用情况',
        type: 'pie',
        radius: ['40%', '70%'], // 环形饼图
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            formatter: function(params) {
              return params.name + '\n' + params.value + ' 台';
            }
          }
        },
        labelLine: {
          show: false
        },
        data: policyData.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询策略列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
