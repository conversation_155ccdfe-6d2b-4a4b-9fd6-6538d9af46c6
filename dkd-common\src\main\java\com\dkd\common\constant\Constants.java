// 定义包路径
package com.dkd.common.constant;

// 导入Java核心类
import java.util.Locale; // 本地化语言设置
import io.jsonwebtoken.Claims; // JWT声明信息

/**
 * 通用常量信息
 * 定义系统中使用的各种常量值
 * 包括字符集、协议、状态码、缓存键等通用配置
 * 便于统一管理和维护系统常量
 *
 * <AUTHOR>
 */
public class Constants
{
    /**
     * UTF-8 字符集
     * 系统默认使用的字符编码格式
     * 支持全球所有语言字符
     */
    public static final String UTF8 = "UTF-8"; // UTF-8编码常量

    /**
     * GBK 字符集
     * 中文字符编码格式
     * 主要用于兼容老系统或特定场景
     */
    public static final String GBK = "GBK"; // GBK编码常量

    /**
     * 系统语言
     * 系统默认的本地化语言设置
     * 设置为简体中文
     */
    public static final Locale DEFAULT_LOCALE = Locale.SIMPLIFIED_CHINESE; // 默认语言环境

    /**
     * www主域
     * 网站主域名前缀
     * 用于URL拼接和域名处理
     */
    public static final String WWW = "www."; // WWW域名前缀

    /**
     * http请求
     * HTTP协议前缀
     * 用于构建HTTP请求URL
     */
    public static final String HTTP = "http://"; // HTTP协议前缀

    /**
     * https请求
     * HTTPS安全协议前缀
     * 用于构建HTTPS请求URL
     */
    public static final String HTTPS = "https://"; // HTTPS协议前缀

    /**
     * 通用成功标识
     * 表示操作成功的状态码
     * 用于统一的响应状态判断
     */
    public static final String SUCCESS = "0"; // 成功状态码

    /**
     * 通用失败标识
     * 表示操作失败的状态码
     * 用于统一的响应状态判断
     */
    // 失败状态码常量
    public static final String FAIL = "1";

    /**
     * 登录成功
     * 表示用户登录操作成功的标识
     * 用于日志记录和状态判断
     */
    // 登录成功状态标识
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     * 表示用户注销操作的标识
     * 用于日志记录和状态判断
     */
    // 用户注销操作标识
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     * 表示用户注册操作的标识
     * 用于日志记录和状态判断
     */
    // 用户注册操作标识
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     * 表示用户登录操作失败的标识
     * 用于日志记录和错误处理
     */
    // 登录失败状态标识
    public static final String LOGIN_FAIL = "Error";

    /**
     * 所有权限标识
     * 超级管理员拥有的最高权限标识
     * 格式：模块:功能:操作，*表示所有
     */
    // 超级权限标识，表示拥有所有权限
    public static final String ALL_PERMISSION = "*:*:*";

    /**
     * 管理员角色权限标识
     * 系统超级管理员的角色标识
     * 拥有系统所有功能的访问权限
     */
    // 超级管理员角色标识
    public static final String SUPER_ADMIN = "admin";

    /**
     * 角色权限分隔符
     * 用于分割多个角色权限的分隔符
     * 在权限字符串中分隔不同的角色
     */
    // 角色权限分隔符，用于分割多个角色
    public static final String ROLE_DELIMETER = ",";

    /**
     * 权限标识分隔符
     * 用于分割多个权限标识的分隔符
     * 在权限字符串中分隔不同的权限
     */
    // 权限标识分隔符，用于分割多个权限
    public static final String PERMISSION_DELIMETER = ",";

    /**
     * 验证码有效期（分钟）
     * 图形验证码的有效时间
     * 超过此时间验证码将失效
     */
    // 验证码有效期，单位为分钟
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 自动识别json对象白名单配置（仅允许解析的包名，范围越小越安全）
     */
    public static final String[] JSON_WHITELIST_STR = { "org.springframework", "com.dkd" };

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = { "com.dkd.quartz.task" };

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = { "java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.dkd.common.utils.file", "com.dkd.common.config", "com.dkd.generator" };
}
