// 定义包路径
package com.dkd.common.config.serializer;

// 导入Java IO相关类
import java.io.IOException; // IO异常类
import java.util.Objects; // 对象工具类
// 导入Jackson JSON处理相关类
import com.fasterxml.jackson.core.JsonGenerator; // JSON生成器
import com.fasterxml.jackson.databind.BeanProperty; // Bean属性信息
import com.fasterxml.jackson.databind.JsonMappingException; // JSON映射异常
import com.fasterxml.jackson.databind.JsonSerializer; // JSON序列化器基类
import com.fasterxml.jackson.databind.SerializerProvider; // 序列化提供者
import com.fasterxml.jackson.databind.ser.ContextualSerializer; // 上下文序列化器接口
// 导入项目相关类
import com.dkd.common.annotation.Sensitive; // 敏感数据注解
import com.dkd.common.core.domain.model.LoginUser; // 登录用户模型
import com.dkd.common.enums.DesensitizedType; // 脱敏类型枚举
import com.dkd.common.utils.SecurityUtils; // 安全工具类

/**
 * 数据脱敏序列化过滤
 * 自定义JSON序列化器，用于在序列化过程中对敏感数据进行脱敏处理
 * 实现ContextualSerializer接口，支持根据注解配置动态创建序列化器
 * 根据用户权限决定是否进行脱敏，管理员用户可以查看完整数据
 *
 * <AUTHOR>
 */
public class SensitiveJsonSerializer extends JsonSerializer<String> implements ContextualSerializer
{
    /**
     * 脱敏类型
     * 存储当前字段的脱敏处理方式
     */
    private DesensitizedType desensitizedType; // 脱敏类型配置

    /**
     * 序列化方法
     * 根据脱敏配置对字符串值进行处理
     *
     * @param value 原始字符串值
     * @param gen JSON生成器
     * @param serializers 序列化提供者
     * @throws IOException IO异常
     */
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException
    {
        // 判断是否需要脱敏处理
        if (desensitization())
        {
            // 使用脱敏器对数据进行脱敏处理
            gen.writeString(desensitizedType.desensitizer().apply(value));
        }
        else
        {
            // 直接输出原始数据
            gen.writeString(value);
        }
    }

    /**
     * 创建上下文序列化器
     * 根据字段上的@Sensitive注解配置创建对应的序列化器
     *
     * @param prov 序列化提供者
     * @param property Bean属性信息
     * @return 序列化器实例
     * @throws JsonMappingException JSON映射异常
     */
    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property)
            throws JsonMappingException
    {
        // 获取字段上的@Sensitive注解
        Sensitive annotation = property.getAnnotation(Sensitive.class);
        // 检查注解是否存在且字段类型为String
        if (Objects.nonNull(annotation) && Objects.equals(String.class, property.getType().getRawClass()))
        {
            // 设置脱敏类型
            this.desensitizedType = annotation.desensitizedType();
            // 返回当前序列化器实例
            return this;
        }
        // 返回默认的序列化器
        return prov.findValueSerializer(property.getType(), property);
    }

    /**
     * 是否需要脱敏处理
     * 根据当前登录用户的权限判断是否需要对数据进行脱敏
     * 管理员用户不进行脱敏，普通用户进行脱敏
     *
     * @return true表示需要脱敏，false表示不需要脱敏
     */
    private boolean desensitization()
    {
        try
        {
            // 获取当前登录用户信息
            LoginUser securityUser = SecurityUtils.getLoginUser();
            // 管理员不脱敏，返回false
            return !securityUser.getUser().isAdmin();
        }
        catch (Exception e)
        {
            // 获取用户信息失败时，默认进行脱敏处理
            return true;
        }
    }
}
