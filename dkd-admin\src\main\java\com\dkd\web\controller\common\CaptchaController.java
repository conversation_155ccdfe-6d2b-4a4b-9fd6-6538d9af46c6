package com.dkd.web.controller.common;

import com.dkd.common.config.RuoYiConfig;
import com.dkd.common.constant.CacheConstants;
import com.dkd.common.constant.Constants;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.redis.RedisCache;
import com.dkd.common.utils.sign.Base64;
import com.dkd.common.utils.uuid.IdUtils;
import com.dkd.system.service.ISysConfigService;
import com.google.code.kaptcha.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 * 控制器类，用于处理验证码相关的请求。
 * 返回值直接作为响应体内容。
 */
@RestController
public class CaptchaController
{
    // 注入名为 captchaProducer 的 Bean，用于生成字符验证码。
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    // 注入名为 captchaProducerMath 的 Bean，用于生成数学公式验证码。
    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    // 自动注入 RedisCache 实例，用于操作 Redis 缓存。
    @Autowired
    private RedisCache redisCache;

    // 自动注入 ISysConfigService 实例，用于获取系统配置信息。
    @Autowired
    private ISysConfigService configService;


    /**
     * 生成验证码
     * 处理 GET 请求 /captchaImage，生成 Base64 编码的验证码图片并返回。
     *
     * @param response HttpServletResponse 对象，用于输出图片流
     * @return AjaxResult 包含验证码启用状态、UUID 和 Base64 图片数据
     * @throws IOException 如果图片写入失败
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {
        // 初始化一个成功状态的 AjaxResult 对象
        AjaxResult ajax = AjaxResult.success();

        // 获取系统配置的验证码启用状态
        boolean captchaEnabled = configService.selectCaptchaEnabled();

        // 将验证码启用状态放入返回结果
        ajax.put("captchaEnabled", captchaEnabled);

        // 如果验证码未启用，则直接返回结果
        if (!captchaEnabled)
        {
            return ajax;
        }

        // 生成唯一标识符 UUID
        String uuid = IdUtils.simpleUUID();

        // 构建缓存 key，用于存储验证码
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        // 定义变量用于存储验证码字符串和实际值
        String capStr = null, code = null;

        // 定义 BufferedImage 对象用于存储生成的验证码图片
        BufferedImage image = null;

        // 获取当前配置的验证码类型
        String captchaType = RuoYiConfig.getCaptchaType();

        // 如果是数学公式验证码
        if ("math".equals(captchaType))
        {
            // 生成数学公式文本
            String capText = captchaProducerMath.createText();

            // 提取公式部分
            capStr = capText.substring(0, capText.lastIndexOf("@"));

            // 提取答案部分
            code = capText.substring(capText.lastIndexOf("@") + 1);

            // 创建数学公式验证码图片
            image = captchaProducerMath.createImage(capStr);
        }
        else if ("char".equals(captchaType))
        {
            // 生成并赋值验证码文本
            capStr = code = captchaProducer.createText();

            // 创建字符验证码图片
            image = captchaProducer.createImage(capStr);
        }

        // 存储验证码到 Redis 中，并设置过期时间
        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);

        // 创建快速字节输出流对象
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            // 将图片写入输出流
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            // 如果出现异常，返回错误信息
            return AjaxResult.error(e.getMessage());
        }

        // 将 UUID 放入返回结果
        ajax.put("uuid", uuid);

        // 将图片数据编码为 Base64 字符串后放入返回结果
        ajax.put("img", Base64.encode(os.toByteArray()));

        // 返回最终结果
        return ajax;
    }
}


// @RestController
// public class CaptchaController
// {
//     @Resource(name = "captchaProducer")
//     private Producer captchaProducer;

//     @Resource(name = "captchaProducerMath")
//     private Producer captchaProducerMath;

//     @Autowired
//     private RedisCache redisCache;

//     @Autowired
//     private ISysConfigService configService;


    
//     @GetMapping("/captchaImage")
//     public AjaxResult getCode(HttpServletResponse response) throws IOException
//     {
//         AjaxResult ajax = AjaxResult.success();
//         boolean captchaEnabled = configService.selectCaptchaEnabled();
//         ajax.put("captchaEnabled", captchaEnabled);
//         if (!captchaEnabled)
//         {
//             return ajax;
//         }

//         // 保存验证码信息
//         String uuid = IdUtils.simpleUUID();
//         String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

//         String capStr = null, code = null;
//         BufferedImage image = null;

//         // 生成验证码
//         String captchaType = RuoYiConfig.getCaptchaType();
//         if ("math".equals(captchaType))
//         {
//             String capText = captchaProducerMath.createText();
//             capStr = capText.substring(0, capText.lastIndexOf("@"));
//             code = capText.substring(capText.lastIndexOf("@") + 1);
//             image = captchaProducerMath.createImage(capStr);
//         }
//         else if ("char".equals(captchaType))
//         {
//             capStr = code = captchaProducer.createText();
//             image = captchaProducer.createImage(capStr);
//         }

//         redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
//         // 转换流信息写出
//         FastByteArrayOutputStream os = new FastByteArrayOutputStream();
//         try
//         {
//             ImageIO.write(image, "jpg", os);
//         }
//         catch (IOException e)
//         {
//             return AjaxResult.error(e.getMessage());
//         }

//         ajax.put("uuid", uuid);
//         ajax.put("img", Base64.encode(os.toByteArray()));
//         return ajax;
//     }
// }
