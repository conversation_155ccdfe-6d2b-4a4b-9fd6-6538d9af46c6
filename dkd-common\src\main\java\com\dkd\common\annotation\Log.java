// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.Documented; // 用于标记注解是否包含在JavaDoc中
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型
import com.dkd.common.enums.BusinessType; // 导入业务类型枚举
import com.dkd.common.enums.OperatorType; // 导入操作人类型枚举

/**
 * 自定义操作日志记录注解
 * 用于标记需要记录操作日志的方法或参数
 * 通过AOP切面拦截带有此注解的方法，自动记录用户操作行为
 * 支持记录操作模块、业务类型、操作人类别、请求参数、响应参数等信息
 *
 * <AUTHOR>
 */
@Target({ ElementType.PARAMETER, ElementType.METHOD }) // 指定注解可以应用于参数和方法
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Documented // 指定注解会被包含在JavaDoc中
public @interface Log // 定义操作日志记录注解接口
{
    /**
     * 模块
     * 用于标识操作所属的功能模块
     * 例如：用户管理、角色管理、系统设置等
     * 便于日志分类和查询
     *
     * @return 模块名称
     */
    public String title() default ""; // 定义模块标题属性，默认值为空字符串

    /**
     * 功能
     * 用于标识具体的业务操作类型
     * 例如：新增、修改、删除、查询、导入、导出等
     * 便于统计各种操作的频次
     *
     * @return 业务类型枚举
     */
    public BusinessType businessType() default BusinessType.OTHER; // 定义业务类型属性，默认值为其他类型

    /**
     * 操作人类别
     * 用于区分不同类型的操作者
     * 例如：管理员、普通用户、系统自动等
     * 便于分析不同角色的操作行为
     *
     * @return 操作人类型枚举
     */
    public OperatorType operatorType() default OperatorType.MANAGE; // 定义操作人类型属性，默认值为管理员

    /**
     * 是否保存请求的参数
     * 控制是否在日志中记录方法的输入参数
     * true：记录请求参数，便于问题排查
     * false：不记录请求参数，节省存储空间
     *
     * @return 是否保存请求参数
     */
    public boolean isSaveRequestData() default true; // 定义是否保存请求数据属性，默认值为true

    /**
     * 是否保存响应的参数
     * 控制是否在日志中记录方法的返回结果
     * true：记录响应参数，便于结果追踪
     * false：不记录响应参数，避免敏感信息泄露
     *
     * @return 是否保存响应参数
     */
    public boolean isSaveResponseData() default true; // 定义是否保存响应数据属性，默认值为true

    /**
     * 排除指定的请求参数
     * 指定不需要记录在日志中的参数名称
     * 通常用于排除密码、令牌等敏感信息
     * 支持多个参数名，以数组形式配置
     *
     * @return 排除的参数名数组
     */
    public String[] excludeParamNames() default {}; // 定义排除参数名属性，默认值为空数组
}
