package com.dkd.web.controller.system;

import java.util.List;
import java.util.Set;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.constant.Constants;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysMenu;
import com.dkd.common.core.domain.entity.SysUser;
import com.dkd.common.core.domain.model.LoginBody;
import com.dkd.common.utils.SecurityUtils;
import com.dkd.framework.web.service.SysLoginService;
import com.dkd.framework.web.service.SysPermissionService;
import com.dkd.system.service.ISysMenuService;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块登录相关的HTTP请求
@RestController
public class SysLoginController
{
    // 注入登录业务层接口
    @Autowired
    private SysLoginService loginService;

    // 注入菜单业务层接口，用于获取用户菜单权限
    @Autowired
    private ISysMenuService menuService;

    // 注入权限校验服务类
    @Autowired
    private SysPermissionService permissionService;

    /**
     * 用户登录接口
     * 请求路径：POST /login
     * @param loginBody 登录请求体包含用户名、密码、验证码、UUID
     * @return 返回包含登录令牌的响应结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        // 创建成功响应对象
        AjaxResult ajax = AjaxResult.success();
        // 调用登录服务生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        // 将令牌放入响应数据中
        ajax.put(Constants.TOKEN, token);
        // 返回响应结果
        return ajax;
    }

    /**
     * 获取当前登录用户信息接口
     * 请求路径：GET /getInfo
     * @return 返回包含用户信息、角色集合和权限集合的响应结果
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        // 获取当前登录用户对象
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 获取用户拥有的角色权限
        Set<String> roles = permissionService.getRolePermission(user);
        // 获取用户拥有的菜单权限
        Set<String> permissions = permissionService.getMenuPermission(user);
        // 创建成功响应对象
        AjaxResult ajax = AjaxResult.success();
        // 添加用户信息到响应数据
        ajax.put("user", user);
        // 添加角色信息到响应数据
        ajax.put("roles", roles);
        // 添加权限信息到响应数据
        ajax.put("permissions", permissions);
        // 返回响应结果
        return ajax;
    }

    /**
     * 获取用户路由信息接口
     * 请求路径：GET /getRouters
     * @return 返回用户可访问的菜单路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        // 获取当前用户的ID
        Long userId = SecurityUtils.getUserId();
        // 查询用户可访问的菜单树
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        // 构建并返回菜单路由信息
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}





// @RestController
// public class SysLoginController
// {
//     @Autowired
//     private SysLoginService loginService;

//     @Autowired
//     private ISysMenuService menuService;

//     @Autowired
//     private SysPermissionService permissionService;

//     /**
//      * 登录方法
//      * 
//      * @param loginBody 登录信息
//      * @return 结果
//      */
//     @PostMapping("/login")
//     @ApiOperation(value = "登录")
//     public AjaxResult login(@RequestBody LoginBody loginBody)
//     {
//         AjaxResult ajax = AjaxResult.success();
//         // 生成令牌
//         String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
//                 loginBody.getUuid());
//         ajax.put(Constants.TOKEN, token);
//         return ajax;
//     }

//     /**
//      * 获取用户信息
//      * 
//      * @return 用户信息
//      */
//     @GetMapping("getInfo")
//     public AjaxResult getInfo()
//     {
//         SysUser user = SecurityUtils.getLoginUser().getUser();
//         // 角色集合
//         Set<String> roles = permissionService.getRolePermission(user);
//         // 权限集合
//         Set<String> permissions = permissionService.getMenuPermission(user);
//         AjaxResult ajax = AjaxResult.success();
//         ajax.put("user", user);
//         ajax.put("roles", roles);
//         ajax.put("permissions", permissions);
//         return ajax;
//     }

//     /**
//      * 获取路由信息
//      * 
//      * @return 路由信息
//      */
//     @GetMapping("getRouters")
//     public AjaxResult getRouters()
//     {
//         Long userId = SecurityUtils.getUserId();
//         List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
//         return AjaxResult.success(menuService.buildMenus(menus));
//     }
// }
