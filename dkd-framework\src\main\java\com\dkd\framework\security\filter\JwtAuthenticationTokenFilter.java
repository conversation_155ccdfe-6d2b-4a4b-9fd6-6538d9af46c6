// 包声明：定义当前类所属的包路径，位于安全框架的过滤器包中
package com.dkd.framework.security.filter;

// 导入Java IO异常类，用于处理输入输出异常
import java.io.IOException;
// 导入Servlet过滤器链接口，用于过滤器链的传递
import javax.servlet.FilterChain;
// 导入Servlet异常类，用于处理Servlet相关异常
import javax.servlet.ServletException;
// 导入HTTP请求接口，用于获取请求信息
import javax.servlet.http.HttpServletRequest;
// 导入HTTP响应接口，用于设置响应信息
import javax.servlet.http.HttpServletResponse;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring Security用户名密码认证令牌类
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
// 导入Spring Security安全上下文持有者，用于管理安全上下文
import org.springframework.security.core.context.SecurityContextHolder;
// 导入Spring Security Web认证详情源，用于构建认证详情
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
// 导入Spring组件注解
import org.springframework.stereotype.Component;
// 导入Spring Web过滤器基类，确保每个请求只执行一次
import org.springframework.web.filter.OncePerRequestFilter;
// 导入登录用户模型类
import com.dkd.common.core.domain.model.LoginUser;
// 导入安全工具类
import com.dkd.common.utils.SecurityUtils;
// 导入字符串工具类
import com.dkd.common.utils.StringUtils;
// 导入Token服务类
import com.dkd.framework.web.service.TokenService;

/**
 * JWT认证令牌过滤器
 * 这个过滤器是Spring Security过滤器链中的重要组成部分，
 * 负责验证每个HTTP请求中的JWT令牌有效性，并设置用户的认证信息。
 *
 * 主要功能：
 * 1. 从HTTP请求中提取JWT令牌
 * 2. 验证令牌的有效性和过期时间
 * 3. 从令牌中获取用户信息和权限
 * 4. 将认证信息设置到Spring Security上下文中
 * 5. 支持令牌自动刷新机制
 *
 * 工作流程：
 * 请求到达 → 提取令牌 → 验证令牌 → 获取用户信息 → 设置认证上下文 → 继续过滤器链
 *
 * <AUTHOR>
 */
@Component // 将此类标记为Spring组件，由Spring容器管理
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter
{
    // 注入Token服务，用于令牌的验证和用户信息获取
    @Autowired
    private TokenService tokenService;

    /**
     * 执行过滤器的核心方法
     * 这个方法会在每个HTTP请求到达时被调用，负责JWT令牌的验证和认证信息的设置
     *
     * @param request HTTP请求对象，包含客户端发送的所有信息
     * @param response HTTP响应对象，用于向客户端发送响应
     * @param chain 过滤器链，用于将请求传递给下一个过滤器或目标资源
     * @throws ServletException 当Servlet处理过程中发生异常时抛出
     * @throws IOException 当输入输出操作发生异常时抛出
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException
    {
        // 第一步：从HTTP请求中提取JWT令牌并获取对应的登录用户信息
        // tokenService.getLoginUser()会解析请求头中的Authorization字段，
        // 验证JWT令牌并从Redis缓存中获取完整的用户信息
        LoginUser loginUser = tokenService.getLoginUser(request);

        // 第二步：检查用户信息是否有效且当前安全上下文中没有认证信息
        // StringUtils.isNotNull(loginUser) - 确保从令牌中成功获取到用户信息
        // StringUtils.isNull(SecurityUtils.getAuthentication()) - 确保当前线程还没有设置认证信息，避免重复设置
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication()))
        {
            // 第三步：验证令牌的有效期，如果接近过期则自动刷新
            // 这个方法会检查令牌是否在20分钟内过期，如果是则自动延长有效期
            tokenService.verifyToken(loginUser);

            // 第四步：创建Spring Security的认证令牌
            // 参数说明：loginUser(主体), null(凭据，JWT验证后不需要密码), loginUser.getAuthorities()(权限列表)
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());

            // 第五步：设置认证详情，包含请求的IP地址、会话ID等信息
            // WebAuthenticationDetailsSource会从HTTP请求中提取详细信息并构建认证详情对象
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

            // 第六步：将认证令牌设置到Spring Security的安全上下文中
            // 设置后，后续的安全检查和权限验证都会基于这个认证信息进行
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        }

        // 第七步：继续执行过滤器链，将请求传递给下一个过滤器或目标控制器
        // 无论认证是否成功，都要继续过滤器链的执行
        chain.doFilter(request, response);
    }
} // 类结束标记
