package com.dkd.web.controller.monitor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.framework.web.domain.Server;

/**
 * 服务器监控
 * 
 * <AUTHOR>
 */
 // 定义REST控制器，处理监控模块服务器信息相关的HTTP请求
@RestController
// 设置基础请求路径为"/monitor/server"
@RequestMapping("/monitor/server")
public class ServerController
{
    // 使用@PreAuthorize注解进行权限校验，确保调用者具有'monitor:server:list'权限
    @PreAuthorize("@ss.hasPermi('monitor:server:list')")
    // 处理GET请求，无路径参数
    @GetMapping()
    public AjaxResult getInfo() throws Exception
    {
        // 创建Server实例
        Server server = new Server();
        // 调用copyTo方法获取服务器信息
        server.copyTo();
        // 返回包含服务器信息的成功响应
        return AjaxResult.success(server);
    }
}



// @RestController
// @RequestMapping("/monitor/server")
// public class ServerController
// {
//     @PreAuthorize("@ss.hasPermi('monitor:server:list')")
//     @GetMapping()
//     public AjaxResult getInfo() throws Exception
//     {
//         Server server = new Server();
//         server.copyTo();
//         return AjaxResult.success(server);
//     }
// }
