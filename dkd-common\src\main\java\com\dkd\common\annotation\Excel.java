// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型
import java.math.BigDecimal; // 导入BigDecimal类，用于精确的小数计算
import org.apache.poi.ss.usermodel.HorizontalAlignment; // 导入POI库的水平对齐方式枚举
import org.apache.poi.ss.usermodel.IndexedColors; // 导入POI库的颜色索引枚举
import com.dkd.common.utils.poi.ExcelHandlerAdapter; // 导入Excel处理适配器类

/**
 * 自定义导出Excel数据注解
 * 用于标记实体类字段，配置Excel导入导出的各种属性
 * 支持字段排序、格式化、样式设置、数据转换等功能
 * 通过反射机制读取注解配置，自动生成Excel文件或解析Excel数据
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Target(ElementType.FIELD) // 指定注解只能应用于字段
public @interface Excel // 定义Excel导入导出注解接口
{
    /**
     * 导出时在excel中排序
     * 数值越小排序越靠前，用于控制Excel列的显示顺序
     * 默认值为Integer.MAX_VALUE，表示排在最后
     *
     * @return 排序值
     */
    public int sort() default Integer.MAX_VALUE; // 定义排序属性，默认值为最大整数

    /**
     * 导出到Excel中的名字
     * 指定在Excel表头显示的列名称
     * 如果为空则使用字段名作为列名
     *
     * @return Excel列名
     */
    public String name() default ""; // 定义列名属性，默认值为空字符串

    /**
     * 日期格式, 如: yyyy-MM-dd
     * 用于格式化日期类型的字段
     * 支持标准的日期时间格式模式
     *
     * @return 日期格式字符串
     */
    public String dateFormat() default ""; // 定义日期格式属性，默认值为空字符串

    /**
     * 如果是字典类型，请设置字典的type值 (如: sys_user_sex)
     * 用于将字典值转换为对应的显示文本
     * 需要配合字典服务使用
     *
     * @return 字典类型
     */
    public String dictType() default ""; // 定义字典类型属性，默认值为空字符串

    /**
     * 读取内容转表达式 (如: 0=男,1=女,2=未知)
     * 用于将数值或代码转换为可读的文本
     * 格式为：值1=显示文本1,值2=显示文本2
     *
     * @return 转换表达式
     */
    public String readConverterExp() default ""; // 定义读取转换表达式属性，默认值为空字符串

    /**
     * 分隔符，读取字符串组内容
     * 用于分割字符串数组或集合类型的数据
     * 默认使用逗号分隔
     *
     * @return 分隔符
     */
    public String separator() default ","; // 定义分隔符属性，默认值为逗号

    /**
     * BigDecimal 精度 默认:-1(默认不开启BigDecimal格式化)
     * 用于控制小数位数，-1表示不进行格式化
     * 大于等于0时，会按照指定精度格式化数值
     *
     * @return 小数精度
     */
    public int scale() default -1; // 定义精度属性，默认值为-1

    /**
     * BigDecimal 舍入规则 默认:BigDecimal.ROUND_HALF_EVEN
     * 用于指定数值舍入的方式
     * 默认使用银行家舍入法（四舍六入五成双）
     *
     * @return 舍入规则
     */
    public int roundingMode() default BigDecimal.ROUND_HALF_EVEN; // 定义舍入规则属性，默认值为银行家舍入

    /**
     * 导出时在excel中每个列的高度
     * 用于设置Excel单元格的行高
     * 单位为磅（point）
     *
     * @return 行高
     */
    public double height() default 14; // 定义行高属性，默认值为14磅

    /**
     * 导出时在excel中每个列的宽度
     * 用于设置Excel单元格的列宽
     * 单位为字符宽度
     *
     * @return 列宽
     */
    public double width() default 16; // 定义列宽属性，默认值为16个字符宽度

    /**
     * 文字后缀,如% 90 变成90%
     * 用于在数值后面添加单位或符号
     * 常用于百分比、货币符号等
     *
     * @return 后缀字符串
     */
    public String suffix() default ""; // 定义后缀属性，默认值为空字符串

    /**
     * 当值为空时,字段的默认值
     * 用于在字段值为null或空时显示的默认内容
     * 避免Excel中出现空白单元格
     *
     * @return 默认值
     */
    public String defaultValue() default ""; // 定义默认值属性，默认值为空字符串

    /**
     * 提示信息
     * 用于在Excel单元格中添加批注或提示
     * 鼠标悬停时显示的帮助信息
     *
     * @return 提示信息
     */
    public String prompt() default ""; // 定义提示信息属性，默认值为空字符串

    /**
     * 设置只能选择不能输入的列内容
     * 用于创建下拉选择列表
     * 限制用户只能从预定义的选项中选择
     *
     * @return 下拉选项数组
     */
    public String[] combo() default {}; // 定义下拉选项属性，默认值为空数组

    /**
     * 是否需要纵向合并单元格,应对需求:含有list集合单元格)
     * 用于处理一对多关系的数据导出
     * 当主记录有多个子记录时，合并主记录的单元格
     *
     * @return 是否合并单元格
     */
    public boolean needMerge() default false; // 定义是否合并单元格属性，默认值为false

    /**
     * 是否导出数据,应对需求:有时我们需要导出一份模板,这是标题需要但内容需要用户手工填写
     * 用于控制是否导出实际数据
     * false时只导出表头，用于生成Excel模板
     *
     * @return 是否导出数据
     */
    public boolean isExport() default true; // 定义是否导出数据属性，默认值为true

    /**
     * 另一个类中的属性名称,支持多级获取,以小数点隔开
     * 用于获取关联对象的属性值
     * 支持链式访问，如：user.dept.name
     *
     * @return 目标属性路径
     */
    public String targetAttr() default ""; // 定义目标属性属性，默认值为空字符串

    /**
     * 是否自动统计数据,在最后追加一行统计数据总和
     * 用于在Excel底部添加合计行
     * 自动计算数值列的总和
     *
     * @return 是否统计数据
     */
    public boolean isStatistics() default false; // 定义是否统计数据属性，默认值为false

    /**
     * 导出类型（0数字 1字符串 2图片）
     * 用于指定单元格的数据类型
     * 影响Excel中的数据格式和显示方式
     *
     * @return 列类型枚举
     */
    public ColumnType cellType() default ColumnType.STRING; // 定义单元格类型属性，默认值为字符串类型

    /**
     * 导出列头背景颜色
     * 用于设置Excel表头的背景色
     * 使用POI库预定义的颜色索引
     *
     * @return 表头背景颜色
     */
    public IndexedColors headerBackgroundColor() default IndexedColors.GREY_50_PERCENT; // 定义表头背景色属性，默认值为50%灰色

    /**
     * 导出列头字体颜色
     * 用于设置Excel表头的字体颜色
     * 使用POI库预定义的颜色索引
     *
     * @return 表头字体颜色
     */
    public IndexedColors headerColor() default IndexedColors.WHITE; // 定义表头字体颜色属性，默认值为白色

    /**
     * 导出单元格背景颜色
     * 用于设置Excel数据单元格的背景色
     * 使用POI库预定义的颜色索引
     *
     * @return 单元格背景颜色
     */
    public IndexedColors backgroundColor() default IndexedColors.WHITE; // 定义单元格背景色属性，默认值为白色

    /**
     * 导出单元格字体颜色
     * 用于设置Excel数据单元格的字体颜色
     * 使用POI库预定义的颜色索引
     *
     * @return 单元格字体颜色
     */
    public IndexedColors color() default IndexedColors.BLACK; // 定义单元格字体颜色属性，默认值为黑色

    /**
     * 导出字段对齐方式
     * 用于设置Excel单元格的水平对齐方式
     * 支持左对齐、居中、右对齐等
     *
     * @return 对齐方式
     */
    public HorizontalAlignment align() default HorizontalAlignment.CENTER; // 定义对齐方式属性，默认值为居中对齐

    /**
     * 自定义数据处理器
     * 用于指定自定义的数据处理类
     * 可以实现复杂的数据转换逻辑
     *
     * @return 处理器类
     */
    public Class<?> handler() default ExcelHandlerAdapter.class; // 定义处理器类属性，默认值为适配器类

    /**
     * 自定义数据处理器参数
     * 用于向自定义处理器传递参数
     * 支持多个参数，以数组形式传递
     *
     * @return 处理器参数数组
     */
    public String[] args() default {}; // 定义处理器参数属性，默认值为空数组

    /**
     * 字段类型（0：导出导入；1：仅导出；2：仅导入）
     * 用于控制字段在Excel操作中的行为
     * ALL：既可以导出也可以导入
     * EXPORT：仅用于导出，导入时忽略
     * IMPORT：仅用于导入，导出时忽略
     *
     * @return 字段类型枚举
     */
    Type type() default Type.ALL; // 定义字段类型属性，默认值为全部类型

    /**
     * 字段类型枚举
     * 定义字段在Excel导入导出中的使用方式
     */
    public enum Type
    {
        ALL(0), // 既可以导出也可以导入
        EXPORT(1), // 仅用于导出
        IMPORT(2); // 仅用于导入

        private final int value; // 枚举值

        /**
         * 构造函数
         * 初始化枚举值
         *
         * @param value 枚举对应的整数值
         */
        Type(int value)
        {
            this.value = value; // 设置枚举值
        }

        /**
         * 获取枚举值
         * 返回枚举对应的整数值
         *
         * @return 枚举值
         */
        public int value()
        {
            return this.value; // 返回枚举值
        }
    }

    /**
     * 列类型枚举
     * 定义Excel单元格的数据类型
     */
    public enum ColumnType
    {
        NUMERIC(0), // 数字类型
        STRING(1), // 字符串类型
        IMAGE(2), // 图片类型
        TEXT(3); // 文本类型

        private final int value; // 枚举值

        /**
         * 构造函数
         * 初始化枚举值
         *
         * @param value 枚举对应的整数值
         */
        ColumnType(int value)
        {
            this.value = value; // 设置枚举值
        }

        /**
         * 获取枚举值
         * 返回枚举对应的整数值
         *
         * @return 枚举值
         */
        public int value()
        {
            return this.value; // 返回枚举值
        }
    }
}