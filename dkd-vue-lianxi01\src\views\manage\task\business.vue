<template>
  <div class="app-container">
    <!-- 工单状态统计图表 -->
    <el-card class="mb-4" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>运营工单状态统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 300px;"></div>
    </el-card>

    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="工单编号" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入工单编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工单状态" prop="taskStatus">
        <el-select
          v-model="queryParams.taskStatus"
          placeholder="请选择工单状态"
          clearable
        >
          <el-option
            v-for="dict in task_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:task:add']"
          >新增</el-button
        >
        <el-button type="primary" plain @click="openTaskConfig"
          >工单配置</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="taskList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="序号"
        type="index"
        width="50"
        align="center"
        prop="taskId"
      />
      <el-table-column label="工单编号" align="center" prop="taskCode" />
      <el-table-column label="设备编号" align="center" prop="innerCode" />
      <el-table-column
        label="工单类型"
        align="center"
        prop="taskType.typeName"
      />
      <el-table-column label="工单方式" align="center" prop="createType">
        <template #default="scope">
          <dict-tag :options="task_create_type" :value="scope.row.createType" />
        </template>
      </el-table-column>
      <el-table-column label="工单状态" align="center" prop="taskStatus">
        <template #default="scope">
          <dict-tag :options="task_status" :value="scope.row.taskStatus" />
        </template>
      </el-table-column>
      <el-table-column label="运营人员" align="center" prop="userName" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{
            parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openTaskDetailDialog(scope.row)"
            v-hasPermi="['manage:task:edit']"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加工单对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="taskRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="设备编号" prop="innerCode">
          <!-- <el-input
            v-model="form.innerCode"
            placeholder="请输入设备编号"
            @blur="handleCode"
          /> -->
          <el-select v-model="form.innerCode" placeholder="请选择设备" clearable @change="handleCode">
              <el-option v-for="item in vmList" :key="item.id" :label="item.innerCode" :value="item.innerCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="工单类型" prop="productTypeId">
          <el-select
            v-model="form.productTypeId"
            placeholder="请选择工单类型"
            clearable
          >
            <el-option
              v-for="dict in taskTypeList"
              :key="dict.typeId"
              :label="dict.typeName"
              :value="dict.typeId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="补货数量：" prop="details">
          <el-button type="text" @click="channelDetails">
            <el-icon> <List /> </el-icon>补货清单
          </el-button>
        </el-form-item>
        <el-form-item label="运营人员：" prop="userId">
          <el-select
            v-model="form.userId"
            placeholder="请选择"
            :filterable="true"
          >
            <el-option
              v-for="(item, index) in userList"
              :key="index"
              :label="item.userName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="desc">
          <el-input
            type="textarea"
            v-model="form.desc"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看详情组件 -->
    <DetailDialog
      :detailVisible="detailVisible"
      :taskId="taskId"
      :taskDada="form"
      :detailData="detailData"
      @getList="getList"
      @handleClose="handleClose"
      @handleAdd="handleAdd"
    ></DetailDialog>
    <!-- end -->
    <!-- 补货详情 -->
    <ReplenishmentDialog
      :channelVisible="channelVisible"
      :innerCode="form.innerCode"
      @getDetailList="getDetailList"
      @handleClose="channelDetailsClose"
    ></ReplenishmentDialog>
    <!-- end -->
    <!-- 工单配置 -->
    <TaskConfig
      :taskConfigVisible="taskConfigVisible"
      @handleClose="handleConfigClose"
    ></TaskConfig>
    <!-- end -->
  </div>
</template>

<script setup name="Task">
import {
  listTask,
  getTask,
  delTask,
  addTask,
  updateTask,
  getBusinessList,
  getTaskDetails,
  getTaskStatusStats,
} from '@/api/manage/task';
import { listTaskType } from '@/api/manage/taskType';
import { loadAllParams } from '@/api/page';
// 组件
import DetailDialog from './components/business-detail-dialog.vue'; //详情组件
import ReplenishmentDialog from './components/business-replenishment-dialog.vue'; //补货组件
import TaskConfig from './components/task-config.vue';
import { listVm } from '@/api/manage/vm';
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh } from '@element-plus/icons-vue';
const { proxy } = getCurrentInstance();
const { task_status, task_create_type } = proxy.useDict(
  'task_status',
  'task_create_type'
);

const taskList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const detailVisible = ref(false); //查看详情弹层显示/隐藏
const taskId = ref(null); //工单id
const taskDada = ref({}); //工单详情
const userList = ref([]); //运维人员
const channelVisible = ref(false); //补货弹层
const detailData = ref([]); //货道列表
const taskConfigVisible = ref(false); //工单配置弹层

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskCode: null,
    taskStatus: null,
    createType: null,
    innerCode: null,
    userName: null,
    regionId: null,
    desc: null,
    productTypeId: null,
    userId: null,
    addr: null,
    params: { isRepair: false },
  },
  rules: {
    innerCode: [
      { required: true, message: '设备编号不能为空', trigger: 'blur' },
    ],
    productTypeId: [
      { required: true, message: '设备类型不能为空', trigger: 'blur' },
    ],
    // details: [{ required: true, message: '补货数量不能为空', trigger: 'blur' }],

    userId: [{ required: true, message: '人员不能为空', trigger: 'blur' }],
    desc: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询运营工单列表 */
function getList() {
  loading.value = true;
  listTask(queryParams.value).then((response) => {
    taskList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/**查询设备列表 */
const vmList = ref([]);
function getVmList() {
  listVm(loadAllParams).then((response) => {
    vmList.value = response.rows;
  })
}
getVmList();

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    taskId: null,
    taskCode: null,
    taskStatus: null,
    createType: null,
    innerCode: null,
    userId: null,
    userName: null,
    regionId: null,
    desc: null,
    productTypeId: null,
    addr: null,
    createTime: null,
    updateTime: null,
    details: [],
  };
  proxy.resetForm('taskRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.taskId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd(val) {
  if (val === 'anew') {
    taskInfo();
    getUserList();
  } else {
    taskId.val = '';
  }
  reset();
  open.value = true;
  title.value = '添加运营工单';
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['taskRef'].validate((valid) => {
    if (valid) {
      const data = form.value;
      form.value = {
        innerCode: data.innerCode,
        userId: data.userId,
        productTypeId: data.productTypeId,
        desc: data.desc,
        createType: 1,
        details: data.details,
      };
      addTask(form.value).then((response) => {
        proxy.$modal.msgSuccess('新增成功');
        open.value = false;
        getList();
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _taskIds = row.taskId || ids.value;
  proxy.$modal
    .confirm('是否确认删除运营工单编号为"' + _taskIds + '"的数据项？')
    .then(function () {
      return delTask(_taskIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'manage/task/export',
    {
      ...queryParams.value,
    },
    `task_${new Date().getTime()}.xlsx`
  );
}

// 查询工单类型列表
const taskTypeList = ref([]);
function getTaskTypeList() {
  // 默认时获取所有得工单类型，需要用type区别开，1:运维工单类型，2:运营工单类型
  const page = {
    ...loadAllParams,
    type: 2,
  };
  listTaskType(page).then((response) => {
    taskTypeList.value = response.rows;
  });
}
// 填写设备编号后
const handleCode = () => {
  if (form.value.innerCode) {
    getUserList();
  }
};
// 获取运营人员列表
const getUserList = () => {
  getBusinessList(form.value.innerCode).then((response) => {
    userList.value = response.data;
  });
};
// 获取工单详情
const taskInfo = () => {
  let dataArr = [];
  let obj = {};
  getTask(taskId.value).then((response) => {
    form.value = response.data;
  });
  // 获取货道列表
  getTaskDetails(taskId.value).then((res) => {
    detailData.value = res.data;
    detailData.value.map((taskDetail) => {
      obj = {
        channelCode: taskDetail.channelCode,
        expectCapacity: taskDetail.expectCapacity,
        skuId: taskDetail.skuId,
        skuName: taskDetail.skuName,
        skuImage: taskDetail.skuImage,
      };
      dataArr.push(obj);
    });
    form.value.details = dataArr;
  });
};
// 查看详情
const openTaskDetailDialog = (row) => {
  taskId.value = row.taskId;
  taskInfo();
  detailVisible.value = true;
};
// 关闭详情弹层
const handleClose = () => {
  detailVisible.value = false;
};
// 补货清单
const channelDetails = () => {
  proxy.$refs['taskRef'].validateField('innerCode', (error) => {
    if (!error) {
      return;
    }
    channelVisible.value = true;
  });
};
// 关闭补货清单
const channelDetailsClose = () => {
  channelVisible.value = false;
};
// 获取货道清单数据
const getDetailList = (val) => {
  form.value.details = val;
};
// 打开工单配置弹层
const openTaskConfig = () => {
  taskConfigVisible.value = true;
};
// 关闭工单配置弹层
const handleConfigClose = () => {
  taskConfigVisible.value = false;
};
getTaskTypeList();

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;
    // 构建查询参数，只查询运营工单（isRepair: false）
    const params = {
      ...queryParams.value,
      params: { isRepair: false }
    };

    const response = await getTaskStatusStats(params);
    const data = response.data || [];

    // 处理数据
    const statusMap = {
      1: '待办',
      2: '进行中',
      3: '取消',
      4: '完成'
    };

    const dates = [];
    const seriesData = {
      '待办': [],
      '进行中': [],
      '取消': [],
      '完成': []
    };

    // 模拟最近7天的数据（实际项目中应该从后端获取）
    const today = new Date();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
      dates.push(dateStr);

      // 模拟数据（实际应该从response.data中获取对应日期的数据）
      seriesData['待办'].push(Math.floor(Math.random() * 20) + 5);
      seriesData['进行中'].push(Math.floor(Math.random() * 15) + 3);
      seriesData['取消'].push(Math.floor(Math.random() * 5) + 1);
      seriesData['完成'].push(Math.floor(Math.random() * 25) + 10);
    }

    updateChart(dates, seriesData);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(dates, seriesData) {
  if (!chartInstance.value) return;

  const option = {
    title: {
      text: '工单状态趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['待办', '进行中', '取消', '完成'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLabel: {
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '待办',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 2
        },
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#409EFF'
        },
        data: seriesData['待办']
      },
      {
        name: '进行中',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 2
        },
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#67C23A'
        },
        data: seriesData['进行中']
      },
      {
        name: '取消',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 2
        },
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#F56C6C'
        },
        data: seriesData['取消']
      },
      {
        name: '完成',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 2
        },
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#E6A23C'
        },
        data: seriesData['完成']
      }
    ]
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initChart();
  });
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

getList();
</script>
<style lang="scss" scoped src="./index.scss"></style>
<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
