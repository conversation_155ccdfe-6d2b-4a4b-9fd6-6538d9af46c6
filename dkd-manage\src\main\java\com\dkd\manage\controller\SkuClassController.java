package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.SkuClass;
import com.dkd.manage.service.ISkuClassService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 商品类型Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
// 定义REST控制器，处理商品类型管理相关的HTTP请求
@RestController
@RequestMapping("/manage/skuClass")
public class SkuClassController extends BaseController
{
    // 注入商品类型业务层接口
    @Autowired
    private ISkuClassService skuClassService;

    /**
     * 查询商品类型列表接口
     * 需要'manage:skuClass:list'权限
     * 请求路径：GET /manage/skuClass/list
     */
    @PreAuthorize("@ss.hasPermi('manage:skuClass:list')")
    @GetMapping("/list")
    public TableDataInfo list(SkuClass skuClass)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询商品类型数据
        List<SkuClass> list = skuClassService.selectSkuClassList(skuClass);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出商品类型列表接口
     * 需要'manage:skuClass:export'权限
     * 请求路径：POST /manage/skuClass/export
     */
    @PreAuthorize("@ss.hasPermi('manage:skuClass:export')")
    @Log(title = "商品类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SkuClass skuClass)
    {
        // 调用服务层方法查询需要导出的商品类型数据
        List<SkuClass> list = skuClassService.selectSkuClassList(skuClass);
        // 创建Excel工具类实例
        ExcelUtil<SkuClass> util = new ExcelUtil<SkuClass>(SkuClass.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "商品类型数据");
    }

    /**
     * 获取商品类型详细信息接口
     * 需要'manage:skuClass:query'权限
     * 请求路径：GET /manage/skuClass/{classId}
     */
    @PreAuthorize("@ss.hasPermi('manage:skuClass:query')")
    @GetMapping(value = "/{classId}")
    public AjaxResult getInfo(@PathVariable("classId") Long classId)
    {
        // 调用服务层方法获取商品类型详情
        return success(skuClassService.selectSkuClassByClassId(classId));
    }

    /**
     * 新增商品类型接口
     * 需要'manage:skuClass:add'权限
     * 请求路径：POST /manage/skuClass
     */
    @PreAuthorize("@ss.hasPermi('manage:skuClass:add')")
    @Log(title = "商品类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SkuClass skuClass)
    {
        // 调用服务层方法新增商品类型
        return toAjax(skuClassService.insertSkuClass(skuClass));
    }

    /**
     * 修改商品类型接口
     * 需要'manage:skuClass:edit'权限
     * 请求路径：PUT /manage/skuClass
     */
    @PreAuthorize("@ss.hasPermi('manage:skuClass:edit')")
    @Log(title = "商品类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SkuClass skuClass)
    {
        // 调用服务层方法修改商品类型信息
        return toAjax(skuClassService.updateSkuClass(skuClass));
    }

    /**
     * 删除商品类型接口
     * 需要'manage:skuClass:remove'权限
     * 请求路径：DELETE /manage/skuClass/{classIds}
     */
    @PreAuthorize("@ss.hasPermi('manage:skuClass:remove')")
    @Log(title = "商品类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{classIds}")
    public AjaxResult remove(@PathVariable Long[] classIds)
    {
        // 调用服务层方法删除指定ID的商品类型
        return toAjax(skuClassService.deleteSkuClassByClassIds(classIds));
    }
}



// @RestController
// @RequestMapping("/manage/skuClass")
// public class SkuClassController extends BaseController
// {
//     @Autowired
//     private ISkuClassService skuClassService;

//     /**
//      * 查询商品类型列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:skuClass:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SkuClass skuClass)
//     {
//         startPage();
//         List<SkuClass> list = skuClassService.selectSkuClassList(skuClass);
//         return getDataTable(list);
//     }

//     /**
//      * 导出商品类型列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:skuClass:export')")
//     @Log(title = "商品类型", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SkuClass skuClass)
//     {
//         List<SkuClass> list = skuClassService.selectSkuClassList(skuClass);
//         ExcelUtil<SkuClass> util = new ExcelUtil<SkuClass>(SkuClass.class);
//         util.exportExcel(response, list, "商品类型数据");
//     }

//     /**
//      * 获取商品类型详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:skuClass:query')")
//     @GetMapping(value = "/{classId}")
//     public AjaxResult getInfo(@PathVariable("classId") Long classId)
//     {
//         return success(skuClassService.selectSkuClassByClassId(classId));
//     }

//     /**
//      * 新增商品类型
//      */
//     @PreAuthorize("@ss.hasPermi('manage:skuClass:add')")
//     @Log(title = "商品类型", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody SkuClass skuClass)
//     {
//         return toAjax(skuClassService.insertSkuClass(skuClass));
//     }

//     /**
//      * 修改商品类型
//      */
//     @PreAuthorize("@ss.hasPermi('manage:skuClass:edit')")
//     @Log(title = "商品类型", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody SkuClass skuClass)
//     {
//         return toAjax(skuClassService.updateSkuClass(skuClass));
//     }

//     /**
//      * 删除商品类型
//      */
//     @PreAuthorize("@ss.hasPermi('manage:skuClass:remove')")
//     @Log(title = "商品类型", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{classIds}")
//     public AjaxResult remove(@PathVariable Long[] classIds)
//     {
//         return toAjax(skuClassService.deleteSkuClassByClassIds(classIds));
//     }
// }
