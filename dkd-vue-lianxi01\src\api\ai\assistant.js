// 导入封装的HTTP请求工具，提供统一的API调用方式
import request from '@/utils/request'

/**
 * RAG增强聊天接口
 * 调用智能体应用，自动判断是否使用专业知识库
 *
 * @param {Object} data 请求数据
 * @param {string} data.sessionId 会话ID，用于维护对话连续性
 * @param {string} data.message 用户输入的消息内容
 * @returns {Promise} 返回包含AI回答的Promise对象
 */
export function ragChat(data) {
  return request({
    url: '/ai/rag-chat', // 后端RAG聊天接口路径
    method: 'post', // 使用POST方法发送请求
    data: data, // 传递请求数据
    timeout: 60000 // 设置60秒超时，给AI足够的处理时间
  })
}

/**
 * 普通聊天接口
 * 直接调用阿里云百炼平台API，不使用本地智能路由
 *
 * @param {Object} data 请求数据
 * @param {string} data.sessionId 会话ID
 * @param {Object} data.input 输入对象
 * @param {Array} data.input.messages 消息列表
 * @returns {Promise} 返回包含AI回答的Promise对象
 */
export function chat(data) {
  return request({
    url: '/ai/chat', // 后端普通聊天接口路径
    method: 'post', // 使用POST方法发送请求
    data: data // 传递请求数据
  })
}

/**
 * 流式聊天接口
 * 使用Server-Sent Events (SSE) 协议实现实时流式响应
 *
 * @param {Object} data 请求数据
 * @param {string} data.sessionId 会话ID
 * @param {Object} data.input 输入对象
 * @returns {Promise} 返回流式响应的Promise对象
 */
export function streamChat(data) {
  return request({
    url: '/ai/chat-stream', // 后端流式聊天接口路径
    method: 'post', // 使用POST方法发送请求
    data: data, // 传递请求数据
    responseType: 'stream' // 指定响应类型为流式数据
  })
}

/**
 * 知识库搜索接口
 * 在智能体应用的知识库中搜索相关信息
 *
 * @param {Object} data 搜索请求数据
 * @param {string} data.query 搜索关键词
 * @returns {Promise} 返回搜索结果的Promise对象
 */
export function knowledgeSearch(data) {
  return request({
    url: '/ai/knowledge-search', // 后端知识库搜索接口路径
    method: 'post', // 使用POST方法发送请求
    data: data // 传递搜索数据
  })
}

/**
 * 获取智能体应用信息接口
 * 获取智能体应用的功能介绍和能力说明
 *
 * @param {Object} data 请求数据
 * @param {string} data.query 查询关键词，用于获取相关功能介绍
 * @returns {Promise} 返回智能体应用信息的Promise对象
 */
export function getAgentInfo(data) {
  return request({
    url: '/ai/agent-info', // 后端智能体应用信息接口路径
    method: 'post', // 使用POST方法发送请求
    data: data // 传递查询数据
  })
}

/**
 * 获取智能体应用状态接口
 * 检查智能体应用的运行状态和配置信息
 *
 * @returns {Promise} 返回智能体应用状态信息的Promise对象
 */
export function getKnowledgeStatus() {
  return request({
    url: '/ai/agent-status', // 后端智能体应用状态接口路径（已更新）
    method: 'get' // 使用GET方法获取状态信息
  })
}

/**
 * 清空会话历史接口
 * 清除指定会话的所有历史消息记录
 *
 * @param {Object} data 请求数据
 * @param {string} data.sessionId 要清空的会话ID
 * @returns {Promise} 返回清空操作结果的Promise对象
 */
export function clearSession(data) {
  return request({
    url: '/ai/clear-session', // 后端清空会话接口路径
    method: 'post', // 使用POST方法发送请求
    data: data // 传递会话ID数据
  })
}
