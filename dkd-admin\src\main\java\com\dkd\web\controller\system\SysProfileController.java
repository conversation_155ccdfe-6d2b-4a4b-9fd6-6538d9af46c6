package com.dkd.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.dkd.common.annotation.Log;
import com.dkd.common.config.RuoYiConfig;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysUser;
import com.dkd.common.core.domain.model.LoginUser;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.SecurityUtils;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.file.FileUploadUtils;
import com.dkd.common.utils.file.MimeTypeUtils;
import com.dkd.framework.web.service.TokenService;
import com.dkd.system.service.ISysUserService;

/**
 * 个人信息 业务处理
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块用户个人信息相关的HTTP请求
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController
{
    // 注入用户业务层接口
    @Autowired
    private ISysUserService userService;

    // 注入Token服务类，用于更新缓存中的用户信息
    @Autowired
    private TokenService tokenService;

    /**
     * 获取当前登录用户个人信息接口
     * 请求路径：GET /system/user/profile
     */
    @GetMapping
    public AjaxResult profile()
    {
        // 获取当前登录用户对象
        LoginUser loginUser = getLoginUser();
        // 获取用户基本信息
        SysUser user = loginUser.getUser();
        // 创建成功响应对象
        AjaxResult ajax = AjaxResult.success(user);
        // 添加用户角色组信息
        ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        // 添加用户岗位组信息
        ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
        // 返回响应结果
        return ajax;
    }

    /**
     * 修改当前登录用户个人信息接口
     * 请求路径：PUT /system/user/profile
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user)
    {
        // 获取当前登录用户对象
        LoginUser loginUser = getLoginUser();
        // 获取当前用户信息
        SysUser currentUser = loginUser.getUser();
        // 更新用户基本信息字段
        currentUser.setNickName(user.getNickName());
        currentUser.setEmail(user.getEmail());
        currentUser.setPhonenumber(user.getPhonenumber());
        currentUser.setSex(user.getSex());

        // 校验手机号是否唯一
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
        }
        // 校验邮箱是否唯一
        if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
        }

        // 调用服务层方法更新用户信息
        if (userService.updateUserProfile(currentUser) > 0)
        {
            // 更新缓存中的用户信息
            tokenService.setLoginUser(loginUser);
            // 返回操作成功结果
            return success();
        }
        // 返回操作失败结果
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置当前登录用户密码接口
     * 请求路径：PUT /system/user/profile/updatePwd
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword)
    {
        // 获取当前登录用户对象
        LoginUser loginUser = getLoginUser();
        // 获取用户名和加密后的当前密码
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();

        // 校验旧密码是否正确
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return error("修改密码失败，旧密码错误");
        }
        // 校验新密码是否与旧密码相同
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return error("新密码不能与旧密码相同");
        }

        // 加密新密码
        newPassword = SecurityUtils.encryptPassword(newPassword);

        // 调用服务层方法重置密码
        if (userService.resetUserPwd(userName, newPassword) > 0)
        {
            // 更新缓存中的用户密码
            loginUser.getUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            // 返回操作成功结果
            return success();
        }
        // 返回操作失败结果
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 用户头像上传接口
     * 请求路径：POST /system/user/profile/avatar
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception
    {
        // 判断文件是否为空
        if (!file.isEmpty())
        {
            // 获取当前登录用户对象
            LoginUser loginUser = getLoginUser();
            // 执行文件上传操作，获取头像路径
            String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
            // 调用服务层方法更新用户头像
            if (userService.updateUserAvatar(loginUser.getUsername(), avatar))
            {
                // 构建成功响应对象
                AjaxResult ajax = AjaxResult.success();
                // 添加头像URL到响应数据
                ajax.put("imgUrl", avatar);
                // 更新缓存中的用户头像
                loginUser.getUser().setAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                // 返回响应结果
                return ajax;
            }
        }
        // 返回操作失败结果
        return error("上传图片异常，请联系管理员");
    }
}






// @RestController
// @RequestMapping("/system/user/profile")
// public class SysProfileController extends BaseController
// {
//     @Autowired
//     private ISysUserService userService;

//     @Autowired
//     private TokenService tokenService;

//     /**
//      * 个人信息
//      */
//     @GetMapping
//     public AjaxResult profile()
//     {
//         LoginUser loginUser = getLoginUser();
//         SysUser user = loginUser.getUser();
//         AjaxResult ajax = AjaxResult.success(user);
//         ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
//         ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
//         return ajax;
//     }

//     /**
//      * 修改用户
//      */
//     @Log(title = "个人信息", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult updateProfile(@RequestBody SysUser user)
//     {
//         LoginUser loginUser = getLoginUser();
//         SysUser currentUser = loginUser.getUser();
//         currentUser.setNickName(user.getNickName());
//         currentUser.setEmail(user.getEmail());
//         currentUser.setPhonenumber(user.getPhonenumber());
//         currentUser.setSex(user.getSex());
//         if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(currentUser))
//         {
//             return error("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
//         }
//         if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser))
//         {
//             return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
//         }
//         if (userService.updateUserProfile(currentUser) > 0)
//         {
//             // 更新缓存用户信息
//             tokenService.setLoginUser(loginUser);
//             return success();
//         }
//         return error("修改个人信息异常，请联系管理员");
//     }

//     /**
//      * 重置密码
//      */
//     @Log(title = "个人信息", businessType = BusinessType.UPDATE)
//     @PutMapping("/updatePwd")
//     public AjaxResult updatePwd(String oldPassword, String newPassword)
//     {
//         LoginUser loginUser = getLoginUser();
//         String userName = loginUser.getUsername();
//         String password = loginUser.getPassword();
//         if (!SecurityUtils.matchesPassword(oldPassword, password))
//         {
//             return error("修改密码失败，旧密码错误");
//         }
//         if (SecurityUtils.matchesPassword(newPassword, password))
//         {
//             return error("新密码不能与旧密码相同");
//         }
//         newPassword = SecurityUtils.encryptPassword(newPassword);
//         if (userService.resetUserPwd(userName, newPassword) > 0)
//         {
//             // 更新缓存用户密码
//             loginUser.getUser().setPassword(newPassword);
//             tokenService.setLoginUser(loginUser);
//             return success();
//         }
//         return error("修改密码异常，请联系管理员");
//     }

//     /**
//      * 头像上传
//      */
//     @Log(title = "用户头像", businessType = BusinessType.UPDATE)
//     @PostMapping("/avatar")
//     public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception
//     {
//         if (!file.isEmpty())
//         {
//             LoginUser loginUser = getLoginUser();
//             String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
//             if (userService.updateUserAvatar(loginUser.getUsername(), avatar))
//             {
//                 AjaxResult ajax = AjaxResult.success();
//                 ajax.put("imgUrl", avatar);
//                 // 更新缓存用户头像
//                 loginUser.getUser().setAvatar(avatar);
//                 tokenService.setLoginUser(loginUser);
//                 return ajax;
//             }
//         }
//         return error("上传图片异常，请联系管理员");
//     }
// }
