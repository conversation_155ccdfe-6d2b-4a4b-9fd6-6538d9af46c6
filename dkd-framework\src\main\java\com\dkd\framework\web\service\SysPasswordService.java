// 包声明：定义当前类所属的包路径
package com.dkd.framework.web.service;

// 导入Java并发包中的时间单位枚举
import java.util.concurrent.TimeUnit;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring配置值注解
import org.springframework.beans.factory.annotation.Value;
// 导入Spring Security认证接口
import org.springframework.security.core.Authentication;
// 导入Spring组件注解
import org.springframework.stereotype.Component;
// 导入缓存常量类
import com.dkd.common.constant.CacheConstants;
// 导入系统用户实体类
import com.dkd.common.core.domain.entity.SysUser;
// 导入Redis缓存操作类
import com.dkd.common.core.redis.RedisCache;
// 导入用户密码不匹配异常类
import com.dkd.common.exception.user.UserPasswordNotMatchException;
// 导入用户密码重试次数超限异常类
import com.dkd.common.exception.user.UserPasswordRetryLimitExceedException;
// 导入安全工具类
import com.dkd.common.utils.SecurityUtils;
// 导入认证上下文持有者
import com.dkd.framework.security.context.AuthenticationContextHolder;

/**
 * 登录密码方法
 * 这个服务类负责处理用户密码相关的验证逻辑，包括：
 * 1. 密码错误次数统计
 * 2. 账户锁定机制
 * 3. 密码匹配验证
 * 4. 登录记录缓存清理
 *
 * <AUTHOR>
 */
@Component // 将此类标记为Spring组件，由Spring容器管理
public class SysPasswordService
{
    // 注入Redis缓存服务，用于存储密码错误次数
    @Autowired
    private RedisCache redisCache;

    // 从配置文件中注入密码最大重试次数，默认值在application.yml中配置
    @Value(value = "${user.password.maxRetryCount}")
    private int maxRetryCount;

    // 从配置文件中注入账户锁定时间（分钟），默认值在application.yml中配置
    @Value(value = "${user.password.lockTime}")
    private int lockTime;

    /**
     * 登录账户密码错误次数缓存键名
     * 生成用于在Redis中存储密码错误次数的缓存键
     *
     * @param username 用户名，用于构造唯一的缓存键
     * @return 缓存键key，格式为：pwd_err_cnt: + 用户名
     */
    private String getCacheKey(String username)
    {
        // 返回密码错误次数缓存键，前缀 + 用户名
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    /**
     * 验证用户密码
     * 这是密码验证的核心方法，包含密码错误次数统计和账户锁定逻辑
     *
     * @param user 系统用户对象，包含用户的基本信息和加密后的密码
     */
    public void validate(SysUser user)
    {
        // 从认证上下文中获取当前的认证令牌
        Authentication usernamePasswordAuthenticationToken = AuthenticationContextHolder.getContext();
        // 从认证令牌中获取用户名
        String username = usernamePasswordAuthenticationToken.getName();
        // 从认证令牌中获取用户输入的原始密码
        String password = usernamePasswordAuthenticationToken.getCredentials().toString();

        // 从Redis缓存中获取该用户的密码错误次数
        Integer retryCount = redisCache.getCacheObject(getCacheKey(username));

        // 如果缓存中没有记录，说明是第一次尝试登录，初始化错误次数为0
        if (retryCount == null)
        {
            retryCount = 0; // 初始化重试次数
        }

        // 检查密码错误次数是否已达到最大限制
        if (retryCount >= Integer.valueOf(maxRetryCount).intValue())
        {
            // 抛出密码重试次数超限异常，触发账户锁定
            throw new UserPasswordRetryLimitExceedException(maxRetryCount, lockTime);
        }

        // 验证用户输入的密码是否与数据库中的密码匹配
        if (!matches(user, password))
        {
            // 密码不匹配，增加错误次数
            retryCount = retryCount + 1;
            // 将新的错误次数存入Redis缓存，设置过期时间为锁定时间
            redisCache.setCacheObject(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
            // 抛出密码不匹配异常
            throw new UserPasswordNotMatchException();
        }
        else // 密码匹配成功
        {
            // 清除该用户的登录错误记录缓存
            clearLoginRecordCache(username);
        }
    }

    /**
     * 验证密码是否匹配
     * 比较用户输入的原始密码与数据库中存储的加密密码是否匹配
     *
     * @param user 系统用户对象，包含加密后的密码
     * @param rawPassword 用户输入的原始密码（未加密）
     * @return 密码是否匹配，true表示匹配，false表示不匹配
     */
    public boolean matches(SysUser user, String rawPassword)
    {
        // 使用安全工具类比较原始密码和加密密码
        return SecurityUtils.matchesPassword(rawPassword, user.getPassword());
    }

    /**
     * 清除登录记录缓存
     * 用户登录成功后，清除该用户的密码错误次数记录
     *
     * @param loginName 登录用户名，用于构造缓存键
     */
    public void clearLoginRecordCache(String loginName)
    {
        // 检查Redis中是否存在该用户的错误次数记录
        if (redisCache.hasKey(getCacheKey(loginName)))
        {
            // 删除该用户的密码错误次数缓存
            redisCache.deleteObject(getCacheKey(loginName));
        }
    }
} // 类结束标记
