<template>
  <div class="login">
    <div class="login-bg"></div>
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <div class="login-tabs">
        <div 
          :class="['tab-item', activeTab === 'account' ? 'active' : '']"
          @click="activeTab = 'account'"
        >账号登录</div>
        <div 
          :class="['tab-item', activeTab === 'phone' ? 'active' : '']"
          @click="activeTab = 'phone'"
        >手机号登录</div>
      </div>

      <template v-if="activeTab === 'account'">
        <h3 class="title">智能售货机管理系统</h3>
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="账号"
          >
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img"/>
          </div>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      </template>

      <template v-else>
        <h3 class="title">智能售货机管理系统</h3>
        <el-form-item prop="phone">
          <el-input
            v-model="loginForm.phone"
            placeholder="手机号"
            size="large"
            maxlength="11"
          >
            <template #prefix><svg-icon icon-class="phone" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="smsCode">
          <el-input
            v-model="loginForm.smsCode"
            placeholder="短信验证码"
            size="large"
            style="width: 60%"
          >
            <template #prefix><svg-icon icon-class="message" class="el-input__icon input-icon" /></template>
          </el-input>
          <el-button 
            style="width: 35%; margin-left: 5%"
            size="large"
            :disabled="smsCountdown > 0"
            @click="sendSmsCode"
          >
            {{ smsCountdown ? `${smsCountdown}秒后重发` : '获取验证码' }}
          </el-button>
        </el-form-item>
      </template>

      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width:100%;"
          @click.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="width: 100%; display: flex; justify-content: flex-end; margin-top: 10px;" v-if="register">
          <router-link class="link-type" :to="'/register'" >还没有账号？立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>

    <div class="el-login-footer">
      <span></span>
    </div>
  </div>
</template>

<script setup>

import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { getCodeImg, sendSms } from "@/api/login";
import useUserStore from '@/store/modules/user';
import { ElMessage } from 'element-plus';

const userStore = useUserStore()
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const activeTab = ref('account') // 当前激活的选项卡
const smsCountdown = ref(0) // 短信验证码倒计时
let smsTimer = null // 添加timer变量

const loginForm = ref({
  username: "",
  password: "",
  phone: "",
  smsCode: "",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
  phone: [
    { required: true, trigger: "blur", message: "请输入手机号码" },
    { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确" }
  ],
  smsCode: [
    { required: true, trigger: "blur", message: "请输入短信验证码" }
  ]
};

// 新增发送短信验证码方法
const sendSmsCode = async () => {
  try {
    if (!/^1[3-9]\d{9}$/.test(loginForm.value.phone)) {
      ElMessage.warning('请输入正确的手机号码')
      return
    }
    
    // 如果倒计时还在进行，不允许重复发送
    if (smsCountdown.value > 0) {
      return
    }
    
    // 调用后端短信接口
    const res = await sendSms({ phone: loginForm.value.phone })
    
    if (res.code === 200) {
      ElMessage.success('验证码已发送')
      // 启动倒计时
      smsCountdown.value = 60
      smsTimer = setInterval(() => {
        smsCountdown.value--
        if (smsCountdown.value <= 0) {
          clearInterval(smsTimer)
          smsTimer = null
        }
      }, 1000)
    } else {
      ElMessage.error(res.msg || '验证码发送失败')
    }
  } catch (error) {
    console.error('发送短信验证码失败:', error)
    ElMessage.error('验证码发送失败，请稍后重试')
  }
}

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      
      // 根据当前选项卡选择登录方式
      if (activeTab.value === 'account') {
        // 账号密码登录
        // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
        if (loginForm.value.rememberMe) {
          Cookies.set("username", loginForm.value.username, { expires: 30 });
          Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
          Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
        } else {
          // 否则移除
          Cookies.remove("username");
          Cookies.remove("password");
          Cookies.remove("rememberMe");
        }
        // 调用action的登录方法
        userStore.login(loginForm.value).then(() => {
          const query = route.query;
          const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur];
            }
            return acc;
          }, {});
          router.push({ path: redirect.value || "/", query: otherQueryParams });
        }).catch(() => {
          loading.value = false;
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode();
          }
        });
      } else {
        // 手机号登录
        const phoneLoginData = {
          phone: loginForm.value.phone,
          smsCode: loginForm.value.smsCode
        };
        
        // 调用手机号登录方法
        userStore.phoneLogin(phoneLoginData).then(() => {
          const query = route.query;
          const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur];
            }
            return acc;
          }, {});
          router.push({ path: redirect.value || "/", query: otherQueryParams });
        }).catch(() => {
          loading.value = false;
        });
      }
    }
  });
}

function getCode() {
  //在获取验证码的方法中接收注册开关状态
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    register.value = res.register ?? true; // 改为默认显示
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login {
  display: grid;
  grid-template-columns: 60% 40%; // 图片60% 表单40%
  height: 100vh;

  .login-form {
    width: 100%;
    height: 100%;
    padding: 20vh 15% 0;
    background: #fff;
  }

  .login-bg {
    background: url("../assets/images/login-background1.png") no-repeat center/cover;
  }
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}
// 在style区块添加（第246行后）
.link-type {
  color: #409EFF;
  font-size: 14px;
  &:hover {
    color: #66b1ff;
  }
}
.login-tabs {
  display: flex;
  margin: -10px 0 20px 0;
  border-bottom: 1px solid #eee;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 15px 0;
    cursor: pointer;
    color: #606266;
    font-size: 16px;
    transition: all 0.3s;

    &.active {
      color: #409EFF;
      border-bottom: 2px solid #409EFF;
      font-weight: 500;
    }

    &:hover {
      background-color: rgba(64, 158, 255, 0.05);
    }
  }
}

</style>
