package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.dkd.manage.domain.dto.ChannelConfigDto;
import com.dkd.manage.domain.vo.ChannelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Sku;
import com.dkd.manage.service.ISkuService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
// 定义REST控制器，处理商品管理相关的HTTP请求
@RestController
@RequestMapping("/manage/sku")
public class SkuController extends BaseController
{
    // 注入商品业务层接口
    @Autowired
    private ISkuService skuService;

    /**
     * 查询商品列表接口
     * 需要'manage:sku:list'权限
     * 请求路径：GET /manage/sku/list
     */
    @PreAuthorize("@ss.hasPermi('manage:sku:list')")
    @GetMapping("/list")
    public TableDataInfo list(Sku sku)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询商品数据
        List<Sku> list = skuService.selectSkuList(sku);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 获取商品详细信息接口
     * 需要'manage:sku:query'权限
     * 请求路径：GET /manage/sku/{skuId}
     */
    @PreAuthorize("@ss.hasPermi('manage:sku:query')")
    @GetMapping(value = "/{skuId}")
    public AjaxResult getInfo(@PathVariable("skuId") Long skuId)
    {
        // 调用服务层方法获取商品详情
        return success(skuService.selectSkuBySkuId(skuId));
    }

    /**
     * 新增商品接口
     * 需要'manage:sku:add'权限
     * 请求路径：POST /manage/sku
     */
    @PreAuthorize("@ss.hasPermi('manage:sku:add')")
    @Log(title = "商品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Sku sku)
    {
        // 调用服务层方法新增商品
        return toAjax(skuService.insertSku(sku));
    }

    /**
     * 修改商品接口
     * 需要'manage:sku:edit'权限
     * 请求路径：PUT /manage/sku
     */
    @PreAuthorize("@ss.hasPermi('manage:sku:edit')")
    @Log(title = "商品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Sku sku)
    {
        // 调用服务层方法修改商品信息
        return toAjax(skuService.updateSku(sku));
    }

    /**
     * 删除商品接口
     * 需要'manage:sku:remove'权限
     * 请求路径：DELETE /manage/sku/{skuIds}
     */
    @PreAuthorize("@ss.hasPermi('manage:sku:remove')")
    @Log(title = "商品管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{skuIds}")
    public AjaxResult remove(@PathVariable Long[] skuIds)
    {
        // 调用服务层方法删除指定ID的商品
        return toAjax(skuService.deleteSkuBySkuIds(skuIds));
    }

    /**
     * 导入商品列表接口
     * 需要'manage:sku:add'权限
     * 请求路径：POST /manage/sku/import
     */
    @PreAuthorize("@ss.hasPermi('manage:sku:add')")
    @Log(title = "商品管理", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult excelImport(MultipartFile file, boolean updateSupport) throws Exception {
        // 创建Excel工具类实例
        ExcelUtil<Sku> util = new ExcelUtil<Sku>(Sku.class);
        // 使用标准Excel导入方法读取上传的Excel文件并解析为商品对象列表
        List<Sku> skuList = util.importExcel(file.getInputStream());
        // 获取当前操作用户名
        String operName = getUsername();
        // 调用服务层方法导入商品数据
        String message = skuService.importSku(skuList, updateSupport, operName);
        // 返回导入结果信息
        return success(message);
    }

    /**
     * 下载商品导入模板接口
     * 请求路径：POST /manage/sku/importTemplate
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        // 创建Excel工具类实例
        ExcelUtil<Sku> util = new ExcelUtil<Sku>(Sku.class);
        // 导出空模板供用户下载
        util.importTemplateExcel(response, "商品数据");
    }

    /**
     * 导出商品列表接口
     * 需要'manage:sku:export'权限
     * 请求路径：POST /manage/sku/export
     */
    @PreAuthorize("@ss.hasPermi('manage:sku:export')")
    @Log(title = "商品管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Sku sku) {
        // 调用服务层方法查询需要导出的商品数据
        List<Sku> list = skuService.selectSkuList(sku);
        // 创建Excel工具类实例
        ExcelUtil<Sku> util = new ExcelUtil<Sku>(Sku.class);
        // 使用EasyExcel将数据导出为Excel文件并下载
        util.exportEasyExcel(response, list, "商品管理数据");
    }
}







// @RestController
// @RequestMapping("/manage/sku")
// public class SkuController extends BaseController
// {
//     @Autowired
//     private ISkuService skuService;

//     /**
//      * 查询商品管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:sku:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Sku sku)
//     {
//         startPage();
//         List<Sku> list = skuService.selectSkuList(sku);
//         return getDataTable(list);
//     }



//     /**
//      * 获取商品管理详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:sku:query')")
//     @GetMapping(value = "/{skuId}")
//     public AjaxResult getInfo(@PathVariable("skuId") Long skuId)
//     {
//         return success(skuService.selectSkuBySkuId(skuId));
//     }

//     /**
//      * 新增商品管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:sku:add')")
//     @Log(title = "商品管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Sku sku)
//     {
//         return toAjax(skuService.insertSku(sku));
//     }

//     /**
//      * 修改商品管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:sku:edit')")
//     @Log(title = "商品管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Sku sku)
//     {
//         return toAjax(skuService.updateSku(sku));
//     }

//     /**
//      * 删除商品管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:sku:remove')")
//     @Log(title = "商品管理", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{skuIds}")
//     public AjaxResult remove(@PathVariable Long[] skuIds)
//     {
//         return toAjax(skuService.deleteSkuBySkuIds(skuIds));
//     }

//     /**
//      * 导入商品管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:sku:add')")
//     @Log(title = "商品管理", businessType = BusinessType.IMPORT)
//     @PostMapping("/import")
//     public AjaxResult excelImport(MultipartFile file) throws Exception {
//         ExcelUtil<Sku> util = new ExcelUtil<Sku>(Sku.class);
//         // 使用EasyExcel提供的方法导入数据
//         List<Sku> skuList = util.importEasyExcel(file.getInputStream());
//         return toAjax(skuService.insertSkus(skuList));
//     }

//     /**
//      * 导出商品管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:sku:export')")
//     @Log(title = "商品管理", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Sku sku) {
//         List<Sku> list = skuService.selectSkuList(sku);
//         ExcelUtil<Sku> util = new ExcelUtil<Sku>(Sku.class);
//         // 使用EasyExcel提供的方法导出数据
//         util.exportEasyExcel(response, list, "商品管理数据");
//     }






// }
