package com.dkd.manage.service.impl;

import java.util.List;
import javax.validation.Validator;

import com.dkd.common.exception.ServiceException;
import com.dkd.common.utils.DateUtils;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.bean.BeanValidators;
import com.dkd.manage.service.IChannelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.manage.mapper.SkuMapper;
import com.dkd.manage.domain.Sku;
import com.dkd.manage.service.ISkuService;

/**
 * 商品管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class SkuServiceImpl implements ISkuService
{
    private static final Logger log = LoggerFactory.getLogger(SkuServiceImpl.class);

    @Autowired
    private SkuMapper skuMapper;

    @Autowired
    private IChannelService channelService;

    @Autowired
    protected Validator validator;

    /**
     * 查询商品管理
     * 
     * @param skuId 商品管理主键
     * @return 商品管理
     */
    @Override
    public Sku selectSkuBySkuId(Long skuId)
    {
        return skuMapper.selectSkuBySkuId(skuId);
    }

    /**
     * 查询商品管理列表
     * 
     * @param sku 商品管理
     * @return 商品管理
     */
    @Override
    public List<Sku> selectSkuList(Sku sku)
    {
        return skuMapper.selectSkuList(sku);
    }

    /**
     * 新增商品管理
     * 
     * @param sku 商品管理
     * @return 结果
     */
    @Override
    public int insertSku(Sku sku)
    {
        sku.setCreateTime(DateUtils.getNowDate());
        return skuMapper.insertSku(sku);
    }

    /**
     * 修改商品管理
     * 
     * @param sku 商品管理
     * @return 结果
     */
    @Override
    public int updateSku(Sku sku)
    {
        sku.setUpdateTime(DateUtils.getNowDate());
        return skuMapper.updateSku(sku);
    }

    /**
     * 批量删除商品管理
     * 
     * @param skuIds 需要删除的商品管理主键
     * @return 结果
     */
    @Override
    public int deleteSkuBySkuIds(Long[] skuIds)
    {
        //1. 判断商品的id集合是否有关联货道
        int count = channelService.countChannelBySkuIds(skuIds);
        if(count>0){
            throw new ServiceException("此商品被货道关联，无法删除");
        }
        //2. 没有关联货道才能删除
        return skuMapper.deleteSkuBySkuIds(skuIds);
    }

    /**
     * 删除商品管理信息
     * 
     * @param skuId 商品管理主键
     * @return 结果
     */
    @Override
    public int deleteSkuBySkuId(Long skuId)
    {

        return skuMapper.deleteSkuBySkuId(skuId);
    }

    /**
     * 批量新增商品管理
     * @param skuList
     * @return 结果
     */
    @Override
    public int insertSkus(List<Sku> skuList) {
        return skuMapper.insertSkus(skuList);
    }

    /**
     * 导入商品数据
     *
     * @param skuList 商品数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importSku(List<Sku> skuList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(skuList) || skuList.size() == 0)
        {
            throw new ServiceException("导入商品数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (Sku sku : skuList)
        {
            try
            {
                // 验证是否存在这个商品（通过商品名称）
                Sku existingSku = skuMapper.selectSkuBySkuName(sku.getSkuName());
                if (StringUtils.isNull(existingSku))
                {
                    // 验证数据
                    BeanValidators.validateWithException(validator, sku);
                    sku.setCreateBy(operName);
                    sku.setCreateTime(DateUtils.getNowDate());
                    skuMapper.insertSku(sku);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、商品 " + sku.getSkuName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    // 更新现有商品
                    BeanValidators.validateWithException(validator, sku);
                    sku.setSkuId(existingSku.getSkuId());
                    sku.setUpdateBy(operName);
                    sku.setUpdateTime(DateUtils.getNowDate());
                    skuMapper.updateSku(sku);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、商品 " + sku.getSkuName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、商品 " + sku.getSkuName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、商品 " + sku.getSkuName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
