<template>
  <div class="app-container">
    <!-- 合作商点位分布统计图表 -->
    <el-card class="mb-4" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>合作商点位分布统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新统计
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 450px;"></div>
    </el-card>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="合作商名称" label-width="95" prop="partnerName">
        <el-input
          v-model="queryParams.partnerName"
          placeholder="请输入合作商名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:partner:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:partner:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:partner:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:partner:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="partnerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="合作商名称" align="center" prop="partnerName" />
      <el-table-column label="账号" align="center" prop="account" />
      <el-table-column label="设备数量" align="center" prop="nodeCount" />
      <el-table-column label="分成比例" align="center" prop="profitRatio" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="resetPwd(scope.row)" v-hasPermi="['manage:partner:edit']">重置密码</el-button>
          <el-button link type="primary"  @click="getPartnerInfo(scope.row)" v-hasPermi="['manage:partner:query']">查看详情</el-button>
          <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['manage:partner:edit']">修改</el-button>
          <el-button link type="primary"  @click="handleDelete(scope.row)" v-hasPermi="['manage:partner:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改合作商管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="partnerRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="合作商名称" prop="partnerName">
          <el-input v-model="form.partnerName" placeholder="请输入合作商名称" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="创建时间" prop="contactPhone" v-if="form.id!=null">
          {{ form.createTime  }}
        </el-form-item>
        <el-form-item label="分成比例" prop="profitRatio">
          <el-input v-model="form.profitRatio" placeholder="请输入分成比例" />
        </el-form-item>
        <el-form-item label="账号" prop="account" v-if="form.id==null">
          <el-input v-model="form.account" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="form.id==null">
          <el-input v-model="form.password" type="password" placeholder="请输入密码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 查看合作商管理详情对话框 -->
    <el-dialog :title="title" v-model="open1" width="500px" append-to-body>
      <el-card>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合作商名称">{{ form.partnerName }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ form.contactPerson }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ form.contactPhone }}</el-descriptions-item>
          <el-descriptions-item label="分成比例">{{ form.profitRatio }}%</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-dialog>
    <!-- ... existing code ... -->

    

  </div>
</template>

<script setup name="Partner">
import { listPartner, getPartner, delPartner, addPartner, updatePartner, resetPartnerPwd, getPartnerNodeStats } from "@/api/manage/partner";
import { listNode } from "@/api/manage/node";
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const partnerList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    partnerName: null,
  },
  rules: {
    partnerName: [
      { required: true, message: "合作商名称不能为空", trigger: "blur" }
    ],
    contactPerson: [
      { required: true, message: "联系人不能为空", trigger: "blur" }
    ],
    contactPhone: [
      { required: true, message: "联系电话不能为空", trigger: "blur" }
    ],
    profitRatio: [
      { required: true, message: "分成比例不能为空", trigger: "blur" }
    ],
    account: [
      { required: true, message: "账号不能为空", trigger: "blur" }
    ],
    password: [
      { required: true, message: "密码不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询合作商管理列表 */
function getList() {
  loading.value = true;
  listPartner(queryParams.value).then(response => {
    partnerList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 刷新图表数据
    if (chartInstance.value) {
      loadChartData();
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    partnerName: null,
    contactPerson: null,
    contactPhone: null,
    profitRatio: null,
    account: null,
    password: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    remark: null
  };
  proxy.resetForm("partnerRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加合作商管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getPartner(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改合作商管理";
  });
}

const open1 = ref(false);
/** 查看详情 */
function getPartnerInfo(row) {
  reset();
  const _id = row.id
  getPartner(_id).then(response => {
    form.value = response.data;
    open1.value = true;
    title.value = "查看合作商详情";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["partnerRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updatePartner(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPartner(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除合作商管理编号为"' + _ids + '"的数据项？').then(function() {
    return delPartner(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 重置密码 */
function resetPwd(row) {
  const _id = row.id;
  proxy.$modal.confirm('你确定要重置该合作商密码吗？').then(function() {
    return resetPartnerPwd(_id);
  }).then(() => {
    proxy.$modal.msgSuccess("重置成功");
  }).catch(() => {});
}

// /* 重置合作商密码 */
// function resetPwd(row) {
//     proxy.$modal.confirm('你确定要重置该合作商密码吗？').then(function () {
//         return resetPartnerPwd(row.id);
//     }).then(() => {
//         proxy.$modal.msgSuccess("重置成功");
//     }).catch(() => { });
// }

/** 导出按钮操作 */
function handleExport() {
  proxy.download('manage/partner/export', {
    ...queryParams.value
  }, `partner_${new Date().getTime()}.xlsx`)
}

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    // 获取所有合作商数据（不分页）
    const allPartnerParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数值来获取所有数据
      partnerName: null,
    };

    const allPartnerResponse = await listPartner(allPartnerParams);
    const allPartnerList = allPartnerResponse.rows || [];

    // 获取所有点位数据来统计每个合作商的点位数量
    const allNodeParams = {
      pageNum: 1,
      pageSize: 10000,
    };

    const allNodeResponse = await listNode(allNodeParams);
    const allNodeList = allNodeResponse.rows || [];

    // 处理数据
    const chartData = [];
    let totalNodes = 0;

    // 统计每个合作商的点位数量
    allPartnerList.forEach(partner => {
      const nodeCount = allNodeList.filter(node => node.partnerId === partner.id).length;
      totalNodes += nodeCount;

      if (nodeCount > 0) { // 只显示有点位的合作商
        chartData.push({
          name: partner.partnerName,
          value: nodeCount
        });
      }
    });

    // 如果没有数据，添加一个默认项
    if (chartData.length === 0) {
      chartData.push({
        name: '暂无数据',
        value: 1
      });
    }

    updateChart(chartData, totalNodes);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(chartData, totalNodes) {
  if (!chartInstance.value) return;

  // 定义颜色数组
  const colors = [
    '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
    '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f',
    '#87ceeb', '#dda0dd', '#98fb98', '#f0e68c', '#ffa07a'
  ];

  const option = {
    title: {
      text: '合作商点位分布',
      subtext: `总点位数: ${totalNodes}`,
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      },
      subtextStyle: {
        fontSize: 14,
        color: '#666'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        if (params.name === '暂无数据') {
          return '暂无数据';
        }
        const percent = ((params.value / totalNodes) * 100).toFixed(1);
        return `${params.name}<br/>点位数量: ${params.value}<br/>占比: ${percent}%`;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
      data: chartData.map(item => item.name),
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '点位分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            formatter: function(params) {
              if (params.name === '暂无数据') {
                return '暂无数据';
              }
              const percent = ((params.value / totalNodes) * 100).toFixed(1);
              return `${params.name}\n${params.value}个\n${percent}%`;
            }
          }
        },
        labelLine: {
          show: false
        },
        data: chartData.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询合作商列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

getList();
</script>

<style lang="scss" scoped>
.label-text {
  color: #606266;
  font-weight: bold;
  margin-right: 8px;
}

.value-text {
  color: #303133;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
