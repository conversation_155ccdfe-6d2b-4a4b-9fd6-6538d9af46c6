package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Order;
import com.dkd.manage.service.IOrderService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 订单管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
 // 定义REST控制器，处理订单管理相关的HTTP请求
@RestController
@RequestMapping("/manage/order")
public class OrderController extends BaseController
{
    // 注入订单业务层接口
    @Autowired
    private IOrderService orderService;

    /**
     * 查询订单列表接口
     * 需要'manage:order:list'权限
     * 请求路径：GET /manage/order/list
     */
    @PreAuthorize("@ss.hasPermi('manage:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(Order order)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询订单数据
        List<Order> list = orderService.selectOrderList(order);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出订单列表接口
     * 需要'manage:order:export'权限
     * 请求路径：POST /manage/order/export
     */
    @PreAuthorize("@ss.hasPermi('manage:order:export')")
    @Log(title = "订单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Order order)
    {
        // 调用服务层方法查询需要导出的订单数据
        List<Order> list = orderService.selectOrderList(order);
        // 创建Excel工具类实例
        ExcelUtil<Order> util = new ExcelUtil<Order>(Order.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "订单管理数据");
    }

    /**
     * 获取订单详细信息接口
     * 需要'manage:order:query'权限
     * 请求路径：GET /manage/order/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取订单详情
        return success(orderService.selectOrderById(id));
    }

    /**
     * 新增订单接口
     * 需要'manage:order:add'权限
     * 请求路径：POST /manage/order
     */
    @PreAuthorize("@ss.hasPermi('manage:order:add')")
    @Log(title = "订单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Order order)
    {
        // 调用服务层方法新增订单
        return toAjax(orderService.insertOrder(order));
    }

    /**
     * 修改订单接口
     * 需要'manage:order:edit'权限
     * 请求路径：PUT /manage/order
     */
    @PreAuthorize("@ss.hasPermi('manage:order:edit')")
    @Log(title = "订单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Order order)
    {
        // 调用服务层方法修改订单信息
        return toAjax(orderService.updateOrder(order));
    }

    /**
     * 删除订单接口
     * 需要'manage:order:remove'权限
     * 请求路径：DELETE /manage/order/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:order:remove')")
    @Log(title = "订单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的订单
        return toAjax(orderService.deleteOrderByIds(ids));
    }
}
// @RestController
// @RequestMapping("/manage/order")
// public class OrderController extends BaseController
// {
//     @Autowired
//     private IOrderService orderService;

//     /**
//      * 查询订单管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:order:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Order order)
//     {
//         startPage();
//         List<Order> list = orderService.selectOrderList(order);
//         return getDataTable(list);
//     }

//     /**
//      * 导出订单管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:order:export')")
//     @Log(title = "订单管理", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Order order)
//     {
//         List<Order> list = orderService.selectOrderList(order);
//         ExcelUtil<Order> util = new ExcelUtil<Order>(Order.class);
//         util.exportExcel(response, list, "订单管理数据");
//     }

//     /**
//      * 获取订单管理详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:order:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(orderService.selectOrderById(id));
//     }

//     /**
//      * 新增订单管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:order:add')")
//     @Log(title = "订单管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Order order)
//     {
//         return toAjax(orderService.insertOrder(order));
//     }

//     /**
//      * 修改订单管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:order:edit')")
//     @Log(title = "订单管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Order order)
//     {
//         return toAjax(orderService.updateOrder(order));
//     }

//     /**
//      * 删除订单管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:order:remove')")
//     @Log(title = "订单管理", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(orderService.deleteOrderByIds(ids));
//     }
// }
