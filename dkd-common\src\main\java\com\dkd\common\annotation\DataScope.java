// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.Documented; // 用于标记注解是否包含在JavaDoc中
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型

/**
 * 数据权限过滤注解
 * 用于在方法级别控制数据访问权限，根据用户角色和权限动态过滤数据
 * 通过AOP切面拦截带有此注解的方法，自动添加数据权限过滤条件到SQL查询中
 * 支持部门数据权限、用户数据权限等多种权限控制方式
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD) // 指定注解只能应用于方法
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Documented // 指定注解会被包含在JavaDoc中
public @interface DataScope // 定义数据权限过滤注解接口
{
    /**
     * 部门表的别名
     * 用于在SQL查询中指定部门表的别名，便于添加部门相关的数据权限过滤条件
     * 默认为空字符串，表示不指定别名
     *
     * @return 部门表别名字符串
     */
    public String deptAlias() default ""; // 定义部门表别名属性，默认值为空字符串

    /**
     * 用户表的别名
     * 用于在SQL查询中指定用户表的别名，便于添加用户相关的数据权限过滤条件
     * 默认为空字符串，表示不指定别名
     *
     * @return 用户表别名字符串
     */
    public String userAlias() default ""; // 定义用户表别名属性，默认值为空字符串

    /**
     * 权限字符（用于多个角色匹配符合要求的权限）
     * 默认根据权限注解@ss获取，多个权限用逗号分隔开来
     * 用于指定需要检查的具体权限，只有拥有相应权限的用户才能访问对应的数据
     *
     * @return 权限字符串，多个权限用逗号分隔
     */
    public String permission() default ""; // 定义权限字符属性，默认值为空字符串
}
