package com.dkd.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysDictType;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.system.service.ISysDictTypeService;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块字典类型相关的HTTP请求
@RestController
@RequestMapping("/system/dict/type")
public class SysDictTypeController extends BaseController
{
    // 注入字典类型业务层接口
    @Autowired
    private ISysDictTypeService dictTypeService;

    /**
     * 获取字典类型列表接口
     * 需要'system:dict:list'权限
     * 请求路径：GET /system/dict/type/list
     */
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDictType dictType)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询字典类型数据
        List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出字典类型数据接口
     * 需要'system:dict:export'权限
     * 请求路径：POST /system/dict/type/export
     */
    @Log(title = "字典类型", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictType dictType)
    {
        // 调用服务层方法查询需要导出的字典类型数据
        List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
        // 创建Excel工具类实例
        ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "字典类型");
    }

    /**
     * 查询字典类型详细信息接口
     * 需要'system:dict:query'权限
     * 请求路径：GET /system/dict/type/{dictId}
     */
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictId}")
    public AjaxResult getInfo(@PathVariable Long dictId)
    {
        // 调用服务层方法获取字典类型详情
        return success(dictTypeService.selectDictTypeById(dictId));
    }

    /**
     * 新增字典类型接口
     * 需要'system:dict:add'权限
     * 请求路径：POST /system/dict/type
     */
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictType dict)
    {
        // 校验字典类型是否唯一
        if (!dictTypeService.checkDictTypeUnique(dict))
        {
            return error("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
        }
        // 设置创建人
        dict.setCreateBy(getUsername());
        // 调用服务层方法新增字典类型
        return toAjax(dictTypeService.insertDictType(dict));
    }

    /**
     * 修改字典类型接口
     * 需要'system:dict:edit'权限
     * 请求路径：PUT /system/dict/type
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictType dict)
    {
        // 校验字典类型是否唯一
        if (!dictTypeService.checkDictTypeUnique(dict))
        {
            return error("修改字典'" + dict.getDictName() + "'失败，字典类型已存在");
        }
        // 设置更新人
        dict.setUpdateBy(getUsername());
        // 调用服务层方法修改字典类型
        return toAjax(dictTypeService.updateDictType(dict));
    }

    /**
     * 删除字典类型接口
     * 需要'system:dict:remove'权限
     * 请求路径：DELETE /system/dict/type/{dictIds}
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictIds}")
    public AjaxResult remove(@PathVariable Long[] dictIds)
    {
        // 调用服务层方法删除指定ID的字典类型
        dictTypeService.deleteDictTypeByIds(dictIds);
        // 返回操作成功结果
        return success();
    }

    /**
     * 刷新字典缓存接口
     * 需要'system:dict:remove'权限
     * 请求路径：DELETE /system/dict/type/refreshCache
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        // 调用服务层方法刷新字典缓存
        dictTypeService.resetDictCache();
        // 返回操作成功结果
        return success();
    }

    /**
     * 获取字典选择框列表接口
     * 无需特殊权限
     * 请求路径：GET /system/dict/type/optionselect
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        // 调用服务层方法获取所有字典类型
        List<SysDictType> dictTypes = dictTypeService.selectDictTypeAll();
        // 返回查询结果
        return success(dictTypes);
    }
}



// @RestController
// @RequestMapping("/system/dict/type")
// public class SysDictTypeController extends BaseController
// {
//     @Autowired
//     private ISysDictTypeService dictTypeService;

//     @PreAuthorize("@ss.hasPermi('system:dict:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysDictType dictType)
//     {
//         startPage();
//         List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
//         return getDataTable(list);
//     }

//     @Log(title = "字典类型", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('system:dict:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysDictType dictType)
//     {
//         List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
//         ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
//         util.exportExcel(response, list, "字典类型");
//     }

//     /**
//      * 查询字典类型详细
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:query')")
//     @GetMapping(value = "/{dictId}")
//     public AjaxResult getInfo(@PathVariable Long dictId)
//     {
//         return success(dictTypeService.selectDictTypeById(dictId));
//     }

//     /**
//      * 新增字典类型
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:add')")
//     @Log(title = "字典类型", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysDictType dict)
//     {
//         if (!dictTypeService.checkDictTypeUnique(dict))
//         {
//             return error("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
//         }
//         dict.setCreateBy(getUsername());
//         return toAjax(dictTypeService.insertDictType(dict));
//     }

//     /**
//      * 修改字典类型
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:edit')")
//     @Log(title = "字典类型", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysDictType dict)
//     {
//         if (!dictTypeService.checkDictTypeUnique(dict))
//         {
//             return error("修改字典'" + dict.getDictName() + "'失败，字典类型已存在");
//         }
//         dict.setUpdateBy(getUsername());
//         return toAjax(dictTypeService.updateDictType(dict));
//     }

//     /**
//      * 删除字典类型
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:remove')")
//     @Log(title = "字典类型", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{dictIds}")
//     public AjaxResult remove(@PathVariable Long[] dictIds)
//     {
//         dictTypeService.deleteDictTypeByIds(dictIds);
//         return success();
//     }

//     /**
//      * 刷新字典缓存
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:remove')")
//     @Log(title = "字典类型", businessType = BusinessType.CLEAN)
//     @DeleteMapping("/refreshCache")
//     public AjaxResult refreshCache()
//     {
//         dictTypeService.resetDictCache();
//         return success();
//     }

//     /**
//      * 获取字典选择框列表
//      */
//     @GetMapping("/optionselect")
//     public AjaxResult optionselect()
//     {
//         List<SysDictType> dictTypes = dictTypeService.selectDictTypeAll();
//         return success(dictTypes);
//     }
// }
