// 声明包名，定义当前类所属的包路径
package com.dkd.manage.service;

// 导入Jackson库的JsonNode类，用于处理JSON数据节点
import com.fasterxml.jackson.databind.JsonNode;
// 导入Jackson库的ObjectMapper类，用于JSON序列化和反序列化
import com.fasterxml.jackson.databind.ObjectMapper;
// 导入Lombok的Slf4j注解，自动生成日志记录器
import lombok.extern.slf4j.Slf4j;

// 导入Spring的Value注解，用于注入配置文件中的属性值
import org.springframework.beans.factory.annotation.Value;
// 导入Spring HTTP相关类，用于HTTP请求处理
import org.springframework.http.*;
// 导入HTTP客户端响应处理类
import org.springframework.http.client.ClientHttpResponse;
// 导入简单HTTP请求工厂类，用于配置HTTP连接参数
import org.springframework.http.client.SimpleClientHttpRequestFactory;
// 导入Spring的Service注解，标识这是一个服务层组件
import org.springframework.stereotype.Service;
// 导入默认响应错误处理器
import org.springframework.web.client.DefaultResponseErrorHandler;
// 导入RestTemplate类，用于发送HTTP请求
import org.springframework.web.client.RestTemplate;

// 导入IO异常类
import java.io.IOException;
// 导入Java集合框架相关类
import java.util.*;

/**
 * RAG增强聊天服务类
 * 结合知识库检索(Retrieval)和大语言模型生成(Generation)技术
 * 为用户提供基于知识库的智能问答服务
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j // Lombok注解，自动生成名为log的日志记录器
@Service // 标识这是一个Spring服务层组件，会被Spring容器管理
public class RagChatService {

    // 从配置文件中注入阿里云百炼API的密钥
    @Value("${aliyun.bailian.api-key}")
    private String apiKey;

    // 从配置文件中注入智能体应用ID
    @Value("${aliyun.bailian.app-id}")
    private String appId;



    /**
     * RAG增强聊天的核心方法
     * 直接调用阿里云百炼平台的智能体应用（已内置知识库）
     * 为用户提供基于内置知识库的专业回答
     *
     * @param userQuery 用户输入的查询问题
     * @param conversationHistory 对话历史记录，包含角色和内容的Map列表
     * @return 基于内置知识库和对话历史生成的AI回答
     */
    public String ragChat(String userQuery, List<Map<String, String>> conversationHistory) {
        try { // 使用try-catch块处理可能的异常，确保方法的健壮性
            // 记录开始处理RAG聊天请求的日志，便于调试和监控
            log.info("处理RAG聊天请求: {}", userQuery);

            // 直接调用智能体应用，传入用户查询和对话历史，获取专业回答
            String answer = callAgentApplication(userQuery, conversationHistory);

            // 记录回答生成完成的日志，包含回答长度，用于性能监控
            log.info("RAG聊天回答生成完成，长度: {}", answer.length());

            // 返回智能体应用生成的专业回答
            return answer;

        } catch (Exception e) { // 捕获所有可能的异常，包括网络异常、API异常等
            // 记录RAG聊天处理失败的错误日志，包含异常详细信息
            log.error("RAG聊天处理失败", e);
            // 返回用户友好的错误提示信息，避免暴露技术细节
            return "抱歉，我遇到了一些技术问题，请稍后再试。如果问题持续存在，请联系系统管理员。";
        }
    }

    /**
     * 调用智能体应用的方法
     * 使用正确的智能体应用API格式，支持session_id管理对话历史
     *
     * @param userQuery 用户当前的查询问题
     * @param conversationHistory 对话历史记录
     * @return 智能体应用生成的回答
     * @throws Exception 当API调用失败时抛出异常
     */
    private String callAgentApplication(String userQuery, List<Map<String, String>> conversationHistory) throws Exception {
        try { // 使用try-catch块处理HTTP请求可能的异常，确保方法的健壮性
            // 检查API密钥是否已配置，如果未配置则抛出运行时异常
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new RuntimeException("API密钥未配置");
            }
            // 检查智能体应用ID是否已配置，如果未配置则抛出运行时异常
            if (appId == null || appId.trim().isEmpty()) {
                throw new RuntimeException("智能体应用ID未配置");
            }

            // 创建带有超时配置的RestTemplate实例，用于发送HTTP请求
            RestTemplate restTemplate = createRestTemplateWithTimeout();

            // 构建智能体应用API请求体，使用阿里云百炼平台的标准格式
            Map<String, Object> requestBody = new HashMap<>();

            // 构建input对象，包含用户的查询内容
            Map<String, Object> input = new HashMap<>();
            // 构建更明确的提示词，指示智能体使用内置知识库回答专业问题
            String enhancedPrompt = "请基于你的内置知识库回答以下关于智能售货机管理系统的问题：" + userQuery +
                                  "\n\n请确保使用专业的技术文档和系统知识来回答。";
            // 将增强后的提示词放入input对象的prompt字段
            input.put("prompt", enhancedPrompt);

            // 如果有对话历史，尝试使用session_id（需要从Controller传入）
            // 这里暂时不处理session_id，让智能体应用自己管理对话历史

            // 将input对象添加到请求体中
            requestBody.put("input", input);

            // 添加模型参数配置，控制AI回答的质量和长度
            Map<String, Object> parameters = new HashMap<>();
            // 设置最大生成token数量，控制回答的最大长度
            parameters.put("max_tokens", 2000);
            // 设置温度参数，控制回答的创造性（0.7表示适中的创造性）
            parameters.put("temperature", 0.7);
            // 设置是否启用增量输出（false表示一次性返回完整结果）
            parameters.put("incremental_output", false);
            // 将参数配置添加到请求体中
            requestBody.put("parameters", parameters);

            // 添加debug对象（可选），用于调试和监控
            requestBody.put("debug", new HashMap<>());

            // 设置HTTP请求头，包含认证信息和内容类型
            HttpHeaders headers = new HttpHeaders();
            // 设置Bearer认证头，使用配置的API密钥
            headers.set("Authorization", "Bearer " + apiKey);
            // 设置内容类型为JSON
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 设置连接关闭，避免连接池问题
            headers.set("Connection", "close");

            // 构建完整的API请求URL，指向智能体应用的completion端点
            String apiUrl = "https://dashscope.aliyuncs.com/api/v1/apps/" + appId + "/completion";
            // 创建HTTP实体，包含请求体和请求头
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 记录调试日志，包含应用ID和用户问题
            log.debug("调用智能体应用API，应用ID: {}, 用户问题: {}", appId, userQuery);
            // 记录请求开始时间，用于计算API调用耗时
            long startTime = System.currentTimeMillis();

            // 发送POST请求到智能体应用API，获取响应
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);

            // 记录请求结束时间
            long endTime = System.currentTimeMillis();
            // 记录API调用完成的调试日志，包含耗时信息
            log.debug("智能体应用API调用完成，耗时: {}ms", endTime - startTime);

            // 创建JSON解析器，用于解析API响应
            ObjectMapper mapper = new ObjectMapper();
            // 将响应体解析为JSON节点
            JsonNode jsonNode = mapper.readTree(response.getBody());

            // 检查响应中是否包含错误代码
            if (jsonNode.has("code")) {
                // 提取错误代码和错误消息
                String errorCode = jsonNode.path("code").asText();
                String errorMessage = jsonNode.path("message").asText();
                // 抛出包含详细错误信息的运行时异常
                throw new RuntimeException("智能体应用API错误: " + errorCode + " - " + errorMessage);
            }

            // 从响应中提取智能体应用的回答
            JsonNode output = jsonNode.path("output");
            // 检查output节点是否包含text字段
            if (output.has("text")) {
                // 返回智能体应用生成的文本回答
                return output.path("text").asText();
            } else {
                // 如果响应格式不符合预期，抛出异常
                throw new RuntimeException("无法解析智能体应用的响应格式");
            }

        } catch (Exception e) {
            // 记录API调用失败的错误日志
            log.error("调用智能体应用API失败: {}", e.getMessage());
            // 重新抛出异常，让上层方法处理
            throw e;
        }
    }

    /**
     * 调用智能体应用的简单方法（用于非专业问题）
     * 直接传入完整的提示词，不使用专业知识库
     *
     * @param prompt 完整的提示词内容
     * @return 智能体应用生成的回答
     * @throws Exception 当API调用失败时抛出异常
     */
    private String callAgentApplicationSimple(String prompt) throws Exception {
        try { // 使用try-catch块处理HTTP请求可能的异常
            // 检查API密钥是否已配置，确保能够正常调用API
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new RuntimeException("API密钥未配置");
            }
            // 检查智能体应用ID是否已配置，确保能够调用正确的应用
            if (appId == null || appId.trim().isEmpty()) {
                throw new RuntimeException("智能体应用ID未配置");
            }

            // 创建带有超时配置的RestTemplate实例，用于发送HTTP请求
            RestTemplate restTemplate = createRestTemplateWithTimeout();

            // 构建智能体应用API请求体，使用简单模式的格式
            Map<String, Object> requestBody = new HashMap<>();

            // 构建input对象，包含用户的完整提示词
            Map<String, Object> input = new HashMap<>();
            // 直接使用传入的提示词，不进行额外的增强处理
            input.put("prompt", prompt);
            // 将input对象添加到请求体中
            requestBody.put("input", input);

            // 添加模型参数配置，控制AI回答的质量
            Map<String, Object> parameters = new HashMap<>();
            // 设置最大生成token数量
            parameters.put("max_tokens", 2000);
            // 设置温度参数，控制回答的创造性
            parameters.put("temperature", 0.7);
            // 设置是否启用增量输出
            parameters.put("incremental_output", false);
            // 将参数配置添加到请求体中
            requestBody.put("parameters", parameters);

            // 添加debug对象，用于调试和监控
            requestBody.put("debug", new HashMap<>());

            // 设置HTTP请求头，包含认证信息
            HttpHeaders headers = new HttpHeaders();
            // 设置Bearer认证头
            headers.set("Authorization", "Bearer " + apiKey);
            // 设置内容类型为JSON
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 设置连接关闭
            headers.set("Connection", "close");

            // 构建API请求URL
            String apiUrl = "https://dashscope.aliyuncs.com/api/v1/apps/" + appId + "/completion";
            // 创建HTTP实体
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 记录调试日志，标识这是简单模式的API调用
            log.debug("调用智能体应用API（简单模式），应用ID: {}", appId);
            // 发送POST请求到智能体应用API
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);

            // 创建JSON解析器，用于解析API响应
            ObjectMapper mapper = new ObjectMapper();
            // 将响应体解析为JSON节点
            JsonNode jsonNode = mapper.readTree(response.getBody());

            // 检查响应中是否包含错误代码
            if (jsonNode.has("code")) {
                // 提取错误代码和错误消息
                String errorCode = jsonNode.path("code").asText();
                String errorMessage = jsonNode.path("message").asText();
                // 抛出包含详细错误信息的运行时异常
                throw new RuntimeException("智能体应用API错误: " + errorCode + " - " + errorMessage);
            }

            // 从响应中提取智能体应用的回答
            JsonNode output = jsonNode.path("output");
            // 检查output节点是否包含text字段
            if (output.has("text")) {
                // 返回智能体应用生成的文本回答
                return output.path("text").asText();
            } else {
                // 如果响应格式不符合预期，抛出异常
                throw new RuntimeException("无法解析智能体应用的响应格式");
            }

        } catch (Exception e) {
            // 记录简单模式API调用失败的错误日志
            log.error("调用智能体应用API（简单模式）失败: {}", e.getMessage());
            // 重新抛出异常，让上层方法处理
            throw e;
        }
    }



    /**
     * 创建带超时配置的RestTemplate的方法
     * 配置HTTP连接和读取超时时间，提高API调用的稳定性
     * 同时添加自定义错误处理器，增强错误处理能力
     *
     * @return 配置好的RestTemplate实例
     */
    private RestTemplate createRestTemplateWithTimeout() {
        // 创建简单的HTTP请求工厂，用于配置超时参数
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 设置连接超时时间为5秒，避免长时间等待连接建立
        factory.setConnectTimeout(5000);
        // 设置读取超时时间为30秒，给AI模型足够的处理时间
        factory.setReadTimeout(30000);

        // 使用配置好的工厂创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate(factory);

        // 添加自定义错误处理器，增强HTTP错误处理能力
        restTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
            // 重写hasError方法，判断是否存在HTTP错误
            @Override
            public boolean hasError(HttpStatus statusCode) {
                // 当状态码为4xx（客户端错误）或5xx（服务器错误）时返回true
                return statusCode.series() == HttpStatus.Series.CLIENT_ERROR ||
                       statusCode.series() == HttpStatus.Series.SERVER_ERROR;
            }

            // 重写handleError方法，自定义错误处理逻辑
            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                // 记录HTTP错误的详细信息，包括状态码和状态文本
                log.error("HTTP错误: {} - {}", response.getStatusCode(), response.getStatusText());
                // 调用父类的错误处理方法，保持默认的异常抛出行为
                super.handleError(response);
            }
        });

        // 返回配置完成的RestTemplate实例
        return restTemplate;
    }



    /**
     * 简单聊天（不使用RAG）
     * 用于一般性对话，直接调用智能体应用的通用能力
     *
     * @param userQuery 用户查询
     * @param conversationHistory 对话历史
     * @return AI回答
     */
    public String simpleChat(String userQuery, List<Map<String, String>> conversationHistory) {
        try { // 使用try-catch块处理可能的异常
            // 记录开始处理简单聊天请求的日志
            log.info("处理简单聊天请求: {}", userQuery);

            // 构建简单的对话提示词，不使用专业知识库
            StringBuilder prompt = new StringBuilder();
            // 添加基础的AI助手角色定义
            prompt.append("你是一个友好、专业的AI助手。请自然地回答用户的问题。\n\n");

            // 添加对话历史，保持对话的连贯性
            if (conversationHistory != null && !conversationHistory.isEmpty()) {
                // 添加对话历史标题
                prompt.append("对话历史：\n");
                // 计算起始索引，只保留最近3轮对话（6条消息）以控制提示长度
                int startIndex = Math.max(0, conversationHistory.size() - 6);
                // 遍历对话历史记录
                for (int i = startIndex; i < conversationHistory.size(); i++) {
                    // 获取当前消息
                    Map<String, String> msg = conversationHistory.get(i);
                    // 提取消息角色（user或assistant）
                    String role = msg.get("role");
                    // 提取消息内容
                    String content = msg.get("content");
                    // 根据角色格式化消息
                    if ("user".equals(role)) {
                        prompt.append("用户：").append(content).append("\n");
                    } else if ("assistant".equals(role)) {
                        prompt.append("助手：").append(content).append("\n");
                    }
                }
                // 添加换行符分隔对话历史和当前问题
                prompt.append("\n");
            }

            // 添加当前用户查询
            prompt.append("当前用户问题：").append(userQuery).append("\n\n");
            // 添加回答指令
            prompt.append("请自然、友好地回答用户的问题：");

            // 尝试调用智能体应用（简单模式，不使用专业知识库）
            try {
                // 直接调用智能体应用的简单模式，传入构建好的提示词
                return callAgentApplicationSimple(prompt.toString());
            } catch (Exception apiException) {
                // 记录API调用失败的警告日志
                log.warn("调用AI API失败，使用降级回答: {}", apiException.getMessage());
                // API调用失败时，使用本地预设的简单回答作为降级处理
                return generateSimpleAnswer(userQuery);
            }

        } catch (Exception e) {
            // 记录简单聊天处理失败的错误日志
            log.error("简单聊天处理失败", e);
            // 返回用户友好的错误提示信息
            return "抱歉，我遇到了一些问题，请稍后再试。";
        }
    }

    /**
     * 生成简单回答（降级回答）
     * 当智能体应用API调用失败时，使用本地预设回答作为降级处理
     *
     * @param userQuery 用户查询内容
     * @return 预设的简单回答
     */
    private String generateSimpleAnswer(String userQuery) {
        // 将用户查询转换为小写，便于关键词匹配
        String lowerQuery = userQuery.toLowerCase();

        // 处理问候语
        if (lowerQuery.contains("你好") || lowerQuery.contains("hello")) {
            return "您好！我是智能售货机管理系统的客服助手，很高兴为您服务！有什么可以帮助您的吗？";
        }

        // 处理感谢语
        if (lowerQuery.contains("谢谢") || lowerQuery.contains("感谢")) {
            return "不客气！如果您还有其他问题，随时可以咨询我。";
        }

        // 对于美食相关的非系统问题，提供友好的引导回答
        if (lowerQuery.contains("美食") || lowerQuery.contains("推荐") || lowerQuery.contains("好吃")) {
            return "很抱歉，我是智能售货机管理系统的专业客服助手，主要负责解答系统相关的问题。\n\n" +
                   "对于美食推荐等生活类问题，建议您咨询专门的生活服务助手。\n\n" +
                   "如果您有关于售货机管理系统的问题，我很乐意为您提供专业的帮助！";
        }

        // 对于旅游相关的非系统问题，提供友好的引导回答
        if (lowerQuery.contains("旅游") || lowerQuery.contains("景点") || lowerQuery.contains("好玩")) {
            return "很抱歉，我是智能售货机管理系统的专业客服助手，主要负责解答系统相关的问题。\n\n" +
                   "对于旅游景点等问题，建议您咨询专门的旅游服务助手。\n\n" +
                   "如果您有关于售货机管理系统的问题，我很乐意为您提供专业的帮助！";
        }

        // 默认回答，引导用户提出系统相关问题
        return "感谢您的咨询！我是智能售货机管理系统的专业客服助手。\n\n" +
               "我主要负责解答系统操作、功能使用、技术问题等相关咨询。\n\n" +
               "如果您有具体的系统问题，请详细描述，我会为您提供专业的帮助！";
    }

    /**
     * 判断是否需要使用RAG模式
     * 根据用户查询内容判断是否需要调用智能体应用的专业知识库
     *
     * @param userQuery 用户查询内容
     * @return 是否需要使用RAG模式（true=使用专业知识库，false=使用通用能力）
     */
    public boolean shouldUseRag(String userQuery) {
        // 检查用户查询是否为空或仅包含空白字符
        if (userQuery == null || userQuery.trim().isEmpty()) {
            return false;
        }

        // 将用户查询转换为小写，便于关键词匹配
        String query = userQuery.toLowerCase();

        // 定义明确的非系统相关关键词数组（优先判断，避免误用专业知识库）
        String[] nonSystemKeywords = {
            "美食", "旅游", "景点", "天气", "股票", "电影", "音乐", "小说", "游戏",
            "娱乐", "明星", "体育", "足球", "篮球", "历史", "地理", "文学", "诗歌",
            "菜谱", "做菜", "烹饪", "健身", "减肥", "化妆", "时尚", "购物",
            "江西", "北京", "上海", "广州", "深圳", "杭州", "成都", "西安",
            "推荐", "好吃", "好玩", "哪里", "什么地方", "哪个城市", "哪家",
            "吃什么", "玩什么", "去哪", "哪儿", "什么好", "有什么好"
        };

        // 遍历非系统关键词，如果匹配则不使用RAG模式
        for (String keyword : nonSystemKeywords) {
            if (query.contains(keyword)) {
                // 记录检测到非系统相关问题的日志
                log.info("检测到非系统相关问题，不使用RAG: {}", userQuery);
                return false;
            }
        }

        // 定义系统相关关键词数组，用于识别需要专业知识库的问题
        String[] systemKeywords = {
            "系统", "功能", "操作", "管理", "设备", "售货机", "工单", "点位",
            "区域", "合作商", "商品", "员工", "策略", "统计", "监控", "权限",
            "登录", "用户", "角色", "菜单", "部门", "字典", "参数", "日志",
            "投放", "补货", "维修", "撤机", "货道", "库存", "收益", "分析",
            "如何", "怎么", "什么是", "怎样", "步骤", "方法", "配置", "设置",
            "技术栈", "技术架构", "框架", "数据库", "redis", "mysql", "spring",
            "vue", "前端", "后端", "接口", "api", "部署", "开发", "代码",
            "dkd", "智能", "客服", "知识库", "帮助", "问题", "故障", "错误"
        };

        // 遍历系统关键词，如果匹配则使用RAG模式
        for (String keyword : systemKeywords) {
            if (query.contains(keyword)) {
                // 记录检测到系统相关问题的日志
                log.info("检测到系统相关问题，使用RAG: {}", userQuery);
                return true;
            }
        }

        // 默认情况下，对于模糊的问题，不使用RAG，让智能体应用使用通用能力回答
        log.info("未检测到明确的系统关键词，不使用RAG: {}", userQuery);
        return false;
    }

    /**
     * 智能聊天方法
     * 自动判断是否使用RAG模式，提供智能路由功能
     *
     * @param userQuery 用户查询内容
     * @param conversationHistory 对话历史记录
     * @return AI生成的回答
     */
    public String intelligentChat(String userQuery, List<Map<String, String>> conversationHistory) {
        // 调用shouldUseRag方法判断是否需要使用专业知识库
        if (shouldUseRag(userQuery)) {
            // 记录使用RAG模式处理查询的日志
            log.info("使用RAG模式处理查询: {}", userQuery);
            // 调用ragChat方法，使用智能体应用的专业知识库
            return ragChat(userQuery, conversationHistory);
        } else {
            // 记录使用简单模式处理查询的日志
            log.info("使用简单模式处理查询: {}", userQuery);
            // 调用simpleChat方法，使用智能体应用的通用能力
            return simpleChat(userQuery, conversationHistory);
        }
    }

    /**
     * 获取智能体应用相关信息
     * 用于调试和查看智能体应用的功能介绍
     *
     * @param query 查询关键词
     * @return 智能体应用的功能介绍信息
     */
    public String getKnowledgeInfo(String query) {
        try { // 使用try-catch块处理可能的异常
            // 构建查询智能体应用信息的提示词，包含用户提供的关键词
            String prompt = "请介绍一下你的功能和能力，以及你可以帮助用户解决哪些问题。关键词：" + query;

            // 调用智能体应用的简单模式获取功能介绍信息
            String response = callAgentApplicationSimple(prompt);

            // 返回格式化的智能体应用信息
            return "智能体应用信息：\n" + response;
        } catch (Exception e) {
            // 记录获取智能体应用信息失败的错误日志
            log.error("获取智能体应用信息失败", e);
            // 返回包含错误信息的友好提示
            return "获取智能体应用信息失败：" + e.getMessage();
        }
    }
}
