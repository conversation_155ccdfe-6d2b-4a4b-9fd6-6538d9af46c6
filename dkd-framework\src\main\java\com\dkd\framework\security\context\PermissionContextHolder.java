// 包声明：定义当前类所属的包路径，位于安全框架的上下文包中
package com.dkd.framework.security.context;

// 导入Spring Web的请求属性接口，用于定义属性作用域
import org.springframework.web.context.request.RequestAttributes;
// 导入Spring Web的请求上下文持有者，用于获取当前请求的上下文信息
import org.springframework.web.context.request.RequestContextHolder;
// 导入通用转换工具类，用于数据类型转换
import com.dkd.common.core.text.Convert;

/**
 * 权限信息上下文持有者
 * 这个工具类用于在HTTP请求范围内存储和获取权限检查的上下文信息，
 * 主要用于权限验证过程中记录当前正在检查的权限，便于日志记录和审计。
 *
 * 主要功能：
 * 1. 在请求范围内存储权限信息
 * 2. 获取当前请求中的权限上下文
 * 3. 支持权限验证的日志记录和审计
 *
 * 与AuthenticationContextHolder的区别：
 * - AuthenticationContextHolder使用ThreadLocal，存储认证信息
 * - PermissionContextHolder使用RequestAttributes，存储权限检查信息
 *
 * <AUTHOR>
 */
public class PermissionContextHolder
{
    /**
     * 权限上下文属性名称常量
     * 用于在RequestAttributes中存储权限信息的键名
     */
    private static final String PERMISSION_CONTEXT_ATTRIBUTES = "PERMISSION_CONTEXT";

    /**
     * 设置权限上下文
     * 将当前正在检查的权限信息存储到请求属性中
     *
     * @param permission 权限字符串，例如："system:user:add"
     */
    public static void setContext(String permission)
    {
        // 获取当前请求的属性对象，并设置权限信息到请求范围内
        // SCOPE_REQUEST表示该属性只在当前HTTP请求范围内有效
        RequestContextHolder.currentRequestAttributes().setAttribute(PERMISSION_CONTEXT_ATTRIBUTES, permission,
                RequestAttributes.SCOPE_REQUEST);
    }

    /**
     * 获取权限上下文
     * 从当前请求属性中获取存储的权限信息
     *
     * @return String 权限字符串，如果没有设置则返回null或空字符串
     */
    public static String getContext()
    {
        // 从当前请求属性中获取权限信息，并转换为字符串类型
        // Convert.toStr()确保返回值为字符串类型，即使原值为null也会转换为空字符串
        return Convert.toStr(RequestContextHolder.currentRequestAttributes().getAttribute(PERMISSION_CONTEXT_ATTRIBUTES,
                RequestAttributes.SCOPE_REQUEST));
    }
} // 类结束标记
