// 包声明：定义当前类所属的包路径
package com.dkd.framework.web.service;

// 导入系统用户数据访问层接口
import com.dkd.system.mapper.SysUserMapper;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring组件注解
import org.springframework.stereotype.Component;
// 导入缓存常量类
import com.dkd.common.constant.CacheConstants;
// 导入系统常量类
import com.dkd.common.constant.Constants;
// 导入用户常量类
import com.dkd.common.constant.UserConstants;
// 导入系统用户实体类
import com.dkd.common.core.domain.entity.SysUser;
// 导入注册请求体模型类
import com.dkd.common.core.domain.model.RegisterBody;
// 导入Redis缓存操作类
import com.dkd.common.core.redis.RedisCache;
// 导入验证码异常类
import com.dkd.common.exception.user.CaptchaException;
// 导入验证码过期异常类
import com.dkd.common.exception.user.CaptchaExpireException;
// 导入消息工具类，用于国际化消息处理
import com.dkd.common.utils.MessageUtils;
// 导入安全工具类，用于密码加密等操作
import com.dkd.common.utils.SecurityUtils;
// 导入字符串工具类
import com.dkd.common.utils.StringUtils;
// 导入异步管理器
import com.dkd.framework.manager.AsyncManager;
// 导入异步工厂类
import com.dkd.framework.manager.factory.AsyncFactory;
// 导入系统配置服务接口
import com.dkd.system.service.ISysConfigService;
// 导入系统用户服务接口
import com.dkd.system.service.ISysUserService;

/**
 * 注册校验方法
 * 这个服务类负责处理用户注册的完整流程，包括：
 * 1. 验证码校验
 * 2. 用户名密码格式验证
 * 3. 用户名唯一性检查
 * 4. 手机号唯一性检查
 * 5. 用户注册处理
 * 6. 注册日志记录
 *
 * <AUTHOR>
 */
@Component // 将此类标记为Spring组件，由Spring容器管理
public class SysRegisterService
{
    // 注入用户服务，用于用户相关操作
    @Autowired
    private ISysUserService userService;

    // 注入配置服务，用于获取系统配置参数
    @Autowired
    private ISysConfigService configService;

    // 注入Redis缓存服务，用于验证码等临时数据存储
    @Autowired
    private RedisCache redisCache;

    // 注入用户数据访问层，用于数据库操作
    @Autowired
    private SysUserMapper userMapper;


    /**
     * 注册
     * 处理用户注册的核心方法，包含完整的注册流程和验证逻辑
     *
     * @param registerBody 注册请求体，包含用户名、密码、手机号、验证码等信息
     * @return 注册结果消息，空字符串表示成功，非空表示失败原因
     */
    public String register(RegisterBody registerBody)
    {
        // 初始化返回消息和提取注册信息
        String msg = ""; // 返回消息，空表示成功
        String username = registerBody.getUsername(); // 获取用户名
        String password = registerBody.getPassword(); // 获取密码
        // 创建系统用户对象
        SysUser sysUser = new SysUser();
        // 设置用户名
        sysUser.setUserName(username);

        // 第一步：验证码校验
        // 从系统配置中获取验证码是否启用的设置
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        // 如果启用了验证码功能
        if (captchaEnabled)
        {
            // 验证用户输入的验证码
            validateCaptcha(username, registerBody.getCode(), registerBody.getUuid());
        }

        // 第二步：参数格式验证
        // 检查用户名是否为空
        if (StringUtils.isEmpty(username))
        {
            msg = "用户名不能为空"; // 设置错误消息
        }
        // 检查密码是否为空
        else if (StringUtils.isEmpty(password))
        {
            msg = "用户密码不能为空"; // 设置错误消息
        }
        // 检查用户名长度是否在规定范围内
        else if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            msg = "账户长度必须在2到20个字符之间"; // 设置错误消息
        }
        // 检查密码长度是否在规定范围内
        else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            msg = "密码长度必须在5到20个字符之间"; // 设置错误消息
        }
        // 第三步：业务逻辑验证
        // 检查用户名是否已存在
        else if (!userService.checkUserNameUnique(sysUser))
        {
            msg = "保存用户'" + username + "'失败，注册账号已存在"; // 设置错误消息
        }
        // 检查手机号是否已被注册
        else if (userMapper.checkPhoneUnique1(registerBody.getPhonenumber()))
        {
            msg = "该手机号已被注册，请更换手机号"; // 设置错误消息
        }
        // 第四步：所有验证通过，执行注册
        else
        {
            // 设置用户昵称（默认与用户名相同）
            sysUser.setNickName(username);
            // 设置加密后的密码
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            // 设置手机号
            sysUser.setPhonenumber(registerBody.getPhonenumber());
            // 调用用户服务执行注册
            boolean regFlag = userService.registerUser(sysUser);
            // 检查注册是否成功
            if (!regFlag)
            {
                msg = "注册失败,请联系系统管理人员"; // 设置错误消息
            }
            else // 注册成功
            {
                // 异步记录注册成功日志
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
            }
        }
        // 返回注册结果消息
        return msg;
    }

    /**
     * 校验验证码
     * 验证用户输入的图形验证码是否正确（注册时使用）
     *
     * @param username 用户名，用于记录日志（此处未使用）
     * @param code 验证码，用户输入的验证码
     * @param uuid 唯一标识，验证码对应的唯一标识符
     * @return 结果，无返回值，验证失败会抛出异常
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        // 构造验证码在Redis中的缓存键，格式为：captcha_codes: + uuid
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        // 从Redis缓存中获取存储的验证码
        String captcha = redisCache.getCacheObject(verifyKey);
        // 立即删除缓存中的验证码，确保验证码只能使用一次
        redisCache.deleteObject(verifyKey);
        // 如果缓存中没有找到验证码，说明验证码已过期或不存在
        if (captcha == null)
        {
            // 抛出验证码过期异常
            throw new CaptchaExpireException();
        }
        // 比较用户输入的验证码与缓存中的验证码（忽略大小写）
        if (!code.equalsIgnoreCase(captcha))
        {
            // 抛出验证码错误异常
            throw new CaptchaException();
        }
        // 验证码验证通过，方法正常结束
    }
} // 类结束标记
