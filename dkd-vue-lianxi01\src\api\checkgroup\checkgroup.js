import request from '@/utils/request'

// 查询checkgroup列表
export function listCheckgroup(query) {
  return request({
    url: '/checkgroup/checkgroup/list',
    method: 'get',
    params: query
  })
}

// 查询checkgroup详细
export function getCheckgroup(id) {
  return request({
    url: '/checkgroup/checkgroup/' + id,
    method: 'get'
  })
}

// 新增checkgroup
export function addCheckgroup(data) {
  return request({
    url: '/checkgroup/checkgroup',
    method: 'post',
    data: data
  })
}

// 修改checkgroup
export function updateCheckgroup(data) {
  return request({
    url: '/checkgroup/checkgroup',
    method: 'put',
    data: data
  })
}

// 删除checkgroup
export function delCheckgroup(id) {
  return request({
    url: '/checkgroup/checkgroup/' + id,
    method: 'delete'
  })
}
