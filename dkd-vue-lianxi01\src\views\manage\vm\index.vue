<template>
  <div class="app-container">
    <!-- 合作商设备类型统计图表 -->
    <el-card class="mb-4" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>合作商设备类型统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新统计
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 450px;"></div>
    </el-card>

    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="设备编号" prop="innerCode">
            <el-input v-model="queryParams.innerCode" placeholder="请输入设备编号" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:vm:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:vm:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:vm:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:vm:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表展示 -->
    <el-table v-loading="loading" :data="vmList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备编号" align="center" prop="innerCode" />
      <el-table-column label="设备型号" align="center" prop="vmTypeId" >
        <template #default="scope">
          <div v-for="item in vmTypeList" :key="item.id">
            <span v-if="item.id==scope.row.vmTypeId">{{ item.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="详细地址" align="center" prop="addr" />
      <el-table-column label="合作商" align="center" prop="partnerId" >
        <template #default="scope">
          <div v-for="item in partnerList" :key="item.id">
            <span v-if="item.id==scope.row.partnerId">{{ item.partnerName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="设备状态" align="center" prop="vmStatus">
        <template #default="scope">
          <dict-tag :options="vm_status" :value="scope.row.vmStatus" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleGoods(scope.row)" v-hasPermi="['manage:vm:edit']">货道</el-button>
          <el-button link type="primary" @click="handleUpdatePolicy(scope.row)" v-hasPermi="['manage:vm:edit']">策略</el-button>
          <el-button link type="primary" @click="handleUpdate(scope.row)" v-hasPermi="['manage:vm:edit']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

   

    <!-- 策略管理对话框 -->
    <el-dialog title="策略管理" v-model="policyOpen" width="500px" append-to-body>
      <el-form ref="vmRef" :model="form" label-width="80px">
        <el-form-item label="策略" prop="policyId">
          <el-select v-model="form.policyId" placeholder="请选择策略">
            <el-option v-for="item in policyList" :key="item.policyId" :label="item.policyName"
              :value="item.policyId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="vmRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备编号">
          <span>{{ form.innerCode == null ? '系统自动生成' : form.innerCode }}</span>
        </el-form-item>
        <el-form-item label="供货时间" v-if="form.innerCode != null">
          <span>{{ parseTime(form.lastSupplyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
        <el-form-item label="设备类型" v-if="form.innerCode != null">
          <div v-for="item in vmTypeList" :key="item.id">
            <span v-if="form.vmTypeId == item.id">{{ item.name }}</span>
          </div>
        </el-form-item>
        <el-form-item label="设备容量" v-if="form.innerCode != null">
          <span>{{ form.channelMaxCapacity }}</span>
        </el-form-item>
        <el-form-item label="选择型号" prop="vmTypeId" v-if="form.innerCode == null">
          <!-- <el-input v-model="form.vmTypeId" placeholder="请输入设备型号" /> -->
          <el-select v-model="form.vmTypeId" placeholder="请选择设备型号" style="width: 100%">
            <el-option v-for="item in vmTypeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择点位" prop="nodeId">
          <!-- <el-input v-model="form.nodeId" placeholder="请输入点位Id" /> -->
          <el-select v-model="form.nodeId" placeholder="请选择点位" style="width: 100%">
            <el-option v-for="item in nodeList" :key="item.id" :label="item.nodeName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="合作商" v-if="form.innerCode != null">
          <div v-for="item in partnerList" :key="item.id">
            <span v-if="form.partnerId == item.id">{{ item.partnerName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="所属区域" v-if="form.innerCode != null">
          <div v-for="item in regionList" :key="item.id">
            <span v-if="form.regionId == item.id">{{ item.regionName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="设备地址" v-if="form.innerCode != null">
            <span>{{ form.addr }}</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 货道组件 -->
    <ChannelDialog :goodVisible="goodVisible" :goodData="goodData" @handleCloseGood="handleCloseGood"></ChannelDialog>
    <!-- end -->
  </div>
</template>

<script setup name="Vm">
// 导入设备管理相关的API函数：包含设备的增删改查和状态统计功能
import { listVm, getVm, delVm, addVm, updateVm, getVmStatusStats } from "@/api/manage/vm";
// 导入设备类型列表相关的API函数：用于获取设备型号选项
import { listVmType } from "@/api/manage/vmType";
// 导入合作商列表相关的API函数：用于获取合作商选项
import { listPartner } from "@/api/manage/partner";
// 导入分页查询参数加载相关的API函数：用于处理分页和查询参数
import { loadAllParams } from '@/api/page';
// 导入点位列表相关的API函数：用于获取设备安装点位信息
import { listNode } from '@/api/manage/node';
// 导入区域列表相关的API函数：用于获取地区选项
import { listRegion } from "@/api/manage/region";
// 导入策略列表相关的API函数：用于设备策略分配功能
import { listPolicy } from '@/api/manage/policy';
// 导入ECharts图表库：用于设备状态统计图表展示
import * as echarts from 'echarts';
// 导入Element Plus图标组件：用于刷新按钮图标
import { Refresh } from '@element-plus/icons-vue';

// 获取当前实例的代理对象，用于访问全局属性和方法
const { proxy } = getCurrentInstance();
// 使用字典工具方法获取设备状态字典
const { vm_status } = proxy.useDict('vm_status');

// 定义设备列表的响应式引用
const vmList = ref([]);
// 定义表单对话框是否显示的响应式引用
const open = ref(false);
// 定义加载状态的响应式引用
const loading = ref(true);
// 定义搜索面板是否显示的响应式引用
const showSearch = ref(true);
// 定义选中项ID的响应式引用
const ids = ref([]);
// 定义是否单选的响应式引用
const single = ref(true);
// 定义是否多选的响应式引用
const multiple = ref(true);
// 定义总记录数的响应式引用
const total = ref(0);
// 定义对话框标题的响应式引用
const title = ref("");

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态

// 定义包含表单数据、查询参数和验证规则的响应式对象
const data = reactive({
  // 定义一个空的表单对象，用于后续的数据绑定和操作
  form: {},
  // 定义查询参数对象，用于分页和筛选查询
  queryParams: {
    // 当前页码，默认为第1页
    pageNum: 1,
    // 每页显示的记录数，默认为10条
    pageSize: 10,
    // 设备编号，用于查询时指定具体的设备编号
    innerCode: null,
    // 点位名称，用于查询时指定具体的节点
    nodeName: null,
    // 业务类型，用于查询时指定具体的业务类型
    businessType: null,
    // 区域ID，用于查询时指定具体的地区
    regionId: null,
    // 合作商ID，用于查询时指定具体的合作商
    partnerId: null,
    // 设备类型ID，用于查询时指定具体的设备类型
    vmTypeId: null,
    // 设备状态，用于查询时指定具体的设备状态
    vmStatus: null,
    // 运行状态，用于查询时指定具体的运行状态
    runningStatus: null,
    // 策略ID，用于查询时指定具体的策略
    policyId: null,
  },
  rules: {
    nodeId: [
      { required: true, message: "点位Id不能为空", trigger: "blur" }
    ],
    vmTypeId: [
      { required: true, message: "设备型号不能为空", trigger: "blur" }
    ],
  }
});

/* 设备策略分配 */
const policyList = ref([]);
const policyOpen = ref(false);

// 解构data对象中的queryParams、form和rules，以便在组件中直接使用
const { queryParams, form, rules } = toRefs(data);

/** 查询设备管理列表的函数：获取设备数据并更新表格和图表 */
function getList() {
  // 设置加载状态为true，显示加载动画
  loading.value = true;
  // 调用API获取设备列表，传入查询参数
  listVm(queryParams.value).then(response => {
    // 将API返回的设备数据赋值给设备列表
    vmList.value = response.rows;
    // 将API返回的总记录数赋值给总数变量
    total.value = response.total;
    // 设置加载状态为false，隐藏加载动画
    loading.value = false;

    // 如果图表实例存在，刷新图表数据
    if (chartInstance.value) {
      loadChartData();
    }
  });
}



// 取消按钮处理函数：关闭对话框并重置表单
function cancel() {
  // 关闭主对话框
  open.value = false;
  // 关闭策略分配对话框
  policyOpen.value = false;
  // 重置表单数据
  reset();
}

/** 提交按钮处理函数：验证表单并提交数据（新增或修改） */
function submitForm() {
  // 使用表单引用进行表单验证
  proxy.$refs["vmRef"].validate(valid => {
    // 如果表单验证通过
    if (valid) {
      // 判断是否为修改操作（通过id是否存在判断）
      if (form.value.id != null) {
        // 调用修改设备API
        updateVm(form.value).then(response => {
          // 显示修改成功提示
          proxy.$modal.msgSuccess("修改成功");
          // 关闭对话框
          open.value = false;
          // 刷新设备列表
          getList();
        });
      } else {
        // 调用新增设备API
        addVm(form.value).then(response => {
          // 显示新增成功提示
          proxy.$modal.msgSuccess("新增成功");
          // 关闭对话框
          open.value = false;
          // 刷新设备列表
          getList();
        });
      }
    }
  });
}

function handleUpdatePolicy(row) {
  //1. 为表单赋值设备id和策略id
  form.value.id = row.id;
  form.value.policyId = row.policyId;
  //2. 查询策略列表
  listPolicy(loadAllParams).then((response) => {
    policyList.value = response.rows;
    policyOpen.value = true;
  });
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    innerCode: null,
    channelMaxCapacity: null,
    nodeId: null,
    addr: null,
    lastSupplyTime: null,
    businessType: null,
    regionId: null,
    partnerId: null,
    vmTypeId: null,
    vmStatus: null,
    runningStatus: null,
    longitudes: null,
    latitude: null,
    clientId: null,
    policyId: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("vmRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加设备管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getVm(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改设备管理";
  });
}



/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除设备管理编号为"' + _ids + '"的数据项？').then(function() {
    return delVm(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('manage/vm/export', {
    ...queryParams.value
  }, `vm_${new Date().getTime()}.xlsx`)
}

/* 查询设备类型列表 */
const vmTypeList = ref([]);
function getVmTypeList() {
  listVmType(loadAllParams).then((response) => {
    vmTypeList.value = response.rows;
  });
}

/* 查询合作商列表 */
const partnerList = ref([]);
function getPartnerList() {
  listPartner(loadAllParams).then((response) => {
    partnerList.value = response.rows;
  });
}

/* 查询点位列表 */
const nodeList = ref([]);
function getNodeList() {
  listNode(loadAllParams).then((response) => {
    nodeList.value = response.rows;
  });
}

/* 查询区域列表 */
const regionList = ref([]);
function getRegionList() {
  listRegion(loadAllParams).then((response) => {
    regionList.value = response.rows;
  });
}

// 在组件初始化时加载区域、合作商、点位和设备类型列表
getRegionList();
getPartnerList();
getNodeList();
getVmTypeList();

// 加载设备管理列表
getList();

 // ********************货道********************
// 货道组件
import ChannelDialog from './components/ChannelDialog.vue';
const goodVisible = ref(false); //货道弹层显示隐藏
const goodData = ref({}); //货道信息用来拿取 vmTypeId和innerCode
// 打开货道弹层
const handleGoods = (row) => {
    goodVisible.value = true;
    goodData.value = row;
};
// 关闭货道弹层
const handleCloseGood = () => {
    goodVisible.value = false;
};
// ********************货道end********************

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    // 获取所有设备数据（不分页）
    const allVmParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数值来获取所有数据
      innerCode: null,
    };

    const allVmResponse = await listVm(allVmParams);
    const allVmList = allVmResponse.rows || [];

    // 获取所有合作商数据
    const allPartnerResponse = await listPartner(loadAllParams);
    const allPartnerList = allPartnerResponse.rows || [];

    // 获取所有设备类型数据
    const allVmTypeResponse = await listVmType(loadAllParams);
    const allVmTypeList = allVmTypeResponse.rows || [];

    // 处理数据
    const partnerNames = [];
    const vmTypeData = {};

    // 初始化设备类型数据结构
    allVmTypeList.forEach(vmType => {
      vmTypeData[vmType.name] = [];
    });

    // 初始化合作商数据
    allPartnerList.forEach(partner => {
      partnerNames.push(partner.partnerName);

      // 统计该合作商下各设备类型的数量
      allVmTypeList.forEach(vmType => {
        const count = allVmList.filter(vm =>
          vm.partnerId === partner.id && vm.vmTypeId === vmType.id
        ).length;
        vmTypeData[vmType.name].push(count);
      });
    });

    // 调试日志
    console.log('合作商名称:', partnerNames);
    console.log('设备类型数据:', vmTypeData);

    // 如果没有数据，添加默认数据
    if (partnerNames.length === 0) {
      partnerNames.push('暂无数据');
      Object.keys(vmTypeData).forEach(typeName => {
        vmTypeData[typeName].push(0);
      });
    }

    updateChart(partnerNames, vmTypeData, allVmTypeList);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(partnerNames, vmTypeData, vmTypeList) {
  if (!chartInstance.value) return;

  // 定义颜色数组
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];

  // 构建系列数据
  const series = vmTypeList.map((vmType, index) => ({
    name: vmType.name,
    type: 'bar',
    data: vmTypeData[vmType.name] || [],
    itemStyle: {
      color: colors[index % colors.length]
    }
  }));

  const option = {
    title: {
      text: '合作商设备类型统计',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>';
        params.forEach(param => {
          result += param.marker + param.seriesName + ': ' + param.value + '台<br/>';
        });
        return result;
      }
    },
    legend: {
      data: vmTypeList.map(vmType => vmType.name),
      top: 30,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: partnerNames,
      axisLabel: {
        interval: 0,
        rotate: partnerNames.length > 5 ? 30 : 0,
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '设备数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      },
      axisLabel: {
        fontSize: 12,
        formatter: '{value}'
      },
      minInterval: 1,
      splitNumber: 5
    },
    series: series
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询设备列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

</script>
<style lang="scss" scoped src="./index.scss"></style>
<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
