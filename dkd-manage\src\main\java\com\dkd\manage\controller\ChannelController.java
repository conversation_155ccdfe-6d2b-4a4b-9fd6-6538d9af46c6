package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.dkd.manage.domain.dto.ChannelConfigDto;
import com.dkd.manage.domain.vo.ChannelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Channel;
import com.dkd.manage.service.IChannelService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 售货机货道Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
// 定义REST控制器，处理售货机货道管理相关的HTTP请求
@RestController
@RequestMapping("/manage/channel")
public class ChannelController extends BaseController
{
    // 注入货道业务层接口
    @Autowired
    private IChannelService channelService;

    /**
     * 查询售货机货道列表接口
     * 需要'manage:channel:list'权限
     * 请求路径：GET /manage/channel/list
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:list')")
    @GetMapping("/list")
    public TableDataInfo list(Channel channel)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询货道数据
        List<Channel> list = channelService.selectChannelList(channel);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出售货机货道列表接口
     * 需要'manage:channel:export'权限
     * 请求路径：POST /manage/channel/export
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:export')")
    @Log(title = "售货机货道", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Channel channel)
    {
        // 调用服务层方法查询需要导出的货道数据
        List<Channel> list = channelService.selectChannelList(channel);
        // 创建Excel工具类实例
        ExcelUtil<Channel> util = new ExcelUtil<Channel>(Channel.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "售货机货道数据");
    }

    /**
     * 获取售货机货道详细信息接口
     * 需要'manage:channel:query'权限
     * 请求路径：GET /manage/channel/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取货道详情
        return success(channelService.selectChannelById(id));
    }

    /**
     * 新增售货机货道接口
     * 需要'manage:channel:add'权限
     * 请求路径：POST /manage/channel
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:add')")
    @Log(title = "售货机货道", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Channel channel)
    {
        // 调用服务层方法新增货道
        return toAjax(channelService.insertChannel(channel));
    }

    /**
     * 修改售货机货道接口
     * 需要'manage:channel:edit'权限
     * 请求路径：PUT /manage/channel
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:edit')")
    @Log(title = "售货机货道", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Channel channel)
    {
        // 调用服务层方法修改货道
        return toAjax(channelService.updateChannel(channel));
    }

    /**
     * 删除售货机货道接口
     * 需要'manage:channel:remove'权限
     * 请求路径：DELETE /manage/channel/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:remove')")
    @Log(title = "售货机货道", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的货道
        return toAjax(channelService.deleteChannelByIds(ids));
    }

    /**
     * 根据售货机编号查询货道列表接口
     * 需要'manage:channel:list'权限
     * 请求路径：GET /manage/channel/list/{innerCode}
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:list')")
    @GetMapping("/list/{innerCode}")
    public AjaxResult listByInnerCode(@PathVariable("innerCode") String innerCode) {
        // 调用服务层方法根据售货机编号查询货道信息
        List<ChannelVo> voList = channelService.selectChannelVoListByInnerCode(innerCode);
        // 返回查询结果
        return success(voList);
    }

    /**
     * 售货机货道关联商品接口
     * 需要'manage:channel:edit'权限
     * 请求路径：PUT /manage/channel/config
     */
    @PreAuthorize("@ss.hasPermi('manage:channel:edit')")
    @Log(title = "售货机货道", businessType = BusinessType.UPDATE)
    @PutMapping("/config")
    public AjaxResult setChannel(@RequestBody ChannelConfigDto channelConfigDto) {
        // 调用服务层方法实现货道与商品的绑定
        return toAjax(channelService.setChannel(channelConfigDto));
    }
}





// @RestController
// @RequestMapping("/manage/channel")
// public class ChannelController extends BaseController
// {
//     @Autowired
//     private IChannelService channelService;

//     /**
//      * 查询售货机货道列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Channel channel)
//     {
//         startPage();
//         List<Channel> list = channelService.selectChannelList(channel);
//         return getDataTable(list);
//     }

//     /**
//      * 导出售货机货道列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:export')")
//     @Log(title = "售货机货道", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Channel channel)
//     {
//         List<Channel> list = channelService.selectChannelList(channel);
//         ExcelUtil<Channel> util = new ExcelUtil<Channel>(Channel.class);
//         util.exportExcel(response, list, "售货机货道数据");
//     }

//     /**
//      * 获取售货机货道详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(channelService.selectChannelById(id));
//     }

//     /**
//      * 新增售货机货道
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:add')")
//     @Log(title = "售货机货道", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Channel channel)
//     {
//         return toAjax(channelService.insertChannel(channel));
//     }

//     /**
//      * 修改售货机货道
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:edit')")
//     @Log(title = "售货机货道", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Channel channel)
//     {
//         return toAjax(channelService.updateChannel(channel));
//     }

//     /**
//      * 删除售货机货道
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:remove')")
//     @Log(title = "售货机货道", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(channelService.deleteChannelByIds(ids));
//     }

//     /**
//      * 根据售货机编号查询货道列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:list')")
//     @GetMapping("/list/{innerCode}")
//     public AjaxResult listByInnerCode(@PathVariable("innerCode") String innerCode) {
//         List<ChannelVo> voList = channelService.selectChannelVoListByInnerCode(innerCode);
//         return success(voList);
//     }

//     /**
//      * 货道关联商品
//      */
//     @PreAuthorize("@ss.hasPermi('manage:channel:edit')")
//     @Log(title = "售货机货道", businessType = BusinessType.UPDATE)
//     @PutMapping("/config")
//     public AjaxResult setChannel(@RequestBody ChannelConfigDto channelConfigDto) {
//         return toAjax(channelService.setChannel(channelConfigDto));
//     }


// }
