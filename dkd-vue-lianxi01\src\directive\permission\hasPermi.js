/**
 * v-hasPermi 操作权限处理指令
 * 这是一个Vue自定义指令，用于根据用户权限动态控制DOM元素的显示和隐藏
 * 使用方法：v-hasPermi="['system:user:add', 'system:user:edit']"
 * Copyright (c) 2019 ruoyi
 */

// 导入用户状态管理模块，用于获取当前登录用户的权限信息
import useUserStore from '@/store/modules/user'

// 导出默认的Vue自定义指令对象
export default {
  // mounted生命周期钩子：当指令绑定的DOM元素被插入到页面中时执行
  // el: 指令绑定的DOM元素
  // binding: 指令绑定对象，包含指令的值、参数、修饰符等信息
  // vnode: Vue编译生成的虚拟节点
  mounted(el, binding, vnode) {
    // 从指令绑定对象中解构出value值，即开发者传入的权限数组
    const { value } = binding
    // 定义超级管理员权限标识，拥有此权限的用户可以访问系统所有功能
    const all_permission = "*:*:*";
    // 从用户状态管理中获取当前用户拥有的权限列表
    const permissions = useUserStore().permissions

    // 检查传入的权限值是否有效：必须是非空的数组类型
    if (value && value instanceof Array && value.length > 0) {
      // 将传入的权限数组赋值给permissionFlag变量，用于后续的权限匹配检查
      const permissionFlag = value

      // 使用数组的some方法检查用户是否拥有所需权限中的任意一个
      // some方法：只要数组中有一个元素满足条件就返回true
      const hasPermissions = permissions.some(permission => {
        // 权限检查逻辑：用户拥有超级管理员权限 或者 用户权限列表包含所需权限中的任意一个
        return all_permission === permission || permissionFlag.includes(permission)
      })

      // 如果用户没有所需的任何权限，则隐藏该DOM元素
      if (!hasPermissions) {
        // 检查元素是否有父节点，如果有则从父节点中移除当前元素（实现隐藏效果）
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      // 如果传入的权限值无效（不是数组、为空数组或为null/undefined），抛出错误
      // 提示开发者正确使用该指令，必须传入有效的权限数组
      throw new Error(`请设置操作权限标签值`)
    }
  }
}
 