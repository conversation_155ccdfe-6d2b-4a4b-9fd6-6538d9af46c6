package com.dkd.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.system.domain.SysConfig;
import com.dkd.system.service.ISysConfigService;

/**
 * 参数配置 信息操作处理
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块参数配置相关的HTTP请求
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController
{
    // 注入系统参数配置业务层接口
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取参数配置列表接口
     * 需要'system:config:list'权限
     * 请求路径：GET /system/config/list
     */
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysConfig config)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询参数配置数据
        List<SysConfig> list = configService.selectConfigList(config);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出参数配置数据接口
     * 需要'system:config:export'权限
     * 请求路径：POST /system/config/export
     */
    @Log(title = "参数管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysConfig config)
    {
        // 调用服务层方法查询需要导出的参数配置数据
        List<SysConfig> list = configService.selectConfigList(config);
        // 创建Excel工具类实例
        ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息接口
     * 需要'system:config:query'权限
     * 请求路径：GET /system/config/{configId}
     */
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable Long configId)
    {
        // 调用服务层方法获取参数配置详情
        return success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值接口
     * 无需特殊权限
     * 请求路径：GET /system/config/configKey/{configKey}
     */
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey)
    {
        // 调用服务层方法根据参数键名查询参数值
        return success(configService.selectConfigByKey(configKey));
    }

    /**
     * 新增参数配置接口
     * 需要'system:config:add'权限
     * 请求路径：POST /system/config
     */
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysConfig config)
    {
        // 校验参数键名是否唯一
        if (!configService.checkConfigKeyUnique(config))
        {
            return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        // 设置创建人
        config.setCreateBy(getUsername());
        // 调用服务层方法新增参数配置
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置接口
     * 需要'system:config:edit'权限
     * 请求路径：PUT /system/config
     */
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysConfig config)
    {
        // 校验参数键名是否唯一
        if (!configService.checkConfigKeyUnique(config))
        {
            return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        // 设置更新人
        config.setUpdateBy(getUsername());
        // 调用服务层方法修改参数配置
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除参数配置接口
     * 需要'system:config:remove'权限
     * 请求路径：DELETE /system/config/{configIds}
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        // 调用服务层方法删除指定ID的参数配置
        configService.deleteConfigByIds(configIds);
        // 返回操作成功结果
        return success();
    }

    /**
     * 刷新参数缓存接口
     * 需要'system:config:remove'权限
     * 请求路径：DELETE /system/config/refreshCache
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        // 调用服务层方法刷新参数缓存
        configService.resetConfigCache();
        // 返回操作成功结果
        return success();
    }
}




// @RestController
// @RequestMapping("/system/config")
// public class SysConfigController extends BaseController
// {
//     @Autowired
//     private ISysConfigService configService;

//     /**
//      * 获取参数配置列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:config:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysConfig config)
//     {
//         startPage();
//         List<SysConfig> list = configService.selectConfigList(config);
//         return getDataTable(list);
//     }

//     @Log(title = "参数管理", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('system:config:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysConfig config)
//     {
//         List<SysConfig> list = configService.selectConfigList(config);
//         ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
//         util.exportExcel(response, list, "参数数据");
//     }

//     /**
//      * 根据参数编号获取详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('system:config:query')")
//     @GetMapping(value = "/{configId}")
//     public AjaxResult getInfo(@PathVariable Long configId)
//     {
//         return success(configService.selectConfigById(configId));
//     }

//     /**
//      * 根据参数键名查询参数值
//      */
//     @GetMapping(value = "/configKey/{configKey}")
//     public AjaxResult getConfigKey(@PathVariable String configKey)
//     {
//         return success(configService.selectConfigByKey(configKey));
//     }

//     /**
//      * 新增参数配置
//      */
//     @PreAuthorize("@ss.hasPermi('system:config:add')")
//     @Log(title = "参数管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysConfig config)
//     {
//         if (!configService.checkConfigKeyUnique(config))
//         {
//             return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
//         }
//         config.setCreateBy(getUsername());
//         return toAjax(configService.insertConfig(config));
//     }

//     /**
//      * 修改参数配置
//      */
//     @PreAuthorize("@ss.hasPermi('system:config:edit')")
//     @Log(title = "参数管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysConfig config)
//     {
//         if (!configService.checkConfigKeyUnique(config))
//         {
//             return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
//         }
//         config.setUpdateBy(getUsername());
//         return toAjax(configService.updateConfig(config));
//     }

//     /**
//      * 删除参数配置
//      */
//     @PreAuthorize("@ss.hasPermi('system:config:remove')")
//     @Log(title = "参数管理", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{configIds}")
//     public AjaxResult remove(@PathVariable Long[] configIds)
//     {
//         configService.deleteConfigByIds(configIds);
//         return success();
//     }

//     /**
//      * 刷新参数缓存
//      */
//     @PreAuthorize("@ss.hasPermi('system:config:remove')")
//     @Log(title = "参数管理", businessType = BusinessType.CLEAN)
//     @DeleteMapping("/refreshCache")
//     public AjaxResult refreshCache()
//     {
//         configService.resetConfigCache();
//         return success();
//     }
// }
