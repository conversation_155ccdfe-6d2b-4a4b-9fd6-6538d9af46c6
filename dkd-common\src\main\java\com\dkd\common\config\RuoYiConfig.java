// 定义包路径
package com.dkd.common.config;

// 导入Spring Boot配置相关类
import org.springframework.boot.context.properties.ConfigurationProperties; // 配置属性绑定注解
import org.springframework.stereotype.Component; // Spring组件注解

/**
 * 读取项目相关配置
 * 用于管理系统的全局配置参数
 * 通过@ConfigurationProperties注解自动绑定配置文件中的属性
 * 提供项目名称、版本、文件路径等配置信息的访问接口
 *
 * <AUTHOR>
 */
@Component // 标记为Spring组件，会被Spring容器管理
@ConfigurationProperties(prefix = "ruoyi") // 绑定配置文件中以ruoyi为前缀的属性
public class RuoYiConfig
{
    /** 项目名称 */
    private String name; // 存储项目名称

    /** 版本 */
    private String version; // 存储项目版本号

    /** 版权年份 */
    private String copyrightYear; // 存储版权年份

    /** 上传路径 */
    private static String profile; // 存储文件上传的基础路径

    /** 获取地址开关 */
    private static boolean addressEnabled; // 控制是否启用IP地址获取功能

    /** 验证码类型 */
    private static String captchaType; // 存储验证码类型配置

    /**
     * 获取项目名称
     *
     * @return 项目名称
     */
    public String getName()
    {
        return name; // 返回项目名称
    }

    /**
     * 设置项目名称
     *
     * @param name 项目名称
     */
    public void setName(String name)
    {
        this.name = name; // 设置项目名称
    }

    /**
     * 获取项目版本
     *
     * @return 项目版本
     */
    public String getVersion()
    {
        return version; // 返回项目版本
    }

    /**
     * 设置项目版本
     *
     * @param version 项目版本
     */
    public void setVersion(String version)
    {
        this.version = version; // 设置项目版本
    }

    /**
     * 获取版权年份
     *
     * @return 版权年份
     */
    public String getCopyrightYear()
    {
        return copyrightYear; // 返回版权年份
    }

    /**
     * 设置版权年份
     *
     * @param copyrightYear 版权年份
     */
    public void setCopyrightYear(String copyrightYear)
    {
        this.copyrightYear = copyrightYear; // 设置版权年份
    }

    /**
     * 获取文件上传基础路径
     *
     * @return 上传路径
     */
    public static String getProfile()
    {
        return profile; // 返回文件上传基础路径
    }

    /**
     * 设置文件上传基础路径
     *
     * @param profile 上传路径
     */
    public void setProfile(String profile)
    {
        RuoYiConfig.profile = profile; // 设置文件上传基础路径
    }

    /**
     * 判断是否启用IP地址获取功能
     *
     * @return true表示启用，false表示禁用
     */
    public static boolean isAddressEnabled()
    {
        return addressEnabled; // 返回IP地址获取功能开关状态
    }

    /**
     * 设置IP地址获取功能开关
     *
     * @param addressEnabled true表示启用，false表示禁用
     */
    public void setAddressEnabled(boolean addressEnabled)
    {
        RuoYiConfig.addressEnabled = addressEnabled; // 设置IP地址获取功能开关
    }

    /**
     * 获取验证码类型
     *
     * @return 验证码类型
     */
    public static String getCaptchaType() {
        return captchaType; // 返回验证码类型
    }

    /**
     * 设置验证码类型
     *
     * @param captchaType 验证码类型
     */
    public void setCaptchaType(String captchaType) {
        RuoYiConfig.captchaType = captchaType; // 设置验证码类型
    }

    /**
     * 获取导入上传路径
     * 用于存放Excel等文件导入时的临时文件
     *
     * @return 导入文件存放路径
     */
    public static String getImportPath()
    {
        return getProfile() + "/import"; // 返回基础路径加上import子目录
    }

    /**
     * 获取头像上传路径
     * 用于存放用户头像图片文件
     *
     * @return 头像文件存放路径
     */
    public static String getAvatarPath()
    {
        return getProfile() + "/avatar"; // 返回基础路径加上avatar子目录
    }

    /**
     * 获取下载路径
     * 用于存放系统生成的可下载文件
     *
     * @return 下载文件存放路径
     */
    public static String getDownloadPath()
    {
        return getProfile() + "/download/"; // 返回基础路径加上download子目录
    }

    /**
     * 获取上传路径
     * 用于存放用户上传的各种文件
     *
     * @return 上传文件存放路径
     */
    public static String getUploadPath()
    {
        return getProfile() + "/upload"; // 返回基础路径加上upload子目录
    }
}
