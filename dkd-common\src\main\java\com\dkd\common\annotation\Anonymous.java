// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.Documented; // 用于标记注解是否包含在JavaDoc中
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型

/**
 * 匿名访问不鉴权注解
 * 用于标记某个方法或类可以匿名访问，不需要进行权限验证
 * 通常用于登录、注册、公开API等不需要身份验证的接口
 *
 * <AUTHOR>
 */
@Target({ ElementType.METHOD, ElementType.TYPE }) // 指定注解可以应用于方法和类型（类、接口、枚举）
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Documented // 指定注解会被包含在JavaDoc中
public @interface Anonymous // 定义匿名访问注解接口
{
    // 注解体为空，仅作为标记使用
}
