<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dkd.manage.mapper.JobMapper">
    
    <resultMap type="Job" id="JobResult">
        <result property="id"    column="id"    />
        <result property="alertValue"    column="alert_value"    />
    </resultMap>

    <sql id="selectJobVo">
        select id, alert_value from tb_job
    </sql>

    <select id="selectJobList" parameterType="Job" resultMap="JobResult">
        <include refid="selectJobVo"/>
        <where>  
            <if test="alertValue != null "> and alert_value = #{alertValue}</if>
        </where>
    </select>
    
    <select id="selectJobById" parameterType="Long" resultMap="JobResult">
        <include refid="selectJobVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertJob" parameterType="Job" useGeneratedKeys="true" keyProperty="id">
        insert into tb_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alertValue != null">alert_value,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alertValue != null">#{alertValue},</if>
         </trim>
    </insert>

    <update id="updateJob" parameterType="Job">
        update tb_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="alertValue != null">alert_value = #{alertValue},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJobById" parameterType="Long">
        delete from tb_job where id = #{id}
    </delete>

    <delete id="deleteJobByIds" parameterType="String">
        delete from tb_job where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>