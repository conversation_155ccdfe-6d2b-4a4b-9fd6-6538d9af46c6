// 包声明：定义当前类所属的包路径，位于安全框架的处理器包中
package com.dkd.framework.security.handle;

// 导入Java IO异常类，用于处理输入输出异常
import java.io.IOException;
// 导入Servlet异常类，用于处理Servlet相关异常
import javax.servlet.ServletException;
// 导入HTTP请求接口，用于获取请求信息
import javax.servlet.http.HttpServletRequest;
// 导入HTTP响应接口，用于设置响应信息
import javax.servlet.http.HttpServletResponse;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring配置注解，标记此类为配置类
import org.springframework.context.annotation.Configuration;
// 导入Spring Security认证接口，包含用户认证信息
import org.springframework.security.core.Authentication;
// 导入Spring Security退出成功处理器接口
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
// 导入阿里巴巴FastJSON库，用于JSON序列化和反序列化
import com.alibaba.fastjson2.JSON;
// 导入系统常量类
import com.dkd.common.constant.Constants;
// 导入Ajax响应结果类，用于统一的响应格式
import com.dkd.common.core.domain.AjaxResult;
// 导入登录用户模型类
import com.dkd.common.core.domain.model.LoginUser;
// 导入消息工具类，用于国际化消息处理
import com.dkd.common.utils.MessageUtils;
// 导入Servlet工具类，用于响应处理
import com.dkd.common.utils.ServletUtils;
// 导入字符串工具类
import com.dkd.common.utils.StringUtils;
// 导入异步管理器，用于异步执行任务
import com.dkd.framework.manager.AsyncManager;
// 导入异步工厂类，用于创建异步任务
import com.dkd.framework.manager.factory.AsyncFactory;
// 导入Token服务类，用于令牌管理
import com.dkd.framework.web.service.TokenService;

/**
 * 自定义退出成功处理类
 * 这个类实现了Spring Security的LogoutSuccessHandler接口，
 * 用于处理用户成功退出登录后的操作，包括清理缓存、记录日志等。
 *
 * 主要功能：
 * 1. 清除用户的JWT令牌缓存
 * 2. 记录用户退出登录的日志
 * 3. 返回统一格式的成功响应
 * 4. 异步处理日志记录，提高响应速度
 *
 * 工作流程：
 * 用户退出 → 获取用户信息 → 删除令牌缓存 → 记录退出日志 → 返回成功响应
 *
 * <AUTHOR>
 */
@Configuration // 将此类标记为Spring配置类，由Spring容器管理
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler
{
    // 注入Token服务，用于令牌的管理和用户信息获取
    @Autowired
    private TokenService tokenService;

    /**
     * 退出成功处理方法
     * 当用户成功退出登录时，Spring Security会调用此方法来处理后续操作
     *
     * @param request HTTP请求对象，包含客户端的请求信息
     * @param response HTTP响应对象，用于向客户端发送响应
     * @param authentication 认证对象，包含用户的认证信息（可能为null）
     * @throws IOException 当输入输出操作发生异常时抛出
     * @throws ServletException 当Servlet处理过程中发生异常时抛出
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException
    {
        // 第一步：从HTTP请求中获取当前登录的用户信息
        // 通过JWT令牌解析出用户信息，如果令牌无效或不存在则返回null
        LoginUser loginUser = tokenService.getLoginUser(request);

        // 第二步：检查用户信息是否有效
        if (StringUtils.isNotNull(loginUser))
        {
            // 获取用户名，用于日志记录
            String userName = loginUser.getUsername();

            // 第三步：删除用户的令牌缓存记录
            // 从Redis缓存中删除该用户的登录信息，使JWT令牌失效
            // 这样即使客户端还持有令牌，也无法再次访问受保护的资源
            tokenService.delLoginUser(loginUser.getToken());

            // 第四步：异步记录用户退出日志
            // 使用异步管理器执行日志记录任务，避免阻塞响应
            // Constants.LOGOUT表示退出操作类型
            // MessageUtils.message("user.logout.success")获取国际化的成功消息
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, MessageUtils.message("user.logout.success")));
        }

        // 第五步：向客户端返回退出成功的响应
        // AjaxResult.success()创建一个成功的响应对象
        // JSON.toJSONString()将响应对象转换为JSON字符串
        // ServletUtils.renderString()将JSON字符串写入HTTP响应中
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.success(MessageUtils.message("user.logout.success"))));
    }
} // 类结束标记
