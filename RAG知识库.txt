# 智能售货机管理系统 naxie- 完整知识库

## 项目概述

### 项目名称
- 中文名：智能售货机管理系统

### 项目描述
这是一个基于Spring Boot + Vue 3的前后端分离的智能售货机管理系统，主要用于管理自助售货机的投放、运营、维护、撤机等全生命周期业务。

## 技术架构

### 后端技术栈
- **核心框架**: Spring Boot 2.5.15
- **安全框架**: Spring Security + JWT
- **持久层框架**: MyBatis + MyBatis-Plus
- **数据库连接池**: Druid 1.2.20
- **缓存**: Redis
- **定时任务**: Quartz
- **API文档**: Swagger 3.0.0
- **AI大模型：阿里云百炼大模型平台的通义千问大模型
- **代码生成**: Velocity 2.3
- **JSON处理**: FastJSON 2.0.43
- **Excel处理**: Apache POI 4.1.2, Easy Excel
- **验证码**: Kaptcha 2.3.3
- **短信验证码服务：国阳云短信服务
- **分页插件**: PageHelper 1.4.7
- **系统监控**: OSHI 6.5.0
- **AI集成**: LangChain4J 1.0.1 + Spring AI Alibaba
- **对象存储服务：x-file-storage + 阿里云OSS

### 前端技术栈
- **核心框架**: Vue 3 + Composition API
- **构建工具**: Vite 5.0.4
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **图表库**: ECharts
- **样式预处理**: SCSS
- **代码规范**: ESLint + Prettier

### 数据库
- **主数据库**: MySQL 8.0+
- **缓存数据库**: Redis 6.0+

## 项目结构

### 后端模块结构
```
dkd-parent/
├── dkd-admin/          # 启动模块
├── dkd-common/         # 通用工具模块
├── dkd-framework/      # 框架核心模块
├── dkd-system/         # 系统管理模块
├── dkd-manage/         # 售货机管理模块
├── dkd-quartz/         # 定时任务模块
└── dkd-generator/      # 代码生成模块
```

### 前端目录结构
```
dkd-vue-lianxi01/
├── public/             # 静态资源
├── src/
│   ├── api/           # API接口
│   ├── assets/        # 资源文件
│   ├── components/    # 公共组件
│   ├── layout/        # 布局组件
│   ├── router/        # 路由配置
│   ├── store/         # 状态管理
│   ├── utils/         # 工具函数
│   └── views/         # 页面组件
├── package.json       # 依赖配置
└── vite.config.js     # 构建配置
```

## 核心功能模块

### 1. 系统管理模块
#### 用户管理
- **功能**: 系统用户的增删改查
- **权限**: 支持角色权限控制
- **操作**: 用户新增、编辑、删除、状态切换、密码重置
- **特性**: 支持用户导入导出、头像上传

#### 角色管理
- **功能**: 角色权限分配管理
- **类型**:
  - 管理员角色
  - 运营员角色 (1002)
  - 维修员角色 (1003)
- **权限**: 菜单权限、数据权限配置

#### 菜单管理
- **功能**: 系统菜单的层级管理
- **类型**: 目录(M)、菜单(C)、按钮(F)
- **权限**: 支持权限标识配置

#### 部门管理
- **功能**: 组织架构管理
- **特性**: 树形结构、层级管理

#### 岗位管理
- **功能**: 岗位信息维护
- **应用**: 员工岗位分配

#### 字典管理
- **功能**: 系统字典数据维护
- **应用**: 下拉选项、状态配置

#### 参数设置
- **功能**: 系统参数配置
- **类型**: 系统内置参数、用户自定义参数

#### 通知公告
- **功能**: 系统公告发布管理
- **特性**: 富文本编辑、发布状态控制

#### 日志管理
- **操作日志**: 记录用户操作行为
- **登录日志**: 记录用户登录信息
- **系统日志**: 记录系统运行日志

### 2. 售货机管理模块 (dkd-manage)

#### 设备管理 (VendingMachine)
- **功能**: 售货机设备的全生命周期管理
- **字段**:
  - id: 主键
  - innerCode: 设备编号(8位UUID)
  - vmStatus: 设备状态 (0:未投放, 1:运营, 3:撤机)
  - runningStatus: 运行状态 (JSON格式)
  - nodeId: 点位ID
  - vmTypeId: 设备型号ID
  - partnerId: 合作商ID
  - regionId: 区域ID
- **操作**:
  - 设备新增: 自动生成设备编号、创建货道
  - 设备编辑: 支持点位变更、策略调整
  - 设备删除: 批量删除支持
  - 状态管理: 投放、运营、撤机状态切换

#### 点位管理 (Node)
- **功能**: 售货机投放点位管理
- **字段**:
  - nodeName: 点位名称
  - address: 详细地址
  - businessType: 商圈类型
  - regionId: 区域ID
  - partnerId: 合作商ID
- **特性**:
  - 商圈类型分类
  - 区域归属管理
  - 设备详情查看

#### 区域管理 (Region)
- **功能**: 地理区域划分管理
- **结构**: 树形层级结构
- **应用**: 设备区域归属、人员管理范围

#### 合作商管理 (Partner)
- **功能**: 合作伙伴信息管理
- **字段**:
  - partnerName: 合作商名称
  - contactPerson: 联系人
  - contactPhone: 联系电话
  - profitRatio: 分成比例
- **应用**: 设备归属、收益分成

#### 设备型号管理 (VmType)
- **功能**: 售货机型号规格管理
- **字段**:
  - name: 型号名称
  - vmRow: 货道行数
  - vmCol: 货道列数
  - channelMaxCapacity: 货道容量
- **应用**: 设备创建时的规格配置

#### 货道管理 (Channel)
- **功能**: 售货机货道信息管理
- **字段**:
  - channelCode: 货道编号 (如: 1-1, 1-2)
  - skuId: 商品ID
  - maxCapacity: 最大容量
  - currentCapacity: 当前容量
  - lastSupplyTime: 上次补货时间
- **特性**: 自动创建、容量管理、补货记录

#### 商品管理 (Sku)
- **功能**: 售货机商品信息管理
- **字段**:
  - skuName: 商品名称
  - skuImage: 商品图片
  - brandName: 品牌名称
  - unit: 计量单位
  - price: 商品价格
  - classId: 商品类别ID
- **特性**: 图片上传、价格管理、分类管理

#### 商品类别管理 (SkuClass)
- **功能**: 商品分类管理
- **结构**: 树形分类结构
- **应用**: 商品归类、统计分析

#### 员工管理 (Emp)
- **功能**: 运维人员管理
- **字段**:
  - userName: 用户名
  - realName: 真实姓名
  - mobile: 手机号
  - roleCode: 角色编码
  - status: 员工状态 (0:禁用, 1:启用)
  - regionId: 所属区域
- **角色类型**:
  - 运营员 (1002): 负责补货、收益管理
  - 维修员 (1003): 负责设备维修、故障处理

### 3. 工单管理系统

#### 工单类型 (TaskType)
- **投放工单** (typeId=1): 新设备投放
- **补货工单** (typeId=2): 设备补货
- **维修工单** (typeId=3): 设备维修
- **撤机工单** (typeId=4): 设备撤机

#### 工单状态
- **待办** (taskStatus=1): 工单创建，等待处理
- **进行中** (taskStatus=2): 工单执行中
- **取消** (taskStatus=3): 工单已取消
- **完成** (taskStatus=4): 工单已完成

#### 工单管理 (Task)
- **字段**:
  - taskCode: 工单编号
  - taskStatus: 工单状态
  - productTypeId: 工单类型ID
  - innerCode: 设备编号
  - userId: 执行人ID
  - userName: 执行人姓名
  - assignorId: 指派人ID
  - createType: 创建类型 (0:自动, 1:手动)
  - desc: 工单描述
  - addr: 设备地址

#### 工单详情 (TaskDetails)
- **功能**: 补货工单的详细信息
- **字段**:
  - taskId: 工单ID
  - channelCode: 货道编号
  - expectCapacity: 期望补货数量
  - skuId: 商品ID
  - skuName: 商品名称
  - skuImage: 商品图片

#### 工单完成功能
- **投放工单完成**: 设备状态改为运营 (vmStatus=1)
- **维修工单完成**: 运行状态改为正常 (runningStatus={"statusCode":"1001","status":true})
- **撤机工单完成**: 删除设备记录
- **补货工单完成**: 更新货道库存

### 4. 策略管理

#### 策略模板 (Policy)
- **功能**: 设备运营策略配置
- **字段**:
  - policyName: 策略名称
  - discount: 折扣率
  - policyType: 策略类型

#### 策略应用
- **设备绑定**: 设备可绑定运营策略
- **动态调整**: 支持策略实时切换

### 5. 数据统计与分析

#### 设备状态统计
- **投放状态统计**: 未投放、运营中、已撤机
- **运行状态统计**: 正常、异常设备数量
- **区域分布**: 各区域设备分布情况
- **型号统计**: 不同型号设备数量

#### 销售数据分析
- **销售排行**: 商品销售排行榜
- **收益统计**: 设备收益数据
- **趋势分析**: 销售趋势图表

#### 工单统计
- **工单状态**: 各状态工单数量统计
- **处理效率**: 工单完成时间分析
- **人员工作量**: 员工工单处理统计

### 6. 系统监控

#### 服务监控
- **在线用户**: 当前在线用户监控
- **数据监控**: 缓存监控、SQL监控
- **服务器信息**: CPU、内存、磁盘使用情况

#### 定时任务
- **任务管理**: 定时任务的增删改查
- **执行日志**: 任务执行记录
- **任务调度**: Cron表达式配置

### 7. 开发工具

#### 代码生成器
- **功能**: 根据数据库表自动生成CRUD代码
- **支持**: 前后端代码一键生成
- **模板**: 基于Velocity模板引擎
- **配置**: 支持字段类型、验证规则配置

#### 表单构建
- **功能**: 可视化表单设计器
- **组件**: 丰富的表单组件库
- **预览**: 实时预览表单效果
- **导出**: 生成Vue组件代码

### 8. AI智能客服

#### 功能特性
- **智能对话**: 基于LangChain4J的AI对话
- **知识库**: 支持RAG知识库问答
- **多模型支持**: 集成阿里云通义千问
- **实时交互**: WebSocket实时通信

#### 技术实现
- **后端**: Spring AI + LangChain4J
- **前端**: Vue 3 + WebSocket
- **AI模型**: 阿里云DashScope API
- **界面**: 现代化聊天界面设计

## 数据库设计

### 核心表结构

#### 设备相关表
- **tb_vending_machine**: 售货机主表
- **tb_channel**: 货道信息表
- **tb_vm_type**: 设备型号表
- **tb_node**: 点位信息表
- **tb_region**: 区域信息表
- **tb_partner**: 合作商信息表

#### 商品相关表
- **tb_sku**: 商品信息表
- **tb_sku_class**: 商品分类表

#### 工单相关表
- **tb_task**: 工单主表
- **tb_task_details**: 工单详情表
- **tb_task_type**: 工单类型表

#### 人员相关表
- **tb_emp**: 员工信息表
- **tb_role**: 角色信息表

#### 策略相关表
- **tb_policy**: 策略模板表

### 关键字段说明

#### 设备状态字段
- **vm_status**: 设备状态
  - 0: 未投放
  - 1: 运营
  - 3: 撤机

#### 运行状态字段
- **running_status**: 运行状态 (JSON格式)
  - 正常: {"statusCode":"1001","status":true}
  - 异常: 其他值或空值

#### 工单状态字段
- **task_status**: 工单状态
  - 1: 待办
  - 2: 进行中
  - 3: 取消
  - 4: 完成

#### 员工状态字段
- **status**: 员工状态
  - 0: 禁用
  - 1: 启用

## 业务流程

### 设备投放流程
1. **点位申请**: 在系统中创建投放点位
2. **设备创建**: 添加设备信息，绑定点位
3. **投放工单**: 创建投放工单，分配给运营员
4. **现场投放**: 运营员到现场完成设备投放
5. **工单完成**: 确认完成，设备状态变为运营

### 设备维修流程
1. **故障上报**: 系统检测或人工上报设备故障
2. **维修工单**: 自动或手动创建维修工单
3. **工单分配**: 分配给对应区域的维修员
4. **现场维修**: 维修员到现场处理故障
5. **工单完成**: 确认完成，设备状态恢复正常

### 设备补货流程
1. **库存监控**: 系统监控货道库存水平
2. **补货工单**: 库存不足时创建补货工单
3. **工单分配**: 分配给运营员
4. **现场补货**: 运营员到现场补充商品
5. **工单完成**: 更新货道库存信息

### 设备撤机流程
1. **撤机申请**: 申请设备撤机
2. **撤机工单**: 创建撤机工单
3. **现场撤机**: 运营员到现场撤回设备
4. **工单完成**: 删除设备记录

## 权限管理

### 角色权限
- **超级管理员**: 所有功能权限
- **管理员**: 系统管理、业务管理权限
- **运营员**: 补货工单、收益查看权限
- **维修员**: 维修工单、设备状态权限

### 数据权限
- **全部数据权限**: 查看所有数据
- **自定数据权限**: 查看指定部门数据
- **部门数据权限**: 查看本部门数据
- **部门及以下数据权限**: 查看本部门及子部门数据
- **仅本人数据权限**: 只查看自己的数据

### 菜单权限
- **目录权限**: 菜单目录访问权限
- **菜单权限**: 具体页面访问权限
- **按钮权限**: 页面内操作按钮权限

## 系统配置

### 环境配置
- **开发环境**: development
- **测试环境**: staging
- **生产环境**: production

### 数据库配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password:
    timeout: 10s
```

### 文件上传配置
- **上传路径**: /profile/upload/
- **允许格式**: jpg, jpeg, png, gif, bmp, doc, docx, xls, xlsx, pdf
- **文件大小限制**: 10MB

## 部署说明

### 后端部署
1. **环境要求**: JDK 1.8+, MySQL 8.0+, Redis 6.0+
2. **构建命令**: `mvn clean package`
3. **启动命令**: `java -jar dkd-admin.jar`
4. **配置文件**: application.yml, application-prod.yml

### 前端部署
1. **环境要求**: Node.js 16+, npm 8+
2. **安装依赖**: `npm install`
3. **开发运行**: `npm run dev`
4. **生产构建**: `npm run build:prod`
5. **预览**: `npm run preview`

### Nginx配置
```nginx
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html/dist;
        try_files $uri $uri/ /index.html;
    }

    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 常用操作指南

### 用户管理操作
1. **新增用户**: 系统管理 → 用户管理 → 新增
2. **分配角色**: 编辑用户 → 选择角色
3. **重置密码**: 用户列表 → 重置密码
4. **用户导入**: 用户管理 → 导入 → 下载模板

### 设备管理操作
1. **添加设备**: 设备管理 → 设备管理 → 新增
2. **设备投放**: 创建投放工单 → 分配运营员 → 完成投放
3. **设备维修**: 创建维修工单 → 分配维修员 → 完成维修
4. **设备撤机**: 创建撤机工单 → 完成撤机

### 工单管理操作
1. **创建工单**: 工单管理 → 新增工单
2. **分配工单**: 选择执行人员
3. **工单处理**: 查看详情 → 开始工单 → 确认完成
4. **工单查询**: 按状态、类型、时间筛选

### 数据统计查看
1. **首页概览**: 登录后首页查看关键指标
2. **设备统计**: 设备管理 → 设备状态统计
3. **销售分析**: 数据分析 → 销售统计
4. **工单统计**: 工单管理 → 统计分析

## 常见问题解决

### 登录问题
- **验证码错误**: 刷新页面重新获取
- **用户名密码错误**: 检查用户状态是否正常
- **权限不足**: 联系管理员分配权限

### 功能使用问题
- **菜单不显示**: 检查角色权限配置
- **数据不显示**: 检查数据权限范围
- **操作按钮不显示**: 检查按钮权限配置

### 系统性能问题
- **页面加载慢**: 检查网络连接和服务器性能
- **数据查询慢**: 优化查询条件，检查数据库索引
- **文件上传失败**: 检查文件大小和格式限制

## 开发规范

### 代码规范
- **命名规范**: 驼峰命名法
- **注释规范**: 类、方法必须添加注释
- **异常处理**: 统一异常处理机制
- **日志规范**: 使用SLF4J日志框架

### 数据库规范
- **表名**: 小写字母，下划线分隔
- **字段名**: 小写字母，下划线分隔
- **主键**: 统一使用id作为主键
- **时间字段**: create_time, update_time

### 接口规范
- **RESTful**: 遵循RESTful API设计规范
- **返回格式**: 统一JSON格式返回
- **状态码**: 使用标准HTTP状态码
- **参数验证**: 统一参数校验机制

### 前端规范
- **组件命名**: PascalCase命名
- **文件命名**: kebab-case命名
- **样式规范**: 使用SCSS预处理器
- **代码格式**: 使用ESLint + Prettier

## 扩展功能

### 移动端支持
- **响应式设计**: 支持移动设备访问
- **PWA支持**: 渐进式Web应用
- **移动端优化**: 触摸操作优化

### 第三方集成
- **支付接口**: 支持多种支付方式
- **短信服务**: 短信通知功能
- **邮件服务**: 邮件通知功能
- **地图服务**: 地理位置服务

### 数据分析
- **报表系统**: 丰富的数据报表
- **图表展示**: 多种图表类型
- **数据导出**: Excel、PDF导出
- **数据备份**: 定期数据备份

## 版本更新记录

### v3.8.7 (当前版本)
- 新增AI智能客服功能
- 优化工单管理流程
- 增强数据统计功能
- 修复已知问题

### 未来规划
- 移动端APP开发
- 物联网设备接入
- 大数据分析平台
- 人工智能优化

## 技术支持

### 开发团队
- **项目负责人**: 系统架构师
- **后端开发**: Spring Boot专家
- **前端开发**: Vue.js专家
- **数据库**: MySQL DBA
- **运维**: DevOps工程师

### 联系方式
- **技术支持**: 提供7x24小时技术支持
- **问题反馈**: 通过系统内置反馈功能
- **文档更新**: 定期更新技术文档
- **培训服务**: 提供系统使用培训

## 详细功能说明

### 工单完成功能详解

#### 功能概述
在工单管理的工单详情对话框中添加了"确认完成"按钮，点击后会根据不同的工单类型自动更新设备状态。

#### 按钮显示逻辑
- **待办状态 (taskStatus = 1)**: 显示"取消工单"和"开始工单"按钮
- **进行中状态 (taskStatus = 2)**: 显示"确认完成"按钮
- **取消状态 (taskStatus = 3)**: 显示"重新创建"按钮
- **完成状态 (taskStatus = 4)**: 不显示操作按钮

#### 业务处理流程
1. **工单状态验证**: 检查工单是否存在，验证工单状态必须为"进行中"
2. **设备信息查询**: 根据工单中的设备编号查询设备信息
3. **根据工单类型处理设备状态**:
   - **投放工单**: 将设备状态改为运营 (vmStatus=1)
   - **维修工单**: 将设备运行状态改为正常 (runningStatus={"statusCode":"1001","status":true})
   - **撤机工单**: 删除设备记录
   - **补货工单**: 不需要更新设备状态，只完成工单
4. **更新工单状态**: 将工单状态设置为"完成"(taskStatus=4)

### AI智能客服功能详解

#### 技术架构
- **后端框架**: Spring AI + LangChain4J
- **AI模型**: 阿里云通义千问 (DashScope API)
- **通信方式**: WebSocket实时通信
- **知识库**: 支持RAG (Retrieval-Augmented Generation)

#### 功能特性
- **智能对话**: 基于大语言模型的自然语言对话
- **上下文理解**: 支持多轮对话上下文记忆
- **知识问答**: 基于项目知识库的专业问答
- **实时响应**: WebSocket确保消息实时传输

#### 使用场景
- **用户咨询**: 系统使用问题咨询
- **故障排查**: 设备故障诊断建议
- **操作指导**: 功能操作步骤指导
- **数据查询**: 业务数据查询帮助

### 数据统计图表功能

#### ECharts集成
- **图表类型**: 柱状图、折线图、饼图、散点图
- **交互功能**: 缩放、筛选、钻取
- **响应式**: 自适应不同屏幕尺寸
- **主题定制**: 支持多种图表主题

#### 统计维度
- **时间维度**: 日、周、月、年统计
- **地域维度**: 按区域、点位统计
- **设备维度**: 按型号、状态统计
- **人员维度**: 按角色、部门统计

### 代码生成器功能

#### 生成能力
- **后端代码**: Controller、Service、Mapper、Entity
- **前端代码**: Vue组件、API接口、路由配置
- **数据库**: 建表SQL、索引优化
- **文档**: API文档、使用说明

#### 模板引擎
- **Velocity模板**: 灵活的代码模板
- **自定义模板**: 支持模板定制
- **变量替换**: 智能变量替换
- **代码格式化**: 自动代码格式化

### 系统监控功能

#### 性能监控
- **JVM监控**: 内存使用、GC情况
- **数据库监控**: 连接池、慢查询
- **缓存监控**: Redis使用情况
- **接口监控**: API响应时间、错误率

#### 日志管理
- **操作日志**: 用户操作记录
- **系统日志**: 系统运行日志
- **错误日志**: 异常错误记录
- **访问日志**: 用户访问记录

## 项目特色功能

### 1. 智能工单分配
- **地理位置**: 根据设备位置自动分配最近的员工
- **工作负载**: 考虑员工当前工单数量
- **技能匹配**: 根据员工技能分配对应工单
- **优先级**: 支持工单优先级设置

### 2. 设备状态实时监控
- **状态同步**: 实时同步设备运行状态
- **异常告警**: 设备异常自动告警
- **预测维护**: 基于历史数据预测维护需求
- **远程诊断**: 支持远程设备诊断

### 3. 数据可视化大屏
- **实时数据**: 实时展示关键业务指标
- **地图展示**: 设备分布地图可视化
- **趋势分析**: 业务趋势图表展示
- **告警中心**: 集中展示系统告警信息

### 4. 移动端适配
- **响应式设计**: 完美适配各种设备
- **触摸优化**: 针对移动设备优化交互
- **离线支持**: 支持离线数据缓存
- **推送通知**: 支持消息推送功能

## API接口详细说明

### 系统管理接口

#### 用户登录接口
- **路径**: POST /login
- **参数**: username, password, code, uuid
- **返回**: token, 用户信息
- **说明**: 用户登录验证，支持验证码

#### 手机号登录接口
- **路径**: POST /sms/phone/login
- **参数**: phone, smsCode
- **返回**: token, 用户信息
- **说明**: 手机号短信验证码登录

#### 获取用户信息接口
- **路径**: GET /getInfo
- **参数**: 无 (需要token)
- **返回**: 用户详细信息、角色、权限
- **说明**: 获取当前登录用户信息

#### 用户注册接口
- **路径**: POST /register
- **参数**: 用户注册信息
- **返回**: 注册结果
- **说明**: 用户注册功能

### 售货机管理接口

#### 设备管理接口
- **查询设备列表**: GET /manage/vendingMachine/list
- **查询设备详情**: GET /manage/vendingMachine/{id}
- **新增设备**: POST /manage/vendingMachine
- **修改设备**: PUT /manage/vendingMachine
- **删除设备**: DELETE /manage/vendingMachine/{ids}

#### 点位管理接口
- **查询点位列表**: GET /manage/node/list
- **查询点位详情**: GET /manage/node/{id}
- **新增点位**: POST /manage/node
- **修改点位**: PUT /manage/node
- **删除点位**: DELETE /manage/node/{id}
- **点位设备统计**: GET /manage/node/vmStats

#### 区域管理接口
- **查询区域列表**: GET /manage/region/list
- **查询区域详情**: GET /manage/region/{id}
- **新增区域**: POST /manage/region
- **修改区域**: PUT /manage/region
- **删除区域**: DELETE /manage/region/{ids}

#### 合作商管理接口
- **查询合作商列表**: GET /manage/partner/list
- **查询合作商详情**: GET /manage/partner/{id}
- **新增合作商**: POST /manage/partner
- **修改合作商**: PUT /manage/partner
- **删除合作商**: DELETE /manage/partner/{ids}

#### 设备型号管理接口
- **查询型号列表**: GET /manage/vmType/list
- **查询型号详情**: GET /manage/vmType/{id}
- **新增型号**: POST /manage/vmType
- **修改型号**: PUT /manage/vmType
- **删除型号**: DELETE /manage/vmType/{ids}

#### 商品管理接口
- **查询商品列表**: GET /manage/sku/list
- **查询商品详情**: GET /manage/sku/{skuId}
- **新增商品**: POST /manage/sku
- **修改商品**: PUT /manage/sku
- **删除商品**: DELETE /manage/sku/{skuIds}

#### 商品类别管理接口
- **查询类别列表**: GET /manage/skuClass/list
- **查询类别详情**: GET /manage/skuClass/{classId}
- **新增类别**: POST /manage/skuClass
- **修改类别**: PUT /manage/skuClass
- **删除类别**: DELETE /manage/skuClass/{classIds}

#### 员工管理接口
- **查询员工列表**: GET /manage/emp/list
- **查询员工详情**: GET /manage/emp/{id}
- **新增员工**: POST /manage/emp
- **修改员工**: PUT /manage/emp
- **删除员工**: DELETE /manage/emp/{ids}
- **根据设备获取运营人员**: GET /manage/emp/businessList/{innerCode}
- **根据设备获取维修人员**: GET /manage/emp/operationList/{innerCode}

### 工单管理接口

#### 工单管理接口
- **查询工单列表**: GET /manage/task/list
- **查询工单详情**: GET /manage/task/{taskId}
- **新增工单**: POST /manage/task
- **修改工单**: PUT /manage/task
- **删除工单**: DELETE /manage/task/{taskId}
- **完成工单**: PUT /manage/task/complete/{taskId}

#### 工单详情接口
- **根据工单ID查询详情**: GET /manage/taskDetails/byTaskId/{taskId}

#### 工单类型管理接口
- **查询工单类型列表**: GET /manage/taskType/list
- **取消工单**: PUT /manage/taskType/cancel

### 策略管理接口

#### 策略管理接口
- **查询策略列表**: GET /manage/policy/list
- **查询策略详情**: GET /manage/policy/{policyId}
- **新增策略**: POST /manage/policy
- **修改策略**: PUT /manage/policy
- **删除策略**: DELETE /manage/policy/{policyIds}

### 系统工具接口

#### 代码生成接口
- **查询生成表列表**: GET /tool/gen/list
- **查询数据库表列表**: GET /tool/gen/db/list
- **查询表详细信息**: GET /tool/gen/{tableId}
- **修改代码生成信息**: PUT /tool/gen
- **导入表**: POST /tool/gen/importTable
- **预览生成代码**: GET /tool/gen/preview/{tableId}
- **删除表数据**: DELETE /tool/gen/{tableId}
- **生成代码**: GET /tool/gen/genCode/{tableName}
- **同步数据库**: GET /tool/gen/synchDb/{tableName}

#### 系统监控接口
- **服务器信息**: GET /monitor/server
- **缓存监控**: GET /monitor/cache
- **在线用户**: GET /monitor/online
- **定时任务**: GET /monitor/job

### 文件上传接口
- **文件上传**: POST /common/upload
- **文件下载**: GET /common/download
- **资源文件**: GET /common/download/resource

### 验证码接口
- **获取验证码**: GET /captchaImage
- **短信验证码**: POST /sms/code

## 前端路由配置

### 主要路由
- **首页**: /index
- **登录**: /login
- **个人中心**: /user/profile
- **系统管理**: /system/*
- **设备管理**: /manage/vm/*
- **点位管理**: /manage/node/*
- **工单管理**: /manage/task/*
- **数据统计**: /manage/statistics/*
- **系统工具**: /tool/*
- **系统监控**: /monitor/*

### 权限路由
- 基于角色的动态路由加载
- 支持菜单权限控制
- 支持按钮权限控制
- 支持数据权限控制

## 数据库连接配置

### 开发环境
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************
    username: root
    password: root
```

### 生产环境
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
```

## 缓存配置

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password:
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
```

### 缓存使用
- **用户信息缓存**: 登录用户信息
- **权限缓存**: 用户权限信息
- **字典缓存**: 系统字典数据
- **参数缓存**: 系统参数配置
- **验证码缓存**: 图形验证码
- **短信验证码缓存**: 手机验证码

---

本知识库涵盖了智能售货机管理系统的所有核心功能、技术架构、操作流程和开发规范，为系统的使用、维护和扩展提供全面的技术支持。系统基于现代化的技术栈构建，具有高可用性、高扩展性和良好的用户体验，是一套完整的企业级售货机管理解决方案。



这是一个智能售货机管理系统
您好！您提到“我的工单有问题”，但没有提供具体的信息。为了更好地帮助您，请您补充以下信息：

1. **工单编号**：您是否有工单的编号？如果有，请提供。
2. **问题描述**：您遇到的具体问题是什么？例如：工单一直是待办状态、处理进度停滞、信息错误等。
3. **时间**：您是什么时候提交的工单？
4. **其他信息**：是否有截图、错误提示或其他相关信息？

提供这些信息后，我可以更准确地帮您分析或指导如何解决。请你详细描述一下



“待办工单”是本系统里面由管理员创建的，交给工作人员完成的工作订单，如果你想取消的话：

---


1. **登录系统**
   - 打开工单管理系统的网址或客户端。
   - 使用你的账号和密码登录。
   - 必须拥有管理员权限

2. **进入工单列表**
   - 在首页或导航栏找到“工单管理”入口，找到对应的订单，点击查看详情对话框，即可取消。


3. **权限不足**
   - 你不是管理员用户，请联系管理员取消



设备故障请及时上报管理员，由管理员创建维修工单，指派该负责改区域的维修员前往维修




1. 工单管理
运营工单管理：

运营人员负责处理补货等工单。系统通过生成和分配运营工单来确保商品供应。
点击查看详情对话框可以查看具体的补货信息和工单信息，以及进行开始工单，确认完成工单，取消工单操作

运维工单管理：

运维人员负责设备的安装、维修等操作。工单生成后，运维人员通过后台进行设备相关的处理工作。
点击查看详情对话框可以查看具体的补货信息和工单信息，以及进行开始工单，确认完成工单，取消工单操作


工单管理功能
工单创建：平台管理员可创建工单，指派运维或运营人员。

工单处理：运维/运营人员处理指派的工单。

工单管理数据库模型
表结构：

tb_task（工单表）：记录工单的基本信息（如任务ID、任务类型、任务状态等）。

tb_task_details（工单明细表）：详细记录工单处理的具体信息。

2. 点位管理
区域管理：将系统的运营区域划分为多个逻辑区域，并根据不同区域进行人员和资源分配。

点位管理：每个区域可包含多个点位，点位是智能售货机的具体放置位置，每一个点位对应一个合作商。

合作商管理：合作商是提供点位资源的合作伙伴。

点位管理功能
区域、点位、合作商信息管理。

点位与区域、合作商的关系一对多。

支持点位的增删改查操作。

数据库模型：
tb_region（区域表）：管理区域信息。

tb_partner（合作商表）：记录合作商的基本信息。

tb_node（点位表）：记录点位的详细信息，关联区域和合作商。

3. 设备管理
设备管理：管理售货机设备信息。

设备状态管理：追踪设备的当前状态（如正常、故障等）。

设备类型管理：管理设备的不同类型，确保售货机类型的多样性。

设备管理功能
增加、删除设备信息。

更新设备状态，确保设备正常运行。

根据设备类型进行设备分类管理。

4. 商品管理
商品管理：管理售货机中的商品信息。
商品管理中设计了批量导入商品的功能，点击批量导入，下载正确的模版，传入对应的Excel文件就可以完成批量导入商品
商品类型管理：商品按照类型进行分类，便于管理和展示。

商品管理功能
商品的增删改查操作。

商品分类管理，方便根据类型进行查询和展示。

5. 人员管理
人员管理：管理员工信息，包括运维人员和运营人员的管理。
人员管理中设计了批量导入人员的功能，点击批量导入，下载正确的模版，传入对应的Excel文件就可以完成批量导入人员信息
员工的归属区域就是区域管理里面的区域信息，请点击人与管理查看
员工角色管理：每个员工可分配不同的角色（如工单管理员、运营人员、运维人员等）。

人员管理功能
管理员工的基本信息。



6. 销售策略管理
销售策略管理：制定和调整销售策略，以实现更高效的商品销售和运营管理。

销售策略管理功能
设定商品促销活动和折扣策略。

调整价格和销售方案。

7. 智能客服
智能客服系统：接入阿里云百炼大模型提供自动化服务，回答用户问题并进行简单的处理。

智能客服功能
自动回复用户问题。

提供基本的售后服务。

8. 系统管理
用户管理：系统管理员负责对用户进行管理，包括添加、删除、修改用户信息，绑定角色信息，获取相应权限。

角色管理：绑定用户角色，获取相应的菜单权限。

菜单权限管理：管理系统菜单的访问权限。

数据字典管理：配置数据字典项，大部分是静态数据，供系统使用。

使用Spring Security实现RBAC模型，控制用户的访问权限。


# 工单完成功能说明

## 功能概述

在工单管理的工单详情对话框中添加了"确认完成"按钮，点击后会根据不同的工单类型自动更新设备状态。

## 功能特性

### 1. 前端界面改进

#### 1.1 按钮显示逻辑
- **待办状态 (taskStatus = 1)**: 显示"取消工单"按钮
- **进行中状态 (taskStatus = 2)**: 显示"确认完成"按钮
- **取消状态 (taskStatus = 3)**: 显示"重新创建"按钮
- **完成状态 (taskStatus = 4)**: 不显示操作按钮

#### 1.2 确认完成按钮
- 按钮类型：成功样式 (type="success")
- 点击后弹出确认对话框
- 确认后调用后端完成工单接口

### 2. 后端业务逻辑

#### 2.1 接口信息
- **请求路径**: `PUT /manage/task/complete/{taskId}`
- **权限要求**: `manage:task:edit`
- **参数**: taskId (工单ID)

#### 2.2 业务处理流程

1. **工单状态验证**
   - 检查工单是否存在
   - 验证工单状态必须为"进行中"(taskStatus = 2)

2. **设备信息查询**
   - 根据工单中的设备编号查询设备信息
   - 验证设备是否存在

3. **根据工单类型处理设备状态**

   ##### 投放工单 (productTypeId = 1)
   ```java
   // 将设备状态改为运营
   vendingMachine.setVmStatus(1L); // 1-运营
   ```

   ##### 维修工单 (productTypeId = 3)
   ```java
   // 将设备运行状态改为正常
   vendingMachine.setRunningStatus("{\"statusCode\":\"1001\",\"status\":true}");
   ```

   ##### 撤机工单 (productTypeId = 4)
   ```java
   // 删除设备
   vendingMachineService.deleteVendingMachineById(vendingMachine.getId());
   ```

   ##### 补货工单 (productTypeId = 2)
   ```java
   // 不需要更新设备状态，只完成工单
   ```

4. **更新工单状态**
   - 将工单状态设置为"完成"(taskStatus = 4)
   - 更新工单的修改时间

## 数据库字段说明

### 设备状态字段 (vm_status)
- `0`: 未投放
- `1`: 运营
- `3`: 撤机

### 设备运行状态字段 (running_status)
- 正常状态: `{"statusCode":"1001","status":true}`
- 异常状态: 其他值或空值

### 工单状态字段 (task_status)
- `1`: 待办
- `2`: 进行中
- `3`: 取消
- `4`: 完成

### 工单类型字段 (product_type_id)
- `1`: 投放工单
- `2`: 补货工单
- `3`: 维修工单
- `4`: 撤机工单

## 使用说明

### 1. 操作步骤
1. 进入工单管理页面
2. 点击工单列表中的"查看详情"
3. 在工单详情对话框中，如果工单状态为"进行中"，会显示"确认完成"按钮
4. 点击"确认完成"按钮
5. 在确认对话框中点击"确定"
6. 系统自动根据工单类型更新设备状态并完成工单

### 2. 注意事项
- 只有状态为"进行中"的工单才能执行完成操作
- 撤机工单完成后会删除设备，请谨慎操作
- 完成操作不可逆，请确认后再执行

## 错误处理

### 前端错误处理
- 网络请求失败时显示错误提示
- 后端返回错误时显示具体错误信息

### 后端错误处理
- 工单不存在：抛出"工单不存在"异常
- 工单状态不正确：抛出"只有进行中的工单才能完成"异常
- 设备不存在：抛出"设备不存在"异常
- 数据库操作失败：事务回滚

## 技术实现

### 前端技术栈
- Vue 3 Composition API
- Element Plus UI组件库
- Axios HTTP客户端

### 后端技术栈
- Spring Boot
- MyBatis
- Spring Security
- 事务管理 (@Transactional)

### 关键代码文件
- 前端组件: `business-detail-dialog.vue`, `operation-detail-dialog.vue`
- 前端API: `task.js`
- 后端控制器: `TaskController.java`
- 后端服务接口: `ITaskService.java`
- 后端服务实现: `TaskServiceImpl.java`

## 测试建议

1. **功能测试**
   - 测试不同工单类型的完成操作
   - 验证设备状态是否正确更新
   - 测试权限控制

2. **异常测试**
   - 测试非进行中状态工单的完成操作
   - 测试不存在工单的完成操作
   - 测试网络异常情况

3. **界面测试**
   - 验证按钮显示逻辑
   - 测试确认对话框
   - 验证成功/失败提示信息
