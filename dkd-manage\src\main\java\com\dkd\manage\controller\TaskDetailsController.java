package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.TaskDetails;
import com.dkd.manage.service.ITaskDetailsService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 工单详情Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
// 定义REST控制器，处理工单详情管理相关的HTTP请求
@RestController
@RequestMapping("/manage/taskDetails")
public class TaskDetailsController extends BaseController
{
    // 注入工单详情业务层接口
    @Autowired
    private ITaskDetailsService taskDetailsService;

    /**
     * 查询工单详情列表接口
     * 需要'manage:taskDeatils:list'权限
     * 请求路径：GET /manage/taskDetails/list
     */
    @PreAuthorize("@ss.hasPermi('manage:taskDeatils:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskDetails taskDetails)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询工单详情数据
        List<TaskDetails> list = taskDetailsService.selectTaskDetailsList(taskDetails);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出工单详情列表接口
     * 需要'manage:taskDeatils:export'权限
     * 请求路径：POST /manage/taskDetails/export
     */
    @PreAuthorize("@ss.hasPermi('manage:taskDeatils:export')")
    @Log(title = "工单详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskDetails taskDetails)
    {
        // 调用服务层方法查询需要导出的工单详情数据
        List<TaskDetails> list = taskDetailsService.selectTaskDetailsList(taskDetails);
        // 创建Excel工具类实例
        ExcelUtil<TaskDetails> util = new ExcelUtil<TaskDetails>(TaskDetails.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "工单详情数据");
    }

    /**
     * 获取工单详情详细信息接口
     * 需要'manage:taskDeatils:query'权限
     * 请求路径：GET /manage/taskDetails/{detailsId}
     */
    @PreAuthorize("@ss.hasPermi('manage:taskDeatils:query')")
    @GetMapping(value = "/{detailsId}")
    public AjaxResult getInfo(@PathVariable("detailsId") Long detailsId)
    {
        // 调用服务层方法获取工单详情详情
        return success(taskDetailsService.selectTaskDetailsByDetailsId(detailsId));
    }

    /**
     * 新增工单详情接口
     * 需要'manage:taskDeatils:add'权限
     * 请求路径：POST /manage/taskDetails
     */
    @PreAuthorize("@ss.hasPermi('manage:taskDeatils:add')")
    @Log(title = "工单详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskDetails taskDetails)
    {
        // 调用服务层方法新增工单详情
        return toAjax(taskDetailsService.insertTaskDetails(taskDetails));
    }

    /**
     * 修改工单详情接口
     * 需要'manage:taskDeatils:edit'权限
     * 请求路径：PUT /manage/taskDetails
     */
    @PreAuthorize("@ss.hasPermi('manage:taskDeatils:edit')")
    @Log(title = "工单详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskDetails taskDetails)
    {
        // 调用服务层方法修改工单详情信息
        return toAjax(taskDetailsService.updateTaskDetails(taskDetails));
    }

    /**
     * 删除工单详情接口
     * 需要'manage:taskDeatils:remove'权限
     * 请求路径：DELETE /manage/taskDetails/{detailsIds}
     */
    @PreAuthorize("@ss.hasPermi('manage:taskDeatils:remove')")
    @Log(title = "工单详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{detailsIds}")
    public AjaxResult remove(@PathVariable Long[] detailsIds)
    {
        // 调用服务层方法删除指定ID的工单详情
        return toAjax(taskDetailsService.deleteTaskDetailsByDetailsIds(detailsIds));
    }

    /**
     * 根据工单ID查询补货详情接口
     * 需要'manage:taskDetails:list'权限
     * 请求路径：GET /manage/taskDetails/byTaskId/{taskId}
     */
    @PreAuthorize("@ss.hasPermi('manage:taskDetails:list')")
    @GetMapping(value = "/byTaskId/{taskId}")
    public AjaxResult byTaskId(@PathVariable("taskId") Long taskId) {
        // 创建查询参数对象并设置工单ID
        TaskDetails taskDetailsParam = new TaskDetails();
        taskDetailsParam.setTaskId(taskId);
        // 调用服务层方法执行查询
        return success(taskDetailsService.selectTaskDetailsList(taskDetailsParam));
    }
}




// @RestController
// @RequestMapping("/manage/taskDetails")
// public class TaskDetailsController extends BaseController
// {
//     @Autowired
//     private ITaskDetailsService taskDetailsService;

//     /**
//      * 查询工单详情列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskDeatils:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(TaskDetails taskDetails)
//     {
//         startPage();
//         List<TaskDetails> list = taskDetailsService.selectTaskDetailsList(taskDetails);
//         return getDataTable(list);
//     }

//     /**
//      * 导出工单详情列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskDeatils:export')")
//     @Log(title = "工单详情", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, TaskDetails taskDetails)
//     {
//         List<TaskDetails> list = taskDetailsService.selectTaskDetailsList(taskDetails);
//         ExcelUtil<TaskDetails> util = new ExcelUtil<TaskDetails>(TaskDetails.class);
//         util.exportExcel(response, list, "工单详情数据");
//     }

//     /**
//      * 获取工单详情详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskDeatils:query')")
//     @GetMapping(value = "/{detailsId}")
//     public AjaxResult getInfo(@PathVariable("detailsId") Long detailsId)
//     {
//         return success(taskDetailsService.selectTaskDetailsByDetailsId(detailsId));
//     }

//     /**
//      * 新增工单详情
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskDeatils:add')")
//     @Log(title = "工单详情", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody TaskDetails taskDetails)
//     {
//         return toAjax(taskDetailsService.insertTaskDetails(taskDetails));
//     }

//     /**
//      * 修改工单详情
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskDeatils:edit')")
//     @Log(title = "工单详情", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody TaskDetails taskDetails)
//     {
//         return toAjax(taskDetailsService.updateTaskDetails(taskDetails));
//     }

//     /**
//      * 删除工单详情
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskDeatils:remove')")
//     @Log(title = "工单详情", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{detailsIds}")
//     public AjaxResult remove(@PathVariable Long[] detailsIds)
//     {
//         return toAjax(taskDetailsService.deleteTaskDetailsByDetailsIds(detailsIds));
//     }

//     /**
//      * 查看工单补货详情
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskDetails:list')")
//     @GetMapping(value = "/byTaskId/{taskId}")
//     public AjaxResult byTaskId(@PathVariable("taskId") Long taskId) {
//         TaskDetails taskDetailsParam = new TaskDetails();
//         taskDetailsParam.setTaskId(taskId);
//         return success(taskDetailsService.selectTaskDetailsList(taskDetailsParam));
//     }


// }
