package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.TaskType;
import com.dkd.manage.service.ITaskTypeService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 工单类型Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
// 定义REST控制器，处理工单类型管理相关的HTTP请求
@RestController
@RequestMapping("/manage/taskType")
public class TaskTypeController extends BaseController
{
    // 注入工单类型业务层接口
    @Autowired
    private ITaskTypeService taskTypeService;

    /**
     * 查询工单类型列表接口
     * 需要'manage:taskType:list'权限
     * 请求路径：GET /manage/taskType/list
     */
    @PreAuthorize("@ss.hasPermi('manage:taskType:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskType taskType)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询工单类型数据
        List<TaskType> list = taskTypeService.selectTaskTypeList(taskType);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出工单类型列表接口
     * 需要'manage:taskType:export'权限
     * 请求路径：POST /manage/taskType/export
     */
    @PreAuthorize("@ss.hasPermi('manage:taskType:export')")
    @Log(title = "工单类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskType taskType)
    {
        // 调用服务层方法查询需要导出的工单类型数据
        List<TaskType> list = taskTypeService.selectTaskTypeList(taskType);
        // 创建Excel工具类实例
        ExcelUtil<TaskType> util = new ExcelUtil<TaskType>(TaskType.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "工单类型数据");
    }

    /**
     * 获取工单类型详细信息接口
     * 需要'manage:taskType:query'权限
     * 请求路径：GET /manage/taskType/{typeId}
     */
    @PreAuthorize("@ss.hasPermi('manage:taskType:query')")
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
    {
        // 调用服务层方法获取工单类型详情
        return success(taskTypeService.selectTaskTypeByTypeId(typeId));
    }

    /**
     * 新增工单类型接口
     * 需要'manage:taskType:add'权限
     * 请求路径：POST /manage/taskType
     */
    @PreAuthorize("@ss.hasPermi('manage:taskType:add')")
    @Log(title = "工单类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskType taskType)
    {
        // 调用服务层方法新增工单类型
        return toAjax(taskTypeService.insertTaskType(taskType));
    }

    /**
     * 修改工单类型接口
     * 需要'manage:taskType:edit'权限
     * 请求路径：PUT /manage/taskType
     */
    @PreAuthorize("@ss.hasPermi('manage:taskType:edit')")
    @Log(title = "工单类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskType taskType)
    {
        // 调用服务层方法修改工单类型信息
        return toAjax(taskTypeService.updateTaskType(taskType));
    }

    /**
     * 删除工单类型接口
     * 需要'manage:taskType:remove'权限
     * 请求路径：DELETE /manage/taskType/{typeIds}
     */
    @PreAuthorize("@ss.hasPermi('manage:taskType:remove')")
    @Log(title = "工单类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{typeIds}")
    public AjaxResult remove(@PathVariable Long[] typeIds)
    {
        // 调用服务层方法删除指定ID的工单类型
        return toAjax(taskTypeService.deleteTaskTypeByTypeIds(typeIds));
    }
}




// @RestController
// @RequestMapping("/manage/taskType")
// public class TaskTypeController extends BaseController
// {
//     @Autowired
//     private ITaskTypeService taskTypeService;

//     /**
//      * 查询工单类型列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskType:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(TaskType taskType)
//     {
//         startPage();
//         List<TaskType> list = taskTypeService.selectTaskTypeList(taskType);
//         return getDataTable(list);
//     }

//     /**
//      * 导出工单类型列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskType:export')")
//     @Log(title = "工单类型", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, TaskType taskType)
//     {
//         List<TaskType> list = taskTypeService.selectTaskTypeList(taskType);
//         ExcelUtil<TaskType> util = new ExcelUtil<TaskType>(TaskType.class);
//         util.exportExcel(response, list, "工单类型数据");
//     }

//     /**
//      * 获取工单类型详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskType:query')")
//     @GetMapping(value = "/{typeId}")
//     public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
//     {
//         return success(taskTypeService.selectTaskTypeByTypeId(typeId));
//     }

//     /**
//      * 新增工单类型
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskType:add')")
//     @Log(title = "工单类型", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody TaskType taskType)
//     {
//         return toAjax(taskTypeService.insertTaskType(taskType));
//     }

//     /**
//      * 修改工单类型
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskType:edit')")
//     @Log(title = "工单类型", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody TaskType taskType)
//     {
//         return toAjax(taskTypeService.updateTaskType(taskType));
//     }

//     /**
//      * 删除工单类型
//      */
//     @PreAuthorize("@ss.hasPermi('manage:taskType:remove')")
//     @Log(title = "工单类型", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{typeIds}")
//     public AjaxResult remove(@PathVariable Long[] typeIds)
//     {
//         return toAjax(taskTypeService.deleteTaskTypeByTypeIds(typeIds));
//     }
// }
