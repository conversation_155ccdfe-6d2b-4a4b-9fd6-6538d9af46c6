<template>
  <div class="app-container">
    <!-- 点位设备统计图表 -->
    <el-card class="mb-4" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>点位设备统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 400px;"></div>
    </el-card>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="点位名称" prop="nodeName">
        <el-input
          v-model="queryParams.nodeName"
          placeholder="请输入点位名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域搜索" prop="regionId">
        <!-- <el-input
          v-model="queryParams.regionId"
          placeholder="请输入区域ID"
          clearable
          @keyup.enter="handleQuery"
        /> -->
        <el-select v-model="queryParams.regionId" placeholder="请选择区域" clearable>
          <el-option v-for="region in regionList" :key="region.id" :label="region.regionName" :value="region.id" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="合作商ID" prop="partnerId">
        <el-input
          v-model="queryParams.partnerId"
          placeholder="请输入合作商ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:node:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:node:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:node:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:node:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="nodeList" @selection-change="handleSelectionChange">
  <el-table-column type="selection" width="55" align="center" />
  <el-table-column label="序号" type="index" width="50" align="center" prop="id" />
  <el-table-column label="点位名称" align="center" prop="nodeName" />
  <!-- 使用mybatis嵌套查询 -->
  <el-table-column label="所在区域" align="center" prop="region.regionName" />
  <el-table-column label="商圈类型" align="center" prop="businessType">
    <template #default="scope">
      <dict-tag :options="business_type" :value="scope.row.businessType" />
    </template>
  </el-table-column>
  <!-- 使用mybatis嵌套查询 -->
  <el-table-column label="合作商" align="center" prop="partner.partnerName" />
  <el-table-column label="详细地址" align="center" prop="address" show-overflow-tooltip="true"/>
  <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
    <template #default="scope">
      <el-button link type="primary"  @click="getNodeInfo(scope.row)" v-hasPermi="['manage:vm:list']">查看详情</el-button>
      <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['manage:node:edit']">修改</el-button>
      <el-button link type="primary"  @click="handleDelete(scope.row)" v-hasPermi="['manage:node:remove']">删除</el-button>
    </template>
  </el-table-column>
</el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改点位管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="nodeRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="点位名称" prop="nodeName">
          <el-input v-model="form.nodeName" placeholder="请输入点位名称" />
        </el-form-item>
        <el-form-item label="商圈类型" prop="businessType">
          <el-select v-model="form.businessType" placeholder="请选择商圈类型">
            <el-option
              v-for="dict in business_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域" prop="regionId">
          <el-select v-model="form.regionId" placeholder="请选择区域" clearable>
            <el-option v-for="region in regionList" :key="region.id" :label="region.regionName" :value="region.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="合作商" prop="partnerId">
          <el-select v-model="form.partnerId" placeholder="请选择合作商" clearable>
            <el-option v-for="partner in partnerList" :key="partner.id" :label="partner.partnerName" :value="partner.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog :title="dialogTitle" v-model="open1" width="600px" append-to-body>
      <el-table
        v-loading="loading"
        :data="vmList"
        empty-text="该点位下暂无设备"
        style="width: 100%"
      >
        <el-table-column label="序号" type="index" width="55" align="center" />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="设备编号" align="center" prop="innerCode" />
        <el-table-column label="设备状态" align="center" prop="vmStatus">
          <template #default="scope">
            <dict-tag :options="vm_status" :value="scope.row.vmStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="最后一次供货时间" align="center" prop="lastSupplyTime">
          <template #default="scope">
            {{ parseTime(scope.row.lastSupplyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 对话框底部按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open1 = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Node">
import { listNode, getNode, delNode, addNode, updateNode, getNodeVmStats } from "@/api/manage/node";
import {listRegion} from "@/api/manage/region";
import {listPartner} from "@/api/manage/partner";
import { listVm } from "@/api/manage/vm";
import {loadAllParams} from "@/api/page";
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { business_type } = proxy.useDict('business_type');
const { vm_status } = proxy.useDict('vm_status');

const nodeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    nodeName: null,
    regionId: null,
    partnerId: null,
  },
  rules: {
    nodeName: [
      { required: true, message: "点位名称不能为空", trigger: "blur" }
    ],
    address: [
      { required: true, message: "详细地址不能为空", trigger: "blur" }
    ],
    businessType: [
      { required: true, message: "商圈类型不能为空", trigger: "change" }
    ],
    regionId: [
      { required: true, message: "区域ID不能为空", trigger: "blur" }
    ],
    partnerId: [
      { required: true, message: "合作商ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询点位管理列表 */
function getList() {
  loading.value = true;
  listNode(queryParams.value).then(response => {
    nodeList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 刷新图表数据
    if (chartInstance.value) {
      loadChartData();
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    nodeName: null,
    address: null,
    businessType: null,
    regionId: null,
    partnerId: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    remark: null
  };
  proxy.resetForm("nodeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加点位管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getNode(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改点位管理";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["nodeRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateNode(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addNode(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除点位管理编号为"' + _ids + '"的数据项？').then(function() {
    return delNode(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('manage/node/export', {
    ...queryParams.value
  }, `node_${new Date().getTime()}.xlsx`)
}

/**查询区域列表 */
const regionList = ref([]);
function getRegionList() {
  listRegion(loadAllParams).then(response => {
    regionList.value = response.rows;
  });
}
getRegionList()

/**查询合作商列表 */
const partnerList = ref([]);
function getPartnerList() {
  listPartner(loadAllParams).then(response => {
    partnerList.value = response.rows;
  });
}
getPartnerList()

/** 查看设备详情 */
const open1 = ref(false);
const vmList = ref([]);
const dialogTitle = ref("点位详情");
function getNodeInfo(row) {
  // 设置对话框标题，显示具体的点位名称
  dialogTitle.value = `${row.nodeName} - 设备详情`;

  // 创建查询参数，根据点位ID查询该点位下的设备
  const vmQueryParams = {
    pageNum: 1,
    pageSize: 10000,
    nodeId: row.id  // 使用nodeId参数来查询特定点位下的设备
  };

  loading.value = true;
  listVm(vmQueryParams).then(response => {
    vmList.value = response.rows;
    open1.value = true;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
    proxy.$modal.msgError("获取设备信息失败");
  });
}

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    const response = await getNodeVmStats();
    const data = response.data || [];

    // 处理数据
    const nodeNames = [];
    const vmCounts = [];

    // 如果后端API尚未实现，使用nodeList的数据模拟
    if (data.length === 0 && nodeList.value.length > 0) {
      nodeList.value.forEach(node => {
        nodeNames.push(node.nodeName);
        vmCounts.push(node.vmCount || Math.floor(Math.random() * 10) + 1); // 使用vmCount或随机数
      });
    } else {
      // 使用API返回的数据
      data.forEach(item => {
        nodeNames.push(item.nodeName);
        vmCounts.push(item.vmCount);
      });
    }

    updateChart(nodeNames, vmCounts);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(nodeNames, vmCounts) {
  if (!chartInstance.value) return;

  const option = {
    title: {
      text: '各点位设备数量统计',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c} 台设备'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: nodeNames,
      axisLabel: {
        interval: 0,
        rotate: nodeNames.length > 10 ? 30 : 0, // 当点位较多时旋转标签
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '设备数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40] // 调整y轴名称位置
      },
      axisLabel: {
        fontSize: 12,
        formatter: '{value}' // 确保显示整数
      },
      minInterval: 1, // 设置最小间隔为1，确保只显示整数刻度
      splitNumber: 5 // 建议分割段数，让刻度更合理
    },
    series: [
      {
        name: '设备数量',
        type: 'bar',
        barWidth: '60%',
        data: vmCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#67C23A' },
            { offset: 1, color: '#409EFF' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#529b2e' },
              { offset: 0.7, color: '#529b2e' },
              { offset: 1, color: '#67C23A' }
            ])
          }
        }
      }
    ]
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询点位管理列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
