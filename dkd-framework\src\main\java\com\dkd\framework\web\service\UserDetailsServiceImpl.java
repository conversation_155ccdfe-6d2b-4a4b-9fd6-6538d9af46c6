// 包声明：定义当前类所属的包路径
package com.dkd.framework.web.service;

// 导入SLF4J日志框架的Logger接口
import org.slf4j.Logger;
// 导入SLF4J日志框架的LoggerFactory工厂类
import org.slf4j.LoggerFactory;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring Security用户详情接口
import org.springframework.security.core.userdetails.UserDetails;
// 导入Spring Security用户详情服务接口
import org.springframework.security.core.userdetails.UserDetailsService;
// 导入Spring Security用户名未找到异常
import org.springframework.security.core.userdetails.UsernameNotFoundException;
// 导入Spring服务注解
import org.springframework.stereotype.Service;
// 导入系统用户实体类
import com.dkd.common.core.domain.entity.SysUser;
// 导入登录用户模型类
import com.dkd.common.core.domain.model.LoginUser;
// 导入用户状态枚举
import com.dkd.common.enums.UserStatus;
// 导入服务异常类
import com.dkd.common.exception.ServiceException;
// 导入消息工具类，用于国际化消息处理
import com.dkd.common.utils.MessageUtils;
// 导入字符串工具类
import com.dkd.common.utils.StringUtils;
// 导入系统用户服务接口
import com.dkd.system.service.ISysUserService;

/**
 * 用户验证处理
 * 这个服务类实现了Spring Security的UserDetailsService接口，
 * 负责在用户认证过程中加载用户详细信息，包括：
 * 1. 根据用户名查询用户信息
 * 2. 验证用户状态（是否存在、是否被删除、是否被停用）
 * 3. 验证用户密码
 * 4. 加载用户权限信息
 * 5. 创建登录用户对象
 *
 * <AUTHOR>
 */
@Service // 将此类标记为Spring服务组件，由Spring容器管理
public class UserDetailsServiceImpl implements UserDetailsService
{
    // 创建日志记录器，用于记录用户验证过程的日志
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    // 注入用户服务，用于查询用户信息
    @Autowired
    private ISysUserService userService;

    // 注入密码服务，用于验证用户密码
    @Autowired
    private SysPasswordService passwordService;

    // 注入权限服务，用于获取用户权限信息
    @Autowired
    private SysPermissionService permissionService;

    /**
     * 根据用户名加载用户详细信息
     * 这是Spring Security认证过程中的核心方法，当用户尝试登录时会被调用
     *
     * @param username 用户名，用户输入的登录账号
     * @return UserDetails 用户详细信息，包含用户基本信息和权限
     * @throws UsernameNotFoundException 当用户名不存在时抛出此异常
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException
    {
        // 第一步：根据用户名查询用户信息
        SysUser user = userService.selectUserByUserName(username);

        // 第二步：验证用户是否存在
        if (StringUtils.isNull(user))
        {
            // 记录用户不存在的日志
            log.info("登录用户：{} 不存在.", username);
            // 抛出服务异常，提示用户不存在
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        }
        // 第三步：验证用户是否已被删除
        else if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            // 记录用户已被删除的日志
            log.info("登录用户：{} 已被删除.", username);
            // 抛出服务异常，提示用户已被删除
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        }
        // 第四步：验证用户是否已被停用
        else if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            // 记录用户已被停用的日志
            log.info("登录用户：{} 已被停用.", username);
            // 抛出服务异常，提示用户已被停用
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }

        // 第五步：验证用户密码（包括密码错误次数检查和账户锁定机制）
        passwordService.validate(user);

        // 第六步：创建并返回登录用户对象
        return createLoginUser(user);
    }

    /**
     * 创建登录用户对象
     * 根据系统用户信息创建包含权限的登录用户对象
     *
     * @param user 系统用户对象，包含用户基本信息
     * @return UserDetails 登录用户对象，包含用户信息和权限
     */
    public UserDetails createLoginUser(SysUser user)
    {
        // 创建登录用户对象，传入用户ID、部门ID、用户信息和菜单权限
        // permissionService.getMenuPermission(user) 获取用户的所有菜单权限
        return new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
    }
} // 类结束标记
