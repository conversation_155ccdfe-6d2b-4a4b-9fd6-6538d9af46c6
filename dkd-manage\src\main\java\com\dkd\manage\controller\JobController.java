package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Job;
import com.dkd.manage.service.IJobService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 自动补货任务Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
// 定义REST控制器，处理自动补货任务管理相关的HTTP请求
@RestController
@RequestMapping("/manage/job")
public class JobController extends BaseController
{
    // 注入自动补货任务业务层接口
    @Autowired
    private IJobService jobService;

    /**
     * 查询自动补货任务列表接口
     * 需要'manage:job:list'权限
     * 请求路径：GET /manage/job/list
     */
    @PreAuthorize("@ss.hasPermi('manage:job:list')")
    @GetMapping("/list")
    public TableDataInfo list(Job job)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询任务数据
        List<Job> list = jobService.selectJobList(job);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出自动补货任务列表接口
     * 需要'manage:job:export'权限
     * 请求路径：POST /manage/job/export
     */
    @PreAuthorize("@ss.hasPermi('manage:job:export')")
    @Log(title = "自动补货任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Job job)
    {
        // 调用服务层方法查询需要导出的任务数据
        List<Job> list = jobService.selectJobList(job);
        // 创建Excel工具类实例
        ExcelUtil<Job> util = new ExcelUtil<Job>(Job.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "自动补货任务数据");
    }

    /**
     * 获取自动补货任务详细信息接口
     * 需要'manage:job:query'权限
     * 请求路径：GET /manage/job/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:job:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取任务详情
        return success(jobService.selectJobById(id));
    }

    /**
     * 新增自动补货任务接口
     * 需要'manage:job:add'权限
     * 请求路径：POST /manage/job
     */
    @PreAuthorize("@ss.hasPermi('manage:job:add')")
    @Log(title = "自动补货任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Job job)
    {
        // 调用服务层方法新增任务
        return toAjax(jobService.insertJob(job));
    }

    /**
     * 修改自动补货任务接口
     * 需要'manage:job:edit'权限
     * 请求路径：PUT /manage/job
     */
    @PreAuthorize("@ss.hasPermi('manage:job:edit')")
    @Log(title = "自动补货任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Job job)
    {
        // 调用服务层方法修改任务信息
        return toAjax(jobService.updateJob(job));
    }

    /**
     * 删除自动补货任务接口
     * 需要'manage:job:remove'权限
     * 请求路径：DELETE /manage/job/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:job:remove')")
    @Log(title = "自动补货任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的任务
        return toAjax(jobService.deleteJobByIds(ids));
    }
}



 
// @RestController
// @RequestMapping("/manage/job")
// public class JobController extends BaseController
// {
//     @Autowired
//     private IJobService jobService;

//     /**
//      * 查询自动补货任务列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:job:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Job job)
//     {
//         startPage();
//         List<Job> list = jobService.selectJobList(job);
//         return getDataTable(list);
//     }

//     /**
//      * 导出自动补货任务列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:job:export')")
//     @Log(title = "自动补货任务", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Job job)
//     {
//         List<Job> list = jobService.selectJobList(job);
//         ExcelUtil<Job> util = new ExcelUtil<Job>(Job.class);
//         util.exportExcel(response, list, "自动补货任务数据");
//     }

//     /**
//      * 获取自动补货任务详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:job:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(jobService.selectJobById(id));
//     }

//     /**
//      * 新增自动补货任务
//      */
//     @PreAuthorize("@ss.hasPermi('manage:job:add')")
//     @Log(title = "自动补货任务", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Job job)
//     {
//         return toAjax(jobService.insertJob(job));
//     }

//     /**
//      * 修改自动补货任务
//      */
//     @PreAuthorize("@ss.hasPermi('manage:job:edit')")
//     @Log(title = "自动补货任务", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Job job)
//     {
//         return toAjax(jobService.updateJob(job));
//     }

//     /**
//      * 删除自动补货任务
//      */
//     @PreAuthorize("@ss.hasPermi('manage:job:remove')")
//     @Log(title = "自动补货任务", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(jobService.deleteJobByIds(ids));
//     }
// }
