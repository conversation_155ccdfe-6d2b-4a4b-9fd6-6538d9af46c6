// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.Documented; // 用于标记注解是否包含在JavaDoc中
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Inherited; // 标记注解可以被子类继承
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型
import com.dkd.common.enums.DataSourceType; // 导入数据源类型枚举

/**
 * 自定义多数据源切换注解
 * 用于在运行时动态切换数据源，支持主从数据库、读写分离等场景
 * 通过AOP切面拦截带有此注解的方法，自动切换到指定的数据源
 *
 * 优先级：先方法，后类，如果方法覆盖了类上的数据源类型，以方法的为准，否则以类上的为准
 * 使用场景：读写分离、多租户数据库、分库分表等
 *
 * <AUTHOR>
 */
@Target({ ElementType.METHOD, ElementType.TYPE }) // 指定注解可以应用于方法和类型（类、接口、枚举）
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Documented // 指定注解会被包含在JavaDoc中
@Inherited // 指定注解可以被子类继承
public @interface DataSource // 定义数据源切换注解接口
{
    /**
     * 切换数据源名称
     * 指定要使用的数据源类型，默认使用主数据源
     *
     * @return 数据源类型枚举值
     */
    public DataSourceType value() default DataSourceType.MASTER; // 定义数据源类型属性，默认值为主数据源
}
