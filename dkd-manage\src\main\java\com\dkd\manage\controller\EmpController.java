package com.dkd.manage.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import com.dkd.common.constant.DkdContants;
import com.dkd.manage.domain.VendingMachine;
import com.dkd.manage.service.IVendingMachineService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Emp;
import com.dkd.manage.service.IEmpService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 人员列表Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
// 定义REST控制器，处理人员管理相关的HTTP请求
@RestController
@RequestMapping("/manage/emp")
public class EmpController extends BaseController
{
    // 注入人员业务层接口
    @Autowired
    private IEmpService empService;

    // 注入售货机业务层接口，用于根据设备查询相关人员
    @Autowired
    private IVendingMachineService vendingMachineService;

    /**
     * 查询人员列表接口
     * 需要'manage:emp:list'权限
     * 请求路径：GET /manage/emp/list
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:list')")
    @GetMapping("/list")
    public TableDataInfo list(Emp emp)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询人员数据
        List<Emp> list = empService.selectEmpList(emp);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出人员列表接口
     * 需要'manage:emp:export'权限
     * 请求路径：POST /manage/emp/export
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:export')")
    @Log(title = "人员列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Emp emp)
    {
        // 调用服务层方法查询需要导出的人员数据
        List<Emp> list = empService.selectEmpList(emp);
        // 创建Excel工具类实例
        ExcelUtil<Emp> util = new ExcelUtil<Emp>(Emp.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "人员列表数据");
    }

    /**
     * 获取人员详细信息接口
     * 需要'manage:emp:query'权限
     * 请求路径：GET /manage/emp/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取人员详情
        return success(empService.selectEmpById(id));
    }

    /**
     * 新增人员接口
     * 需要'manage:emp:add'权限
     * 请求路径：POST /manage/emp
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:add')")
    @Log(title = "人员列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Emp emp)
    {
        // 调用服务层方法新增人员
        return toAjax(empService.insertEmp(emp));
    }

    /**
     * 修改人员接口
     * 需要'manage:emp:edit'权限
     * 请求路径：PUT /manage/emp
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:edit')")
    @Log(title = "人员列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Emp emp)
    {
        // 调用服务层方法修改人员信息
        return toAjax(empService.updateEmp(emp));
    }

    /**
     * 删除人员接口
     * 需要'manage:emp:remove'权限
     * 请求路径：DELETE /manage/emp/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:remove')")
    @Log(title = "人员列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的人员
        return toAjax(empService.deleteEmpByIds(ids));
    }

    /**
     * 根据售货机编号查询运营人员列表接口
     * 需要'manage:emp:list'权限
     * 请求路径：GET /manage/emp/businessList/{innerCode}
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:list')")
    @GetMapping("/businessList/{innerCode}")
    public AjaxResult businessList(@PathVariable("innerCode") String innerCode) {
        // 1. 查询售货机信息
        VendingMachine vm = vendingMachineService.selectVendingMachineByInnerCode(innerCode);
        if (vm == null) {
            // 售货机不存在时返回错误结果
            return error();
        }

        // 2. 构建查询参数
        Emp empParam = new Emp();
        empParam.setRegionId(vm.getRegionId()); // 设置为设备所属区域
        empParam.setStatus(DkdContants.EMP_STATUS_NORMAL); // 查询启用状态的员工
        empParam.setRoleCode(DkdContants.ROLE_CODE_BUSINESS); // 查询角色编码为运营员的人员

        // 3. 执行查询并返回结果
        return success(empService.selectEmpList(empParam));
    }

    /**
     * 根据售货机编号查询运维人员列表接口
     * 需要'manage:emp:list'权限
     * 请求路径：GET /manage/emp/operationList/{innerCode}
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:list')")
    @GetMapping("/operationList/{innerCode}")
    public AjaxResult getOperationList(@PathVariable("innerCode") String innerCode) {
        // 1. 查询售货机信息
        VendingMachine vm = vendingMachineService.selectVendingMachineByInnerCode(innerCode);
        if (vm == null) {
            // 售货机不存在时返回错误提示
            return error("售货机不存在");
        }

        // 2. 构建查询参数
        Emp empParam = new Emp();
        empParam.setRegionId(vm.getRegionId()); // 设置为设备所属区域
        empParam.setStatus(DkdContants.EMP_STATUS_NORMAL); // 查询启用状态的员工
        empParam.setRoleCode(DkdContants.ROLE_CODE_OPERATOR); // 查询角色编码为维修员的人员

        // 3. 执行查询并返回结果
        return success(empService.selectEmpList(empParam));
    }

    /**
     * 获取人员角色分布统计数据
     * 用于生成人员角色分布统计图表
     * 需要'manage:emp:list'权限
     * 请求路径：GET /manage/emp/roleStats
     */
    @PreAuthorize("@ss.hasPermi('manage:emp:list')")
    @GetMapping("/roleStats")
    public AjaxResult getEmpRoleStats()
    {
        // 调用服务层方法获取人员角色分布统计数据
        Map<String, Object> stats = empService.getEmpRoleStats();
        // 返回成功结果，包含统计数据
        return AjaxResult.success(stats);
    }






// @RestController
// @RequestMapping("/manage/emp")
// public class EmpController extends BaseController
// {
//     @Autowired
//     private IEmpService empService;

//     @Autowired
//     private IVendingMachineService vendingMachineService;

//     /**
//      * 查询人员列表列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Emp emp)
//     {
//         startPage();
//         List<Emp> list = empService.selectEmpList(emp);
//         return getDataTable(list);
//     }

//     /**
//      * 导出人员列表列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:export')")
//     @Log(title = "人员列表", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Emp emp)
//     {
//         List<Emp> list = empService.selectEmpList(emp);
//         ExcelUtil<Emp> util = new ExcelUtil<Emp>(Emp.class);
//         util.exportExcel(response, list, "人员列表数据");
//     }

//     /**
//      * 获取人员列表详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(empService.selectEmpById(id));
//     }

//     /**
//      * 新增人员列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:add')")
//     @Log(title = "人员列表", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Emp emp)
//     {
//         return toAjax(empService.insertEmp(emp));
//     }

//     /**
//      * 修改人员列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:edit')")
//     @Log(title = "人员列表", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Emp emp)
//     {
//         return toAjax(empService.updateEmp(emp));
//     }

//     /**
//      * 删除人员列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:remove')")
//     @Log(title = "人员列表", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(empService.deleteEmpByIds(ids));
//     }

//     /**
//      * 根据售货机获取运营人员列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:list')")
//     @GetMapping("/businessList/{innerCode}")
//     public AjaxResult businessList(@PathVariable("innerCode") String innerCode) {
//         // 1.查询售货机信息
//         VendingMachine vm = vendingMachineService.selectVendingMachineByInnerCode(innerCode);
//         if (vm == null) {
//             return error();
//         }
//         // 2.根据区域id、角色编号、员工状态查询运营人员列表
//         Emp empParam = new Emp();
//         empParam.setRegionId(vm.getRegionId());// 设备所属区域
//         empParam.setStatus(DkdContants.EMP_STATUS_NORMAL);// 员工启用
//         empParam.setRoleCode(DkdContants.ROLE_CODE_BUSINESS);// 角色编码：运营员
//         return success(empService.selectEmpList(empParam));
//     }

//     /**
//      * 根据售货机获取运维人员列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:emp:list')")
//     @GetMapping("/operationList/{innerCode}")
//     public AjaxResult getOperationList(@PathVariable("innerCode") String innerCode) {
//         // 1.查询售货机信息
//         VendingMachine vm = vendingMachineService.selectVendingMachineByInnerCode(innerCode);
//         if (vm == null) {
//             return error("售货机不存在");
//         }
//         // 2.根据区域id、角色编号、状态查询运维人员列表
//         Emp empParam = new Emp();
//         empParam.setRegionId(vm.getRegionId());// 设备所属区域
//         empParam.setStatus(DkdContants.EMP_STATUS_NORMAL);// 员工启用
//         empParam.setRoleCode(DkdContants.ROLE_CODE_OPERATOR);// 角色编码：维修员
//         return success(empService.selectEmpList(empParam));
//     }


    /**
     * 导入员工列表接口
     * 需要'manage:emp:import'权限
     * 请求路径：POST /manage/emp/importData
     */
    @Log(title = "员工管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('manage:emp:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        // 读取Excel文件中的员工数据
        ExcelUtil<Emp> util = new ExcelUtil<Emp>(Emp.class);
        List<Emp> empList = util.importExcel(file.getInputStream());
        // 获取当前操作用户名
        String operName = getUsername();
        // 调用服务层方法导入员工数据
        String message = empService.importEmp(empList, updateSupport, operName);
        // 返回导入结果信息
        return success(message);
    }

    /**
     * 下载员工导入模板接口
     * 请求路径：POST /manage/emp/importTemplate
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        // 创建Excel工具类实例
        ExcelUtil<Emp> util = new ExcelUtil<Emp>(Emp.class);
        // 导出空模板供用户下载
        util.importTemplateExcel(response, "员工数据");
    }

}
