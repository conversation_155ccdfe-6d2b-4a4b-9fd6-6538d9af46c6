package com.dkd.manage.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.VmType;
import com.dkd.manage.service.IVmTypeService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 设备类型管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
// 定义REST控制器，处理设备类型管理相关的HTTP请求
@RestController
@RequestMapping("/manage/vmType")
public class VmTypeController extends BaseController
{
    // 注入设备类型业务层接口
    @Autowired
    private IVmTypeService vmTypeService;

    /**
     * 查询设备类型列表接口
     * 需要'manage:vmType:list'权限
     * 请求路径：GET /manage/vmType/list
     */
    @PreAuthorize("@ss.hasPermi('manage:vmType:list')")
    @GetMapping("/list")
    public TableDataInfo list(VmType vmType)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询设备类型数据
        List<VmType> list = vmTypeService.selectVmTypeList(vmType);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出设备类型列表接口
     * 需要'manage:vmType:export'权限
     * 请求路径：POST /manage/vmType/export
     */
    @PreAuthorize("@ss.hasPermi('manage:vmType:export')")
    @Log(title = "设备类型管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VmType vmType)
    {
        // 调用服务层方法查询需要导出的设备类型数据
        List<VmType> list = vmTypeService.selectVmTypeList(vmType);
        // 创建Excel工具类实例
        ExcelUtil<VmType> util = new ExcelUtil<VmType>(VmType.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "设备类型管理数据");
    }

    /**
     * 获取设备类型详细信息接口
     * 需要'manage:vmType:query'权限
     * 请求路径：GET /manage/vmType/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:vmType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取设备类型详情
        return success(vmTypeService.selectVmTypeById(id));
    }

    /**
     * 新增设备类型接口
     * 需要'manage:vmType:add'权限
     * 请求路径：POST /manage/vmType
     */
    @PreAuthorize("@ss.hasPermi('manage:vmType:add')")
    @Log(title = "设备类型管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VmType vmType)
    {
        // 调用服务层方法新增设备类型
        return toAjax(vmTypeService.insertVmType(vmType));
    }

    /**
     * 修改设备类型接口
     * 需要'manage:vmType:edit'权限
     * 请求路径：PUT /manage/vmType
     */
    @PreAuthorize("@ss.hasPermi('manage:vmType:edit')")
    @Log(title = "设备类型管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VmType vmType)
    {
        // 调用服务层方法修改设备类型信息
        return toAjax(vmTypeService.updateVmType(vmType));
    }

    /**
     * 删除设备类型接口
     * 需要'manage:vmType:remove'权限
     * 请求路径：DELETE /manage/vmType/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:vmType:remove')")
    @Log(title = "设备类型管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的设备类型
        return toAjax(vmTypeService.deleteVmTypeByIds(ids));
    }

    /**
     * 获取设备类型统计数据
     * 用于生成设备类型统计图表
     * 需要'manage:vmType:list'权限
     * 请求路径：GET /manage/vmType/stats
     */
    @PreAuthorize("@ss.hasPermi('manage:vmType:list')")
    @GetMapping("/stats")
    public AjaxResult getVmTypeStats()
    {
        // 调用服务层方法获取设备类型统计数据
        Map<String, Object> stats = vmTypeService.getVmTypeStats();
        // 返回成功结果，包含统计数据
        return AjaxResult.success(stats);
    }
}



// @RestController
// @RequestMapping("/manage/vmType")
// public class VmTypeController extends BaseController
// {
//     @Autowired
//     private IVmTypeService vmTypeService;

//     /**
//      * 查询设备类型管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vmType:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(VmType vmType)
//     {
//         startPage();
//         List<VmType> list = vmTypeService.selectVmTypeList(vmType);
//         return getDataTable(list);
//     }

//     /**
//      * 导出设备类型管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vmType:export')")
//     @Log(title = "设备类型管理", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, VmType vmType)
//     {
//         List<VmType> list = vmTypeService.selectVmTypeList(vmType);
//         ExcelUtil<VmType> util = new ExcelUtil<VmType>(VmType.class);
//         util.exportExcel(response, list, "设备类型管理数据");
//     }

//     /**
//      * 获取设备类型管理详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vmType:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(vmTypeService.selectVmTypeById(id));
//     }

//     /**
//      * 新增设备类型管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vmType:add')")
//     @Log(title = "设备类型管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody VmType vmType)
//     {
//         return toAjax(vmTypeService.insertVmType(vmType));
//     }

//     /**
//      * 修改设备类型管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vmType:edit')")
//     @Log(title = "设备类型管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody VmType vmType)
//     {
//         return toAjax(vmTypeService.updateVmType(vmType));
//     }

//     /**
//      * 删除设备类型管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vmType:remove')")
//     @Log(title = "设备类型管理", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(vmTypeService.deleteVmTypeByIds(ids));
//     }
// }
