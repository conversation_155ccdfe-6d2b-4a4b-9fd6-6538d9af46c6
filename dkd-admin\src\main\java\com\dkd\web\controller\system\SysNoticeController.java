package com.dkd.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.system.domain.SysNotice;
import com.dkd.system.service.ISysNoticeService;

/**
 * 公告 信息操作处理
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块通知公告相关的HTTP请求
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController
{
    // 注入通知公告业务层接口
    @Autowired
    private ISysNoticeService noticeService;

    /**
     * 获取通知公告列表接口
     * 需要'system:notice:list'权限
     * 请求路径：GET /system/notice/list
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysNotice notice)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询通知公告数据
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 根据通知公告编号获取详细信息接口
     * 需要'system:notice:query'权限
     * 请求路径：GET /system/notice/{noticeId}
     */
    @PreAuthorize("@ss.hasPermi('system:notice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable Long noticeId)
    {
        // 调用服务层方法获取通知公告详情
        return success(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告接口
     * 需要'system:notice:add'权限
     * 请求路径：POST /system/notice
     */
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysNotice notice)
    {
        // 设置创建人
        notice.setCreateBy(getUsername());
        // 调用服务层方法新增通知公告
        return toAjax(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知公告接口
     * 需要'system:notice:edit'权限
     * 请求路径：PUT /system/notice
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysNotice notice)
    {
        // 设置更新人
        notice.setUpdateBy(getUsername());
        // 调用服务层方法修改通知公告
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告接口
     * 需要'system:notice:remove'权限
     * 请求路径：DELETE /system/notice/{noticeIds}
     */
    @PreAuthorize("@ss.hasPermi('system:notice:remove')")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds)
    {
        // 调用服务层方法删除指定ID的通知公告
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }
}






// @RestController
// @RequestMapping("/system/notice")
// public class SysNoticeController extends BaseController
// {
//     @Autowired
//     private ISysNoticeService noticeService;

//     /**
//      * 获取通知公告列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:notice:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysNotice notice)
//     {
//         startPage();
//         List<SysNotice> list = noticeService.selectNoticeList(notice);
//         return getDataTable(list);
//     }

//     /**
//      * 根据通知公告编号获取详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('system:notice:query')")
//     @GetMapping(value = "/{noticeId}")
//     public AjaxResult getInfo(@PathVariable Long noticeId)
//     {
//         return success(noticeService.selectNoticeById(noticeId));
//     }

//     /**
//      * 新增通知公告
//      */
//     @PreAuthorize("@ss.hasPermi('system:notice:add')")
//     @Log(title = "通知公告", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysNotice notice)
//     {
//         notice.setCreateBy(getUsername());
//         return toAjax(noticeService.insertNotice(notice));
//     }

//     /**
//      * 修改通知公告
//      */
//     @PreAuthorize("@ss.hasPermi('system:notice:edit')")
//     @Log(title = "通知公告", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysNotice notice)
//     {
//         notice.setUpdateBy(getUsername());
//         return toAjax(noticeService.updateNotice(notice));
//     }

//     /**
//      * 删除通知公告
//      */
//     @PreAuthorize("@ss.hasPermi('system:notice:remove')")
//     @Log(title = "通知公告", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{noticeIds}")
//     public AjaxResult remove(@PathVariable Long[] noticeIds)
//     {
//         return toAjax(noticeService.deleteNoticeByIds(noticeIds));
//     }
// }
