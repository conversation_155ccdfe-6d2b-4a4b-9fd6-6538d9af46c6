// 包声明：定义当前类所属的包路径
package com.dkd.framework.web.service;

// 导入Java集合框架中的Set接口，用于存储权限集合
import java.util.Set;
// 导入Spring框架的Service注解，用于标记服务层组件
import org.springframework.stereotype.Service;
// 导入Spring工具类，用于判断集合是否为空
import org.springframework.util.CollectionUtils;
// 导入系统常量类，包含权限相关的常量定义
import com.dkd.common.constant.Constants;
// 导入系统角色实体类
import com.dkd.common.core.domain.entity.SysRole;
// 导入登录用户模型类
import com.dkd.common.core.domain.model.LoginUser;
// 导入安全工具类，用于获取当前登录用户信息
import com.dkd.common.utils.SecurityUtils;
// 导入字符串工具类，提供字符串操作方法
import com.dkd.common.utils.StringUtils;
// 导入权限上下文持有者，用于存储当前权限检查的上下文信息
import com.dkd.framework.security.context.PermissionContextHolder;

/**
 * RuoYi首创 自定义权限实现，ss取自SpringSecurity首字母
 * 这个服务类提供了完整的权限验证功能，包括：
 * 1. 单个权限验证
 * 2. 多个权限验证（任意一个）
 * 3. 角色验证
 * 4. 多个角色验证（任意一个）
 *
 * <AUTHOR>
 */
@Service("ss") // 将此类注册为Spring服务组件，bean名称为"ss"
public class PermissionService
{
    /**
     * 验证用户是否具备某权限
     * 这是最基础的权限验证方法，用于检查当前登录用户是否拥有指定的权限
     *
     * @param permission 权限字符串，例如："system:user:add"
     * @return 用户是否具备某权限，true表示有权限，false表示无权限
     */
    public boolean hasPermi(String permission)
    {
        // 检查权限字符串是否为空，如果为空则直接返回false
        if (StringUtils.isEmpty(permission))
        {
            return false; // 权限字符串为空，无权限
        }
        // 从安全上下文中获取当前登录的用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 检查用户是否为空或用户权限集合是否为空
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions()))
        {
            return false; // 用户未登录或无任何权限，返回false
        }
        // 将当前检查的权限设置到权限上下文中，用于日志记录或审计
        PermissionContextHolder.setContext(permission);
        // 调用私有方法检查用户权限集合中是否包含指定权限
        return hasPermissions(loginUser.getPermissions(), permission);
    }

    /**
     * 验证用户是否不具备某权限，与 hasPermi逻辑相反
     * 这个方法用于反向权限检查，当需要确认用户没有某个权限时使用
     *
     * @param permission 权限字符串，例如："system:user:delete"
     * @return 用户是否不具备某权限，true表示没有权限，false表示有权限
     */
    public boolean lacksPermi(String permission)
    {
        // 调用hasPermi方法并取反，如果hasPermi返回true，则lacksPermi返回false
        return hasPermi(permission) != true;
    }

    /**
     * 验证用户是否具有以下任意一个权限
     * 这个方法用于检查用户是否拥有多个权限中的任意一个
     * 权限字符串使用逗号分隔，例如："system:user:add,system:user:edit"
     *
     * @param permissions 以 PERMISSION_DELIMETER 为分隔符的权限列表
     * @return 用户是否具有以下任意一个权限，true表示至少有一个权限，false表示都没有
     */
    public boolean hasAnyPermi(String permissions)
    {
        // 检查权限字符串是否为空
        if (StringUtils.isEmpty(permissions))
        {
            return false; // 权限列表为空，返回false
        }
        // 获取当前登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 检查用户是否为空或用户权限集合是否为空
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions()))
        {
            return false; // 用户未登录或无任何权限，返回false
        }
        // 将权限列表设置到权限上下文中
        PermissionContextHolder.setContext(permissions);
        // 获取用户的权限集合
        Set<String> authorities = loginUser.getPermissions();
        // 使用权限分隔符（逗号）分割权限字符串，遍历每个权限
        for (String permission : permissions.split(Constants.PERMISSION_DELIMETER))
        {
            // 检查权限是否不为空，并且用户是否拥有该权限
            if (permission != null && hasPermissions(authorities, permission))
            {
                return true; // 找到一个匹配的权限，立即返回true
            }
        }
        return false; // 遍历完所有权限都没有匹配，返回false
    }

    /**
     * 判断用户是否拥有某个角色
     * 这个方法用于检查当前登录用户是否拥有指定的角色
     *
     * @param role 角色字符串，例如："admin"、"user"等
     * @return 用户是否具备某角色，true表示有该角色，false表示没有
     */
    public boolean hasRole(String role)
    {
        // 检查角色字符串是否为空
        if (StringUtils.isEmpty(role))
        {
            return false; // 角色字符串为空，返回false
        }
        // 获取当前登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 检查用户是否为空或用户角色列表是否为空
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getUser().getRoles()))
        {
            return false; // 用户未登录或无任何角色，返回false
        }
        // 遍历用户的所有角色
        for (SysRole sysRole : loginUser.getUser().getRoles())
        {
            // 获取角色的关键字（角色标识）
            String roleKey = sysRole.getRoleKey();
            // 检查是否为超级管理员角色，或者角色标识与目标角色匹配
            if (Constants.SUPER_ADMIN.equals(roleKey) || roleKey.equals(StringUtils.trim(role)))
            {
                return true; // 找到匹配的角色，返回true
            }
        }
        return false; // 遍历完所有角色都没有匹配，返回false
    }

    /**
     * 验证用户是否不具备某角色，与 hasRole逻辑相反
     * 这个方法用于反向角色检查，当需要确认用户没有某个角色时使用
     *
     * @param role 角色名称，例如："admin"
     * @return 用户是否不具备某角色，true表示没有该角色，false表示有该角色
     */
    public boolean lacksRole(String role)
    {
        // 调用hasRole方法并取反，如果hasRole返回true，则lacksRole返回false
        return hasRole(role) != true;
    }

    /**
     * 验证用户是否具有以下任意一个角色
     * 这个方法用于检查用户是否拥有多个角色中的任意一个
     * 角色字符串使用逗号分隔，例如："admin,user,manager"
     *
     * @param roles 以 ROLE_DELIMETER 为分隔符的角色列表
     * @return 用户是否具有以下任意一个角色，true表示至少有一个角色，false表示都没有
     */
    public boolean hasAnyRoles(String roles)
    {
        // 检查角色字符串是否为空
        if (StringUtils.isEmpty(roles))
        {
            return false; // 角色列表为空，返回false
        }
        // 获取当前登录用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 检查用户是否为空或用户角色列表是否为空
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getUser().getRoles()))
        {
            return false; // 用户未登录或无任何角色，返回false
        }
        // 使用角色分隔符（逗号）分割角色字符串，遍历每个角色
        for (String role : roles.split(Constants.ROLE_DELIMETER))
        {
            // 检查用户是否拥有当前遍历的角色
            if (hasRole(role))
            {
                return true; // 找到一个匹配的角色，立即返回true
            }
        }
        return false; // 遍历完所有角色都没有匹配，返回false
    }

    /**
     * 判断是否包含权限
     * 这是一个私有辅助方法，用于检查权限集合中是否包含指定的权限
     * 支持两种情况：1.拥有所有权限(*:*:*) 2.拥有具体的权限
     *
     * @param permissions 用户拥有的权限列表集合
     * @param permission 要检查的权限字符串
     * @return 用户是否具备某权限，true表示有权限，false表示无权限
     */
    private boolean hasPermissions(Set<String> permissions, String permission)
    {
        // 检查权限集合中是否包含全部权限标识或者包含指定的权限（去除首尾空格后）
        // Constants.ALL_PERMISSION 通常是 "*:*:*" 表示拥有所有权限
        // StringUtils.trim(permission) 去除权限字符串的首尾空格
        return permissions.contains(Constants.ALL_PERMISSION) || permissions.contains(StringUtils.trim(permission));
    }
} // 类结束标记
