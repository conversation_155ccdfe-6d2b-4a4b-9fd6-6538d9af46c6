// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.Documented; // 用于标记注解是否包含在JavaDoc中
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Inherited; // 标记注解可以被子类继承
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型

/**
 * 自定义注解防止表单重复提交
 * 用于防止用户在短时间内重复提交同一个表单
 * 通过AOP切面拦截带有此注解的方法，基于Redis或内存缓存实现防重复提交
 * 常用于订单提交、数据保存等重要操作，避免因网络延迟或用户误操作导致的重复提交
 *
 * <AUTHOR>
 */
@Inherited // 指定注解可以被子类继承
@Target(ElementType.METHOD) // 指定注解只能应用于方法
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Documented // 指定注解会被包含在JavaDoc中
public @interface RepeatSubmit // 定义防重复提交注解接口
{
    /**
     * 间隔时间(ms)，小于此时间视为重复提交
     * 指定两次提交之间的最小时间间隔
     * 在此时间内的重复请求将被拒绝
     * 单位为毫秒，默认5秒
     *
     * @return 时间间隔（毫秒）
     */
    public int interval() default 5000; // 定义时间间隔属性，默认值为5000毫秒

    /**
     * 提示消息
     * 当检测到重复提交时返回给用户的提示信息
     * 可以自定义消息内容，提供更好的用户体验
     *
     * @return 提示消息
     */
    public String message() default "不允许重复提交，请稍候再试"; // 定义提示消息属性，默认值为标准提示信息
}
