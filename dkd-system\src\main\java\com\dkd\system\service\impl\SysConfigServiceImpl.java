package com.dkd.system.service.impl;

import java.util.Collection;
import java.util.List;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.common.annotation.DataSource;
import com.dkd.common.constant.CacheConstants;
import com.dkd.common.constant.UserConstants;
import com.dkd.common.core.redis.RedisCache;
import com.dkd.common.core.text.Convert;
import com.dkd.common.enums.DataSourceType;
import com.dkd.common.exception.ServiceException;
import com.dkd.common.utils.StringUtils;
import com.dkd.system.domain.SysConfig;
import com.dkd.system.mapper.SysConfigMapper;
import com.dkd.system.service.ISysConfigService;

/**
 * 参数配置 服务层实现
 * 
 * <AUTHOR>
 */
// 定义系统参数配置服务实现类
@Service
public class SysConfigServiceImpl implements ISysConfigService
{
    // 注入数据库操作Mapper
    @Autowired
    private SysConfigMapper configMapper;

    // 注入Redis缓存操作组件
    @Autowired
    private RedisCache redisCache;

    /**
     * 项目启动时初始化参数到缓存
     * 使用@PostConstruct注解确保在Bean初始化完成后执行
     */
    @PostConstruct
    public void init()
    {
        loadingConfigCache();
    }

    /**
     * 根据ID查询具体参数配置信息
     * 
     * @param configId 参数配置ID
     * @return 参数配置实体对象
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public SysConfig selectConfigById(Long configId)
    {
        // 创建参数配置对象并设置ID
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        // 调用Mapper查询数据库
        return configMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数值
     * 优先从Redis缓存中获取，缓存不存在则从数据库获取，并写入缓存
     * 
     * @param configKey 参数键名
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey)
    {
        // 尝试从缓存获取数据
        String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue))
        {
            return configValue;
        }
        
        // 缓存不存在则查询数据库
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = configMapper.selectConfig(config);
        
        // 查询结果存在，则更新缓存
        if (StringUtils.isNotNull(retConfig))
        {
            redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        
        // 数据库也不存在返回空字符串
        return StringUtils.EMPTY;
    }

    /**
     * 获取验证码开关状态
     * 默认开启（true）如果未找到配置或配置为false则关闭
     * 
     * @return boolean 验证码启用状态
     */
    @Override
    public boolean selectCaptchaEnabled()
    {
        // 查询验证码相关配置
        String captchaEnabled = selectConfigByKey("sys.account.captchaEnabled");
        
        // 如果没有明确配置，默认开启
        if (StringUtils.isEmpty(captchaEnabled))
        {
            return true;
        }
        
        // 返回转换后的布尔值
        return Convert.toBool(captchaEnabled);
    }

    /**
     * 查询参数配置列表
     * 
     * @param config 查询条件封装对象
     * @return 符合条件的参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config)
    {
        // 直接调用Mapper查询数据库
        return configMapper.selectConfigList(config);
    }

    /**
     * 新增参数配置
     * 成功插入后同步更新Redis缓存
     * 
     * @param config 待新增的参数配置
     * @return 插入影响行数
     */
    @Override
    public int insertConfig(SysConfig config)
    {
        // 插入数据库记录
        int row = configMapper.insertConfig(config);
        if (row > 0)
        {
            // 插入成功则更新缓存
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 修改参数配置
     * 若修改了configKey需删除旧key缓存，并添加新key缓存
     * 
     * @param config 待更新的参数配置
     * @return 更新影响行数
     */
    @Override
    public int updateConfig(SysConfig config)
    {
        // 查询当前已存在的配置
        SysConfig temp = configMapper.selectConfigById(config.getConfigId());
        
        // 判断是否更改了configKey
        if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey()))
        {
            // 删除旧key的缓存
            redisCache.deleteObject(getCacheKey(temp.getConfigKey()));
        }

        // 执行更新操作
        int row = configMapper.updateConfig(config);
        if (row > 0)
        {
            // 更新成功则更新缓存
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 批量删除参数配置
     * 内置参数不能删除
     * 
     * @param configIds 待删除的参数ID数组
     */
    @Override
    public void deleteConfigByIds(Long[] configIds)
    {
        for (Long configId : configIds)
        {
            // 查询待删除的配置
            SysConfig config = selectConfigById(configId);
            
            // 校验是否为内置参数
            if (StringUtils.equals(UserConstants.YES, config.getConfigType()))
            {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
            }
            
            // 删除数据库记录
            configMapper.deleteConfigById(configId);
            
            // 同步删除缓存
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载所有参数配置到缓存
     * 系统启动时或需要刷新缓存时调用
     */
    @Override
    public void loadingConfigCache()
    {
        // 查询全部参数配置
        List<SysConfig> configsList = configMapper.selectConfigList(new SysConfig());
        for (SysConfig config : configsList)
        {
            // 将每个参数单独写入缓存
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清除所有参数缓存
     */
    @Override
    public void clearConfigCache()
    {
        // 查找所有以SYS_CONFIG_KEY开头的缓存键
        Collection<String> keys = redisCache.keys(CacheConstants.SYS_CONFIG_KEY + "*");
        // 删除匹配的所有缓存
        redisCache.deleteObject(keys);
    }

    /**
     * 重置参数缓存
     * 先清空再重新加载
     */
    @Override
    public void resetConfigCache()
    {
        clearConfigCache();   // 清空现有缓存
        loadingConfigCache(); // 重新加载缓存
    }

    /**
     * 校验参数键名唯一性
     * 
     * @param config 待校验的参数配置
     * @return 是否唯一标识
     */
    @Override
    public boolean checkConfigKeyUnique(SysConfig config)
    {
        // 获取当前要校验的configId，为空默认赋值-1L
        Long configId = StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId();
        
        // 查询是否存在相同configKey的记录
        SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
        
        // 存在且不是当前记录，说明不唯一
        if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        
        // 不存在其他记录，说明唯一
        return UserConstants.UNIQUE;
    }

    /**
     * 构建缓存key
     * 
     * @param configKey 原始参数键名
     * @return 拼接后的完整缓存键
     */
    private String getCacheKey(String configKey)
    {
        // 在原始键前加上统一前缀
        return CacheConstants.SYS_CONFIG_KEY + configKey;
    }
}



// @Service
// public class SysConfigServiceImpl implements ISysConfigService
// {
//     @Autowired
//     private SysConfigMapper configMapper;

//     @Autowired
//     private RedisCache redisCache;

//     /**
//      * 项目启动时，初始化参数到缓存
//      */
//     @PostConstruct
//     public void init()
//     {
//         loadingConfigCache();
//     }

//     /**
//      * 查询参数配置信息
//      * 
//      * @param configId 参数配置ID
//      * @return 参数配置信息
//      */
//     @Override
//     @DataSource(DataSourceType.MASTER)
//     public SysConfig selectConfigById(Long configId)
//     {
//         SysConfig config = new SysConfig();
//         config.setConfigId(configId);
//         return configMapper.selectConfig(config);
//     }

//     /**
//      * 根据键名查询参数配置信息
//      * 
//      * @param configKey 参数key
//      * @return 参数键值
//      */
//     @Override
//     public String selectConfigByKey(String configKey)
//     {
//         String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
//         if (StringUtils.isNotEmpty(configValue))
//         {
//             return configValue;
//         }
//         SysConfig config = new SysConfig();
//         config.setConfigKey(configKey);
//         SysConfig retConfig = configMapper.selectConfig(config);
//         if (StringUtils.isNotNull(retConfig))
//         {
//             redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
//             return retConfig.getConfigValue();
//         }
//         return StringUtils.EMPTY;
//     }

//     /**
//      * 获取验证码开关
//      * 
//      * @return true开启，false关闭
//      */
//     @Override
//     public boolean selectCaptchaEnabled()
//     {
//         String captchaEnabled = selectConfigByKey("sys.account.captchaEnabled");
//         if (StringUtils.isEmpty(captchaEnabled))
//         {
//             return true;
//         }
//         return Convert.toBool(captchaEnabled);
//     }

//     /**
//      * 查询参数配置列表
//      * 
//      * @param config 参数配置信息
//      * @return 参数配置集合
//      */
//     @Override
//     public List<SysConfig> selectConfigList(SysConfig config)
//     {
//         return configMapper.selectConfigList(config);
//     }

//     /**
//      * 新增参数配置
//      * 
//      * @param config 参数配置信息
//      * @return 结果
//      */
//     @Override
//     public int insertConfig(SysConfig config)
//     {
//         int row = configMapper.insertConfig(config);
//         if (row > 0)
//         {
//             redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
//         }
//         return row;
//     }

//     /**
//      * 修改参数配置
//      * 
//      * @param config 参数配置信息
//      * @return 结果
//      */
//     @Override
//     public int updateConfig(SysConfig config)
//     {
//         SysConfig temp = configMapper.selectConfigById(config.getConfigId());
//         if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey()))
//         {
//             redisCache.deleteObject(getCacheKey(temp.getConfigKey()));
//         }

//         int row = configMapper.updateConfig(config);
//         if (row > 0)
//         {
//             redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
//         }
//         return row;
//     }

//     /**
//      * 批量删除参数信息
//      * 
//      * @param configIds 需要删除的参数ID
//      */
//     @Override
//     public void deleteConfigByIds(Long[] configIds)
//     {
//         for (Long configId : configIds)
//         {
//             SysConfig config = selectConfigById(configId);
//             if (StringUtils.equals(UserConstants.YES, config.getConfigType()))
//             {
//                 throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
//             }
//             configMapper.deleteConfigById(configId);
//             redisCache.deleteObject(getCacheKey(config.getConfigKey()));
//         }
//     }

//     /**
//      * 加载参数缓存数据
//      */
//     @Override
//     public void loadingConfigCache()
//     {
//         List<SysConfig> configsList = configMapper.selectConfigList(new SysConfig());
//         for (SysConfig config : configsList)
//         {
//             redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
//         }
//     }

//     /**
//      * 清空参数缓存数据
//      */
//     @Override
//     public void clearConfigCache()
//     {
//         Collection<String> keys = redisCache.keys(CacheConstants.SYS_CONFIG_KEY + "*");
//         redisCache.deleteObject(keys);
//     }

//     /**
//      * 重置参数缓存数据
//      */
//     @Override
//     public void resetConfigCache()
//     {
//         clearConfigCache();
//         loadingConfigCache();
//     }

//     /**
//      * 校验参数键名是否唯一
//      * 
//      * @param config 参数配置信息
//      * @return 结果
//      */
//     @Override
//     public boolean checkConfigKeyUnique(SysConfig config)
//     {
//         Long configId = StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId();
//         SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
//         if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue())
//         {
//             return UserConstants.NOT_UNIQUE;
//         }
//         return UserConstants.UNIQUE;
//     }

//     /**
//      * 设置cache key
//      * 
//      * @param configKey 参数键
//      * @return 缓存键key
//      */
//     private String getCacheKey(String configKey)
//     {
//         return CacheConstants.SYS_CONFIG_KEY + configKey;
//     }
// }
