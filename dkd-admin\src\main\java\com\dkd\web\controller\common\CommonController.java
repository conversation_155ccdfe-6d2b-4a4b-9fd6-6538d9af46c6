package com.dkd.web.controller.common;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.dkd.common.config.RuoYiConfig;
import com.dkd.common.constant.Constants;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.file.FileUploadUtils;
import com.dkd.common.utils.file.FileUtils;
import com.dkd.framework.config.ServerConfig;

/**
 * 通用请求处理
 * 通用请求处理控制器。
 * 提供文件下载、上传等通用功能接口。
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    /**
     * 日志记录器实例，用于记录控制器日志信息。
     */
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    /**
     * 自动注入 ServerConfig 实例，用于获取服务器相关配置。
     */
    @Autowired
    private ServerConfig serverConfig;

    /**
     * 自动注入 FileStorageService 实例，用于处理文件存储操作。
     */
    @Autowired
    private FileStorageService fileStorageService;// 注入实列

    /**
     * 文件路径分隔符，用于拼接多个文件路径。
     */
    private static final String FILE_DELIMETER = ",";


    /**
     * 通用下载请求。
     * 根据文件名下载指定文件，并可选择是否删除原文件。
     *
     * @param fileName 下载的文件名称
     * @param delete   是否在下载后删除源文件
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            // 检查文件名是否允许下载
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }

            // 构建真实文件名（防止重复）
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            // 构建文件路径
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            // 设置响应类型为二进制流
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            // 设置附件响应头以便浏览器识别为下载文件
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            // 写出文件字节到响应输出流
            FileUtils.writeBytes(filePath, response.getOutputStream());

            // 如果需要删除，则删除原始文件
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            // 记录下载失败日志
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个文件）。
     * 将上传的文件保存至服务器并返回访问地址。
     *
     * @param file 上传的文件对象
     * @return AjaxResult 包含文件访问地址等信息
     * @throws Exception 上传过程中可能抛出异常
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 指定 OSS 保存路径，按日期组织目录
            String objectName = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")) + "/";

            // 上传图片，成功返回文件信息
            FileInfo fileInfo = fileStorageService.of(file).setPath(objectName).upload();

            // 构造返回结果
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", fileInfo.getUrl());
            ajax.put("fileName", fileInfo.getUrl());  // 注意：这里的值要改为 url，前端访问的地址，而不是文件名称
            ajax.put("newFileName", fileInfo.getUrl());
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        }
        catch (Exception e)
        {
            // 返回错误信息
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（多个文件）。
     * 支持批量上传文件，并返回各文件相关信息。
     *
     * @param files 上传的文件列表
     * @return AjaxResult 包含所有文件的访问地址、名称等信息
     * @throws Exception 上传过程中可能抛出异常
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 获取上传文件的基础路径
            String filePath = RuoYiConfig.getUploadPath();

            // 初始化用于存储文件访问地址的列表
            List<String> urls = new ArrayList<>();

            // 初始化用于存储文件存储路径名称的列表
            List<String> fileNames = new ArrayList<>();

            // 初始化用于存储文件新名称的列表
            List<String> newFileNames = new ArrayList<>();

            // 初始化用于存储文件原始名称的列表
            List<String> originalFilenames = new ArrayList<>();

            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;

                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }

            // 构造返回结果
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));

            return ajax;
        }
        catch (Exception e)
        {
            // 返回错误信息
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载。
     * 支持通过资源标识符下载本地资源。
     *
     * @param resource 资源标识符
     * @param request  HTTP 请求对象
     * @param response HTTP 响应对象
     * @throws Exception 下载过程中可能抛出异常
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response) throws Exception
    {
        try
        {
            // 检查资源是否允许下载
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }

            // 获取本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 构建实际下载路径
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 获取下载文件名
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");

            // 设置响应类型为二进制流
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            // 设置附件响应头以便浏览器识别为下载文件
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            // 写出文件字节到响应输出流
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            // 记录下载失败日志
            log.error("下载文件失败", e);
        }
    }
}


// @RestController
// @RequestMapping("/common")
// public class CommonController
// {
//     private static final Logger log = LoggerFactory.getLogger(CommonController.class);

//     @Autowired
//     private ServerConfig serverConfig;

//     @Autowired
//     private FileStorageService fileStorageService;//注入实列

//     private static final String FILE_DELIMETER = ",";

//     /**
//      * 通用下载请求
//      * 
//      * @param fileName 文件名称
//      * @param delete 是否删除
//      */
//     @GetMapping("/download")
//     public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
//     {
//         try
//         {
//             if (!FileUtils.checkAllowDownload(fileName))
//             {
//                 throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
//             }
//             String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
//             String filePath = RuoYiConfig.getDownloadPath() + fileName;

//             response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
//             FileUtils.setAttachmentResponseHeader(response, realFileName);
//             FileUtils.writeBytes(filePath, response.getOutputStream());
//             if (delete)
//             {
//                 FileUtils.deleteFile(filePath);
//             }
//         }
//         catch (Exception e)
//         {
//             log.error("下载文件失败", e);
//         }
//     }

//     /**
//      * 通用上传请求（单个）
//      */
//     @PostMapping("/upload")
//     public AjaxResult uploadFile(MultipartFile file) throws Exception {

//         try {
//             // 指定oss保存文件路径
//             String objectName = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")) + "/";
//             // 上传图片，成功返回文件信息
//             FileInfo fileInfo = fileStorageService.of(file).setPath(objectName).upload();
//             // 设置返回结果
//             AjaxResult ajax = AjaxResult.success();
//             ajax.put("url", fileInfo.getUrl());
//             ajax.put("fileName", fileInfo.getUrl());  //注意：这里的值要改为url，前端访问的地址,需要文件的地址 而不是文件名称
//             ajax.put("newFileName", fileInfo.getUrl());
//             ajax.put("originalFilename", file.getOriginalFilename());
//             return ajax;
//         } catch (Exception e) {
//             return AjaxResult.error(e.getMessage());
//         }
//     }

//     /**
//      * 通用上传请求（多个）
//      */
//     @PostMapping("/uploads")
//     public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
//     {
//         try
//         {
//             // 上传文件路径
//             String filePath = RuoYiConfig.getUploadPath();
//             List<String> urls = new ArrayList<String>();
//             List<String> fileNames = new ArrayList<String>();
//             List<String> newFileNames = new ArrayList<String>();
//             List<String> originalFilenames = new ArrayList<String>();
//             for (MultipartFile file : files)
//             {
//                 // 上传并返回新文件名称
//                 String fileName = FileUploadUtils.upload(filePath, file);
//                 String url = serverConfig.getUrl() + fileName;
//                 urls.add(url);
//                 fileNames.add(fileName);
//                 newFileNames.add(FileUtils.getName(fileName));
//                 originalFilenames.add(file.getOriginalFilename());
//             }
//             AjaxResult ajax = AjaxResult.success();
//             ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
//             ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
//             ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
//             ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
//             return ajax;
//         }
//         catch (Exception e)
//         {
//             return AjaxResult.error(e.getMessage());
//         }
//     }

//     /**
//      * 本地资源通用下载
//      */
//     @GetMapping("/download/resource")
//     public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
//             throws Exception
//     {
//         try
//         {
//             if (!FileUtils.checkAllowDownload(resource))
//             {
//                 throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
//             }
//             // 本地资源路径
//             String localPath = RuoYiConfig.getProfile();
//             // 数据库资源地址
//             String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
//             // 下载名称
//             String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
//             response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
//             FileUtils.setAttachmentResponseHeader(response, downloadName);
//             FileUtils.writeBytes(downloadPath, response.getOutputStream());
//         }
//         catch (Exception e)
//         {
//             log.error("下载文件失败", e);
//         }
//     }
// }
