<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dkd.manage.mapper.PartnerMapper">
    
    <resultMap type="Partner" id="PartnerResult">
        <result property="id"    column="id"    />
        <result property="partnerName"    column="partner_name"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="profitRatio"    column="profit_ratio"    />
        <result property="account"    column="account"    />
        <result property="password"    column="password"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPartnerVo">
        select id, partner_name, contact_person, contact_phone, profit_ratio, account, password, create_time, update_time, create_by, update_by, remark from tb_partner
    </sql>

    <select id="selectPartnerList" parameterType="Partner" resultMap="PartnerResult">
        <include refid="selectPartnerVo"/>
        <where>  
            <if test="partnerName != null  and partnerName != ''"> and partner_name like concat('%', #{partnerName}, '%')</if>
        </where>
    </select>
    
    <select id="selectPartnerById" parameterType="Long" resultMap="PartnerResult">
        <include refid="selectPartnerVo"/>
        where id = #{id}
    </select>

<!--    <select id="selectPartnerVoList" resultType="com.dkd.manage.domain.vo.PartnerVo">-->
<!--        SELECT-->
<!--        p.id,-->
<!--        p.partner_name,-->
<!--        p.remark,-->
<!--        IFNULL(n.node_count, 0) AS node_count-->
<!--        FROM tb_partner p-->
<!--        LEFT JOIN (-->
<!--        SELECT partner_id, COUNT(*) AS node_count-->
<!--        FROM tb_node-->
<!--        GROUP BY partner_id-->
<!--        ) n ON p.id = n.partner_id-->
<!--        <where>-->
<!--            <if test="partnerName != null and partnerName != ''">-->
<!--                AND p.partner_name LIKE CONCAT('%', #{partnerName}, '%')-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->


    <select id="selectPartnerVoList" resultType="com.dkd.manage.domain.vo.PartnerVo">
        SELECT p.*, COUNT(n.id) AS node_count FROM tb_partner p
        LEFT JOIN tb_node n ON p.id = n.partner_id
        <where>
            <if test="partnerName != null  and partnerName != ''">and partner_name like concat('%', #{partnerName},'%')
            </if>
        </where>
        GROUP BY p.id
    </select>

    <insert id="insertPartner" parameterType="Partner" useGeneratedKeys="true" keyProperty="id">
        insert into tb_partner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="partnerName != null and partnerName != ''">partner_name,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="profitRatio != null">profit_ratio,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="partnerName != null and partnerName != ''">#{partnerName},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="profitRatio != null">#{profitRatio},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePartner" parameterType="Partner">
        update tb_partner
        <trim prefix="SET" suffixOverrides=",">
            <if test="partnerName != null and partnerName != ''">partner_name = #{partnerName},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="profitRatio != null">profit_ratio = #{profitRatio},</if>
            <if test="account != null and account != ''">account = #{account},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deletePartnerById" parameterType="Long">
        delete from tb_partner where id = #{id}
    </delete>

    <delete id="deletePartnerByIds" parameterType="String">
        delete from tb_partner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>