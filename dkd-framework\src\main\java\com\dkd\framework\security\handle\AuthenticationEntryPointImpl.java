// 包声明：定义当前类所属的包路径，位于安全框架的处理器包中
package com.dkd.framework.security.handle;

// 导入Java IO异常类，用于处理输入输出异常
import java.io.IOException;
// 导入Java序列化接口，用于对象序列化
import java.io.Serializable;
// 导入HTTP请求接口，用于获取请求信息
import javax.servlet.http.HttpServletRequest;
// 导入HTTP响应接口，用于设置响应信息
import javax.servlet.http.HttpServletResponse;
// 导入Spring Security认证异常类，用于处理认证失败的情况
import org.springframework.security.core.AuthenticationException;
// 导入Spring Security认证入口点接口，用于处理未认证的请求
import org.springframework.security.web.AuthenticationEntryPoint;
// 导入Spring组件注解
import org.springframework.stereotype.Component;
// 导入阿里巴巴FastJSON库，用于JSON序列化和反序列化
import com.alibaba.fastjson2.JSON;
// 导入HTTP状态码常量类
import com.dkd.common.constant.HttpStatus;
// 导入Ajax响应结果类，用于统一的响应格式
import com.dkd.common.core.domain.AjaxResult;
// 导入Servlet工具类，用于响应处理
import com.dkd.common.utils.ServletUtils;
// 导入字符串工具类，用于字符串格式化
import com.dkd.common.utils.StringUtils;

/**
 * 认证失败处理类
 * 这个类实现了Spring Security的AuthenticationEntryPoint接口，
 * 用于处理用户未认证或认证失败时的情况，返回统一的错误响应。
 *
 * 主要功能：
 * 1. 处理未认证的请求访问
 * 2. 处理JWT令牌无效或过期的情况
 * 3. 返回统一格式的错误响应
 * 4. 记录认证失败的详细信息
 *
 * 触发场景：
 * - 用户未登录访问受保护的资源
 * - JWT令牌已过期或无效
 * - JWT令牌格式错误或被篡改
 * - 用户权限不足访问特定资源
 *
 * <AUTHOR>
 */
@Component // 将此类标记为Spring组件，由Spring容器管理
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint, Serializable
{
    /**
     * 序列化版本号
     * 用于确保序列化和反序列化的兼容性，当类结构发生变化时需要更新此值
     */
    private static final long serialVersionUID = -8970718410437077606L;

    /**
     * 处理认证失败的核心方法
     * 当用户未认证或认证失败时，Spring Security会调用此方法来处理响应
     *
     * @param request HTTP请求对象，包含客户端的请求信息
     * @param response HTTP响应对象，用于向客户端发送错误响应
     * @param e 认证异常对象，包含认证失败的具体原因
     * @throws IOException 当输入输出操作发生异常时抛出
     */
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e)
            throws IOException
    {
        // 设置HTTP状态码为401未授权
        // HttpStatus.UNAUTHORIZED对应HTTP 401状态码，表示请求需要用户认证
        int code = HttpStatus.UNAUTHORIZED;

        // 构造错误消息，包含请求的URI信息
        // StringUtils.format()方法用于格式化字符串，{}是占位符
        // request.getRequestURI()获取用户尝试访问的资源路径
        String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());

        // 将错误信息以JSON格式返回给客户端
        // AjaxResult.error(code, msg)创建一个包含错误码和错误消息的响应对象
        // JSON.toJSONString()将响应对象转换为JSON字符串
        // ServletUtils.renderString()将JSON字符串写入HTTP响应中
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
    }
} // 类结束标记
