import request from '@/utils/request'
import { getToken } from '@/utils/auth'

export function sendMessage(message, sessionId) {
  return request({
    url: '/ai/chat',
    method: 'post',
    data: {
      sessionId: sessionId,
      input: {
        messages: [{
          role: "user",
          content: message
        }]
      },
      parameters: {
        result_format: "text"
      }
    }
  })
}

// 修改流式请求方法，添加AbortController支持
export function sendStreamMessage(message, sessionId, abortController) {
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken()
    },
    body: JSON.stringify({
      sessionId: sessionId,
      input: {
        messages: [{
          role: "user",
          content: message
        }]
      }
    })
  }
  
  // 如果提供了AbortController，则添加到请求选项中
  if (abortController) {
    options.signal = abortController.signal
  }
  
  // 使用 /dev-api 前缀，确保被正确代理
  return fetch('/dev-api/ai/chat-stream', options)
}