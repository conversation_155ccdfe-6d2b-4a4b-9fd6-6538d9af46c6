<template>
  <div class="app-container">
    <!-- 商品类型统计图表 -->
    <el-card class="mb-4" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>商品类型数量统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新统计
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 400px;"></div>
    </el-card>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品类型" prop="className">
        <el-input
          v-model="queryParams.className"
          placeholder="请输入商品类型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:skuClass:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:skuClass:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:skuClass:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:skuClass:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="skuClassList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" align="center" width="50" />
      <el-table-column label="商品类型" align="center" prop="className" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['manage:skuClass:edit']">修改</el-button>
          <el-button link type="primary"  @click="handleDelete(scope.row)" v-hasPermi="['manage:skuClass:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改商品类型对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="skuClassRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品类型" prop="className">
          <el-input v-model="form.className" placeholder="请输入商品类型" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SkuClass">
import { listSkuClass, getSkuClass, delSkuClass, addSkuClass, updateSkuClass } from "@/api/manage/skuClass";
import { listSku } from "@/api/manage/sku";
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const skuClassList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    className: null,
  },
  rules: {
    className: [
      { required: true, message: "商品类型不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询商品类型列表 */
function getList() {
  loading.value = true;
  listSkuClass(queryParams.value).then(response => {
    skuClassList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 刷新图表数据
    if (chartInstance.value) {
      loadChartData();
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    classId: null,
    className: null,
    parentId: null
  };
  proxy.resetForm("skuClassRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.classId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加商品类型";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _classId = row.classId || ids.value
  getSkuClass(_classId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改商品类型";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["skuClassRef"].validate(valid => {
    if (valid) {
      if (form.value.classId != null) {
        updateSkuClass(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSkuClass(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _classIds = row.classId || ids.value;
  proxy.$modal.confirm('是否确认删除商品类型编号为"' + _classIds + '"的数据项？').then(function() {
    return delSkuClass(_classIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('manage/skuClass/export', {
    ...queryParams.value
  }, `skuClass_${new Date().getTime()}.xlsx`)
}

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    // 获取所有商品类型数据（不分页）
    const allSkuClassParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数值来获取所有数据
      className: null,
    };

    const allSkuClassResponse = await listSkuClass(allSkuClassParams);
    const allSkuClassList = allSkuClassResponse.rows || [];

    // 获取所有商品数据来统计每个类型的商品数量
    const allSkuParams = {
      pageNum: 1,
      pageSize: 10000,
    };

    const allSkuResponse = await listSku(allSkuParams);
    const allSkuList = allSkuResponse.rows || [];

    // 处理数据
    const classNames = [];
    const skuCounts = [];

    // 统计每个商品类型的商品数量
    allSkuClassList.forEach(skuClass => {
      classNames.push(skuClass.className);

      // 统计该类型下的商品数量
      const classSkus = allSkuList.filter(sku => sku.classId === skuClass.classId);
      skuCounts.push(classSkus.length);
    });

    // 调试日志
    console.log('商品类型名称:', classNames);
    console.log('商品数量:', skuCounts);

    // 如果没有数据，添加默认数据
    if (classNames.length === 0) {
      classNames.push('暂无数据');
      skuCounts.push(0);
    }

    updateChart(classNames, skuCounts);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(classNames, skuCounts) {
  if (!chartInstance.value) return;

  const option = {
    title: {
      text: '商品类型数量统计',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c} 个商品'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: classNames,
      axisLabel: {
        interval: 0,
        rotate: classNames.length > 5 ? 30 : 0,
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '商品数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      },
      axisLabel: {
        fontSize: 12,
        formatter: '{value}'
      },
      minInterval: 1,
      splitNumber: 5
    },
    series: [
      {
        name: '商品数量',
        type: 'bar',
        barWidth: '60%',
        data: skuCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#87CEEB' },
            { offset: 1, color: '#4682B4' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#4169E1' },
              { offset: 0.7, color: '#4169E1' },
              { offset: 1, color: '#87CEEB' }
            ])
          }
        }
      }
    ]
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询商品类型列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

getList();
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
