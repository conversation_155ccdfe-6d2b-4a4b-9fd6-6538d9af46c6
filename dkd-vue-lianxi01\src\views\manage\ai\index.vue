<template>
  <div class="chat-container">
    <div class="chat-wrapper">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="header-content">
          <div class="avatar-section">
            <div class="ai-avatar">
              <el-icon class="avatar-icon"><Service /></el-icon>
            </div>
            <div class="header-info">
              <h3 class="title">智能客服助手</h3>
              <p class="subtitle">24小时在线为您服务</p>
            </div>
          </div>
          <div class="status-indicator">
            <span class="status-dot"></span>
            <span class="status-text">在线</span>
          </div>
        </div>
      </div>

      <!-- 对话区域 -->
      <div class="chat-content">
        <div class="chat-box" ref="chatBox">
          <div v-for="(msg, index) in messages" :key="index"
              :class="['message', msg.role === 'user' ? 'user-message' : 'assistant-message']">

            <!-- AI消息头像 -->
            <div v-if="msg.role === 'assistant'" class="message-avatar">
              <div class="avatar ai-avatar-small">
                <el-icon><Service /></el-icon>
              </div>
            </div>

            <!-- 用户消息头像 -->
            <div v-if="msg.role === 'user'" class="message-avatar">
              <div class="avatar user-avatar-small">
                <el-icon><User /></el-icon>
              </div>
            </div>

            <div class="message-content">
              <div v-if="msg.loading" class="loading-container">
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span class="typing-text">客服正在回复中...</span>
              </div>
              <template v-else>
                <div v-if="msg.isQuestionList" class="quick-questions">
                  <div class="question-title">
                    <el-icon><QuestionFilled /></el-icon>
                    常见问题
                  </div>
                  <div class="questions-grid">
                    <div
                      v-for="(question, qIndex) in msg.questions"
                      :key="qIndex"
                      @click="handleQuickQuestion(question)"
                      class="question-card">
                      <el-icon class="question-icon"><Message /></el-icon>
                      <span>{{ question }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="message-text">
                  {{ msg.content }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-wrapper">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="1"
            :autosize="{ minRows: 1, maxRows: 4 }"
            placeholder="输入您的问题，按 Enter 发送..."
            @keyup.enter.exact="handleSend"
            @keyup.enter.shift.exact="addNewLine"
            class="message-input"
            resize="none"
          />
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSend"
            class="send-button"
            v-hasPermi="['manage:ai:list']"
            :disabled="!inputMessage.trim()"
          >
            <el-icon v-if="!loading"><Promotion /></el-icon>
            <span v-if="loading">发送中</span>
            <span v-else>发送</span>
          </el-button>
        </div>
        <div class="input-tips">
          <span class="tip-text">
            <el-icon><InfoFilled /></el-icon>
            按 Enter 发送，Shift + Enter 换行
          </span>
        </div>
      </div>
    </div>
  </div>
</template>




<script setup>
// 从Vue 3核心库导入响应式API和生命周期钩子
import { ref, nextTick, onMounted } from 'vue'
// 从Element Plus UI库导入消息提示组件
import { ElMessage } from 'element-plus'
// 只导入确定存在的基础图标，避免导入不存在的图标导致错误
import {
  User,        // 用户图标
  Service,     // 服务图标
  QuestionFilled, // 问号填充图标
  Message,     // 消息图标
  Promotion,   // 推广图标
  InfoFilled   // 信息填充图标
} from '@element-plus/icons-vue'
// 从API模块导入AI助手相关的接口函数
import { ragChat, knowledgeSearch, getKnowledgeStatus, clearSession, getAgentInfo } from '@/api/ai/assistant'

// 定义响应式数据：聊天消息列表，初始为空数组
const messages = ref([])
// 定义响应式数据：用户输入的消息内容，初始为空字符串
const inputMessage = ref('')
// 定义响应式数据：加载状态标识，用于显示加载动画
const loading = ref(false)
// 定义响应式引用：聊天框DOM元素的引用，用于滚动控制
const chatBox = ref(null)
// 定义响应式数据：会话ID，用于维持对话连续性
const sessionId = ref(null)

// RAG相关状态管理
const useRag = ref(true) // 是否使用RAG模式的开关，默认开启
// 知识库状态信息的响应式对象
const knowledgeStatus = ref({
  initialized: false, // 知识库是否已初始化
  stats: ''           // 知识库统计信息
})

// 初始化欢迎消息：在页面加载时向消息列表添加AI助手的欢迎语
messages.value.push({
  role: 'assistant', // 消息角色：助手
  // 欢迎消息内容，包含问候语和功能介绍
  content: '👋 您好！我是您的专属智能客服助手，很高兴为您服务！\n\n我可以帮助您解决各种问题，包括工单处理、系统操作、数据导入等。请随时告诉我您需要什么帮助！'
})

// 新增常见问题消息：提供快速问题选项供用户点击
messages.value.push({
  role: 'assistant', // 消息角色：助手
  // 常见问题列表，用户可以直接点击这些问题
  questions: [
    '我的工单有问题',        // 工单相关问题
    '待办工单怎么取消',      // 工单操作问题
    '如何批量导入商品',      // 商品管理问题
    '如何查看我的归属区域',  // 区域查询问题
    '设备故障如何处理',      // 设备维护问题
  ],
  isQuestionList: true // 标识这是一个问题列表消息，用于模板渲染判断
})

// 新增快速问题处理函数：当用户点击常见问题时调用
const handleQuickQuestion = (question) => {
  // 将点击的问题设置为输入框内容
  inputMessage.value = question
  // 自动发送该问题
  handleSend()
}

// 添加换行功能：在输入框中添加换行符
const addNewLine = () => {
  // 在当前输入内容后添加换行符
  inputMessage.value += '\n'
}



// RAG聊天方法：处理用户消息并调用RAG API获取回答
const handleRagSend = async () => {
  // 获取用户输入的消息并去除首尾空格
  const userMessage = inputMessage.value.trim()
  // 检查用户输入是否为空
  if (!userMessage) {
    // 如果输入为空，显示警告提示
    ElMessage.warning('请输入有效内容')
    return // 提前返回，不执行后续逻辑
  }

  // 添加用户消息到聊天记录中
  messages.value.push({
    role: 'user',        // 消息角色：用户
    content: userMessage // 消息内容：用户输入的文本
  })

  // 设置加载状态为true，显示加载动画
  loading.value = true
  // 创建临时的助手消息对象，用于显示加载状态
  const tempMessage = {
    role: 'assistant', // 消息角色：助手
    content: '',       // 初始内容为空
    loading: true      // 标记为加载中状态
  }
  // 将临时消息添加到聊天记录中
  messages.value.push(tempMessage)
  // 记录临时消息在数组中的索引，用于后续更新
  const tempIndex = messages.value.length - 1

  // 清空输入框内容
  inputMessage.value = ''
  // 等待DOM更新完成
  await nextTick()
  // 滚动到聊天框底部，显示最新消息
  scrollToBottom()

  try { // 使用try-catch块处理API调用可能的异常
    // 调用RAG聊天API，发送用户消息和会话ID
    const response = await ragChat({
      message: userMessage,        // 用户输入的消息内容
      sessionId: sessionId.value   // 当前会话ID，用于维持对话连续性
    })

    // 检查API响应是否成功
    if (response.code === 200) {
      // 更新会话ID：如果服务器返回了新的会话ID，则更新本地会话ID
      if (response.data.sessionId) {
        sessionId.value = response.data.sessionId
      }

      // 模拟打字效果：逐字显示AI回答，提升用户体验
      const fullText = response.data.content // 获取完整的AI回答文本
      let currentPos = 0 // 当前显示位置的索引

      // 取消临时消息的加载状态
      messages.value[tempIndex].loading = false

      // 创建定时器实现打字效果
      const typeInterval = setInterval(() => {
        // 检查是否还有未显示的字符
        if (currentPos < fullText.length) {
          // 更新消息内容，显示从开始到当前位置的文本
          messages.value[tempIndex].content = fullText.substring(0, currentPos + 1)
          currentPos++ // 移动到下一个字符
          scrollToBottom() // 滚动到底部保持最新消息可见
        } else {
          // 所有字符都已显示，清除定时器
          clearInterval(typeInterval)
        }
      }, 30) // 每30毫秒显示一个字符，实现打字效果

    } else {
      // API调用失败的处理：显示错误消息
      messages.value[tempIndex].content = '抱歉，我遇到了一些问题，请稍后再试。'
      messages.value[tempIndex].loading = false // 取消加载状态
      ElMessage.error(response.msg || '发送失败') // 显示错误提示
    }

  } catch (error) { // 捕获API调用过程中的所有异常
    // 在控制台输出详细的错误信息，便于调试
    console.error('RAG聊天错误:', error)

    // 定义默认的错误消息
    let errorMessage = '抱歉，我遇到了一些技术问题，请稍后再试。'
    let userMessage = '发送失败，请检查网络连接'

    // 根据不同的错误类型提供具体的错误提示
    if (error.name === 'AbortError') {
      // 请求被中止的错误（通常是超时）
      errorMessage = '请求超时，服务器响应较慢，请稍后重试'
      userMessage = '请求超时，请稍后重试'
    } else if (error.code === 'ECONNABORTED') {
      // 连接超时错误
      errorMessage = '请求超时，服务器正在处理中，请稍后重试'
      userMessage = '请求超时，请稍后重试'
    } else if (error.response) {
      // 服务器返回了错误状态码
      const status = error.response.status
      if (status === 500) {
        // 服务器内部错误
        errorMessage = '服务器内部错误，请稍后重试'
        userMessage = '服务器错误，请稍后重试'
      } else if (status === 503) {
        // 服务不可用错误
        errorMessage = '服务暂时不可用，请稍后重试'
        userMessage = '服务暂时不可用'
      } else {
        // 其他HTTP错误状态码
        errorMessage = `服务器错误 (${status})，请稍后重试`
        userMessage = `服务器错误 (${status})`
      }
    } else if (error.request) {
      // 请求发送失败（网络问题）
      errorMessage = '网络连接失败，请检查网络连接后重试'
      userMessage = '网络连接失败'
    }

    // 更新临时消息的内容为错误信息
    messages.value[tempIndex].content = errorMessage
    // 取消加载状态
    messages.value[tempIndex].loading = false
    // 显示用户友好的错误提示
    ElMessage.error(userMessage)
  } finally { // 无论成功还是失败都会执行的清理代码
    // 重置全局加载状态
    loading.value = false
  }
}

// 主要的发送消息处理函数：统一的消息发送入口
const handleSend = async () => {
  // 获取用户输入并去除首尾空格
  const userMessage = inputMessage.value.trim()
  // 验证用户输入是否为空
  if (!userMessage) {
    // 显示警告提示用户输入有效内容
    ElMessage.warning('请输入有效内容')
    return // 提前返回，不执行发送逻辑
  }

  // 默认使用RAG模式处理用户消息
  await handleRagSend()
}

// 滚动到聊天框底部的函数：确保最新消息始终可见
const scrollToBottom = () => {
  // 使用nextTick确保DOM更新完成后再执行滚动
  nextTick(() => {
    // 检查聊天框DOM元素是否存在
    if (chatBox.value) {
      // 平滑滚动到聊天框的底部
      chatBox.value.scrollTo({
        top: chatBox.value.scrollHeight, // 滚动到最大高度（底部）
        behavior: 'smooth'               // 使用平滑滚动动画
      })
    }
  })
}

// RAG相关方法集合
// 切换RAG模式的函数：在RAG智能模式和普通模式之间切换
const toggleRagMode = () => {
  // 切换RAG模式状态
  useRag.value = !useRag.value
  // 显示切换成功的提示消息
  ElMessage.success(useRag.value ? '已切换到RAG智能模式' : '已切换到普通模式')
}

// 清空会话历史的异步函数：重置聊天记录和会话状态
const handleClearSession = async () => {
  try { // 使用try-catch处理可能的API调用异常
    // 如果存在会话ID，调用API清空服务器端的会话记录
    if (sessionId.value) {
      await clearSession({ sessionId: sessionId.value })
    }
    // 清空本地的消息列表
    messages.value = []
    // 重置会话ID
    sessionId.value = null

    // 重新添加欢迎消息：恢复初始的聊天界面状态
    messages.value.push({
      role: 'assistant', // 消息角色：助手
      // 欢迎消息内容
      content: '👋 您好！我是您的专属智能客服助手，很高兴为您服务！\n\n我可以帮助您解决各种问题，包括工单处理、系统操作、数据导入等。请随时告诉我您需要什么帮助！'
    })

    // 重新添加常见问题列表
    messages.value.push({
      role: 'assistant', // 消息角色：助手
      // 常见问题选项列表
      questions: [
        '我的工单有问题',        // 工单相关问题
        '待办工单怎么取消',      // 工单操作问题
        '如何批量导入商品',      // 商品管理问题
        '如何查看我的归属区域',  // 区域查询问题
        '设备故障如何处理',      // 设备维护问题
        '数据统计怎么查看'       // 数据查看问题
      ],
      isQuestionList: true // 标识为问题列表消息
    })

    // 显示清空成功的提示
    ElMessage.success('会话已清空')
  } catch (error) { // 捕获清空会话过程中的异常
    // 在控制台输出错误信息
    console.error('清空会话失败:', error)
    // 显示错误提示给用户
    ElMessage.error('清空会话失败')
  }
}

// 获取智能体应用信息的异步函数：获取智能体应用的功能介绍
const handleGetAgentInfo = async () => {
  try { // 使用try-catch处理API调用可能的异常
    // 显示正在获取信息的提示
    ElMessage.info('正在获取智能体应用信息...')
    // 调用API获取智能体应用信息
    const response = await getAgentInfo({ query: '系统功能' })
    // 检查API响应是否成功
    if (response.code === 200) {
      // 显示获取成功的提示
      ElMessage.success('智能体应用信息获取成功')
      // 可以在这里处理返回的信息，比如显示在对话中
      console.log('智能体应用信息:', response.data)
    }
  } catch (error) { // 捕获获取过程中的异常
    // 在控制台输出错误信息
    console.error('获取智能体应用信息失败:', error)
    // 显示获取失败的错误提示
    ElMessage.error('获取智能体应用信息失败')
  }
}

// 检查知识库状态的异步函数：获取知识库的初始化状态和统计信息
const checkKnowledgeStatus = async () => {
  try { // 使用try-catch处理API调用可能的异常
    // 调用API获取知识库状态
    const response = await getKnowledgeStatus()
    // 检查API响应是否成功
    if (response.code === 200) {
      // 更新本地的知识库状态数据
      knowledgeStatus.value = response.data
      // 显示知识库状态信息
      ElMessage.success(`知识库状态: ${response.data.initialized ? '已初始化' : '未初始化'}`)
    }
  } catch (error) { // 捕获获取状态过程中的异常
    // 在控制台输出错误信息
    console.error('获取知识库状态失败:', error)
    // 显示错误提示给用户
    ElMessage.error('获取知识库状态失败')
  }
}

// 组件挂载时检查知识库状态：在组件初始化完成后自动检查知识库状态
onMounted(() => {
  // 调用检查知识库状态的函数
  checkKnowledgeStatus()
})
</script>

<style lang="scss" scoped>
.chat-container {
  height: calc(100vh - 120px);
  padding: 20px;
  background: linear-gradient(135deg, #0066CC 0%, #4A90E2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-wrapper {
  width: 100%;
  max-width: 900px;
  height: 100%;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

// 聊天头部样式
.chat-header {
  background: linear-gradient(135deg, #0066CC 0%, #4A90E2 100%);
  color: white;
  padding: 20px 30px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .avatar-section {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .ai-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);

    .avatar-icon {
      font-size: 24px;
      color: white;
    }
  }

  .header-info {
    .title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .subtitle {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-dot {
      width: 8px;
      height: 8px;
      background: #4ade80;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    .status-text {
      font-size: 14px;
      opacity: 0.9;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 聊天内容区域
.chat-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  background: #f8fafc;
}

.chat-box {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  padding: 30px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

// 消息样式
.message {
  margin-bottom: 25px;
  display: flex;
  align-items: flex-start;
  width: 100%;

  &.user-message {
    justify-content: flex-end;
    margin-left: auto;
    max-width: 85%;

    .message-avatar {
      order: 2;
      margin-left: 12px;
    }

    .message-content {
      order: 1;
      background: linear-gradient(135deg, #0066CC 0%, #4A90E2 100%);
      color: white;
      border-radius: 20px 20px 5px 20px;
    }
  }

  &.assistant-message {
    justify-content: flex-start;
    margin-right: auto;
    max-width: 85%;

    .message-avatar {
      order: 1;
      margin-right: 12px;
    }

    .message-content {
      order: 2;
      background: white;
      color: #374151;
      border-radius: 20px 20px 20px 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
  }
}

.message-avatar {
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;

    &.ai-avatar-small {
      background: linear-gradient(135deg, #0066CC 0%, #4A90E2 100%);
      color: white;
    }

    &.user-avatar-small {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
    }
  }
}

.message-content {
  max-width: 70%;
  padding: 16px 20px;
  word-break: break-word;
  white-space: pre-wrap;
  line-height: 1.6;
  font-size: 15px;
  position: relative;
}



// 输入区域样式
.input-area {
  padding: 25px 30px;
  background: white;
  border-top: 1px solid #e2e8f0;

  .input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    margin-bottom: 12px;
  }

  .message-input {
    flex: 1;

    :deep(.el-textarea__inner) {
      border-radius: 20px;
      border: 2px solid #e2e8f0;
      padding: 12px 20px;
      font-size: 15px;
      line-height: 1.5;
      resize: none;
      transition: all 0.3s ease;

      // 滚动条样式控制
      overflow-y: auto;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 3px;
        transition: background-color 0.3s ease;
      }

      // 当悬停或聚焦时显示滚动条
      &:hover::-webkit-scrollbar-thumb,
      &:focus::-webkit-scrollbar-thumb {
        background: #cbd5e1;
      }

      &:hover::-webkit-scrollbar-thumb:hover,
      &:focus::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }

      // 当聚焦时滚动条颜色更明显
      &:focus::-webkit-scrollbar-thumb {
        background: #6366f1;

        &:hover {
          background: #4f46e5;
        }
      }

      // Firefox 滚动条样式
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;

      &:hover,
      &:focus {
        scrollbar-color: #cbd5e1 transparent;
      }

      &:focus {
        scrollbar-color: #6366f1 transparent;
      }

      &:focus {
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      }

      &::placeholder {
        color: #94a3b8;
      }
    }
  }

  .send-button {
    height: 48px;
    padding: 0 24px;
    border-radius: 24px;
    background: linear-gradient(135deg, #0066CC 0%, #4A90E2 100%);
    border: none;
    font-weight: 600;
    font-size: 15px;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .input-tips {
    display: flex;
    justify-content: center;

    .tip-text {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #94a3b8;
    }
  }
}

// 加载动画
.loading-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.typing-indicator {
  display: flex;
  gap: 4px;

  span {
    width: 8px;
    height: 8px;
    background: #94a3b8;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

.typing-text {
  color: #64748b;
  font-size: 14px;
  font-style: italic;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 快速问题样式
.quick-questions {
  .question-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #4f46e5;
    font-size: 16px;
  }

  .questions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
  }

  .question-card {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid transparent;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: #475569;

    .question-icon {
      color: #6366f1;
      font-size: 16px;
    }

    &:hover {
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      border-color: #6366f1;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
    }
  }
}

.message-text {
  line-height: 1.6;
}

// 响应式设计
@media (max-width: 768px) {
  .chat-container {
    padding: 10px;
    height: calc(100vh - 60px);
  }

  .chat-wrapper {
    border-radius: 15px;
  }

  .chat-header {
    padding: 15px 20px;

    .ai-avatar {
      width: 40px;
      height: 40px;

      .avatar-icon {
        font-size: 20px;
      }
    }

    .header-info .title {
      font-size: 18px;
    }
  }

  .chat-box {
    padding: 20px 15px;
  }

  .message-content {
    max-width: 85%;
    padding: 12px 16px;
    font-size: 14px;
  }

  .input-area {
    padding: 20px 15px;

    .send-button {
      height: 44px;
      padding: 0 20px;
      font-size: 14px;
    }
  }

  .questions-grid {
    grid-template-columns: 1fr;
  }
}
</style>