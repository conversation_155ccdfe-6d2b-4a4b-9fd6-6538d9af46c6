// 包声明：定义当前类所属的包路径
package com.dkd.framework.web.service;

// 导入Java集合框架中的HashMap类，用于存储键值对
import java.util.HashMap;
// 导入Java集合框架中的Map接口，用于存储键值对
import java.util.Map;
// 导入Java并发包中的时间单位枚举
import java.util.concurrent.TimeUnit;
// 导入Java Servlet API中的HTTP请求接口
import javax.servlet.http.HttpServletRequest;
// 导入SLF4J日志框架的Logger接口
import org.slf4j.Logger;
// 导入SLF4J日志框架的LoggerFactory工厂类
import org.slf4j.LoggerFactory;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring配置值注解
import org.springframework.beans.factory.annotation.Value;
// 导入Spring组件注解
import org.springframework.stereotype.Component;
// 导入缓存常量类
import com.dkd.common.constant.CacheConstants;
// 导入系统常量类
import com.dkd.common.constant.Constants;
// 导入登录用户模型类
import com.dkd.common.core.domain.model.LoginUser;
// 导入Redis缓存操作类
import com.dkd.common.core.redis.RedisCache;
// 导入Servlet工具类
import com.dkd.common.utils.ServletUtils;
// 导入字符串工具类
import com.dkd.common.utils.StringUtils;
// 导入地址工具类，用于根据IP获取地理位置
import com.dkd.common.utils.ip.AddressUtils;
// 导入IP工具类，用于获取客户端IP地址
import com.dkd.common.utils.ip.IpUtils;
// 导入ID工具类，用于生成UUID
import com.dkd.common.utils.uuid.IdUtils;
// 导入用户代理工具类，用于解析浏览器和操作系统信息
import eu.bitwalker.useragentutils.UserAgent;
// 导入JWT声明接口
import io.jsonwebtoken.Claims;
// 导入JWT工具类
import io.jsonwebtoken.Jwts;
// 导入JWT签名算法枚举
import io.jsonwebtoken.SignatureAlgorithm;

/**
 * token验证处理
 * 这个服务类负责处理JWT令牌的完整生命周期管理，包括：
 * 1. 令牌创建和生成
 * 2. 令牌解析和验证
 * 3. 用户信息缓存管理
 * 4. 令牌刷新机制
 * 5. 用户代理信息设置
 * 6. 令牌删除和清理
 *
 * <AUTHOR>
 */
@Component // 将此类标记为Spring组件，由Spring容器管理
public class TokenService
{
    // 创建日志记录器，用于记录Token服务的操作日志
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    // 从配置文件中注入令牌自定义标识，通常是HTTP请求头名称（如：Authorization）
    @Value("${token.header}")
    private String header;

    // 从配置文件中注入令牌签名密钥，用于JWT的签名和验证
    @Value("${token.secret}")
    private String secret;

    // 从配置文件中注入令牌有效期（分钟），默认30分钟
    @Value("${token.expireTime}")
    private int expireTime;

    // 毫秒常量：1秒 = 1000毫秒
    protected static final long MILLIS_SECOND = 1000;

    // 毫秒常量：1分钟 = 60秒 = 60000毫秒
    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    // 毫秒常量：20分钟 = 1200000毫秒，用于令牌自动刷新的时间阈值
    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;

    // 注入Redis缓存服务，用于存储用户登录信息
    @Autowired
    private RedisCache redisCache;

    /**
     * 获取用户身份信息
     * 根据HTTP请求中的JWT令牌获取对应的登录用户信息
     *
     * @param request HTTP请求对象，包含令牌信息
     * @return 用户信息，如果令牌无效或不存在则返回null
     */
    public LoginUser getLoginUser(HttpServletRequest request)
    {
        // 从HTTP请求中获取JWT令牌
        String token = getToken(request);
        // 检查令牌是否不为空
        if (StringUtils.isNotEmpty(token))
        {
            try
            {
                // 解析JWT令牌，获取声明信息
                Claims claims = parseToken(token);
                // 从声明中获取用户唯一标识（UUID）
                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
                // 构造Redis缓存键
                String userKey = getTokenKey(uuid);
                // 从Redis缓存中获取完整的用户登录信息
                LoginUser user = redisCache.getCacheObject(userKey);
                // 返回用户信息
                return user;
            }
            catch (Exception e) // 捕获令牌解析过程中的异常
            {
                // 记录获取用户信息异常的错误日志
                log.error("获取用户信息异常'{}'", e.getMessage());
            }
        }
        // 令牌为空或解析失败，返回null
        return null;
    }

    /**
     * 设置用户身份信息
     * 更新用户的登录信息到Redis缓存中，通常用于刷新令牌有效期
     *
     * @param loginUser 登录用户对象，包含用户信息和令牌
     */
    public void setLoginUser(LoginUser loginUser)
    {
        // 检查登录用户对象和令牌是否都不为空
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken()))
        {
            // 刷新用户令牌，更新缓存中的用户信息和有效期
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户身份信息
     * 从Redis缓存中删除指定令牌对应的用户登录信息，通常用于用户退出登录
     *
     * @param token 用户令牌，用于构造缓存键
     */
    public void delLoginUser(String token)
    {
        // 检查令牌是否不为空
        if (StringUtils.isNotEmpty(token))
        {
            // 根据令牌构造Redis缓存键
            String userKey = getTokenKey(token);
            // 从Redis缓存中删除用户登录信息
            redisCache.deleteObject(userKey);
        }
    }

    /**
     * 创建令牌
     * 为登录用户生成JWT令牌，这是用户登录成功后的核心操作
     *
     * @param loginUser 用户信息，包含用户基本信息和权限
     * @return 令牌，返回生成的JWT令牌字符串
     */
    public String createToken(LoginUser loginUser)
    {
        // 生成一个快速UUID作为令牌的唯一标识
        String token = IdUtils.fastUUID();
        // 将生成的令牌设置到登录用户对象中
        loginUser.setToken(token);
        // 设置用户代理信息（IP地址、浏览器、操作系统等）
        setUserAgent(loginUser);
        // 刷新令牌，将用户信息存储到Redis缓存中
        refreshToken(loginUser);

        // 创建JWT声明Map，用于存储令牌相关信息
        Map<String, Object> claims = new HashMap<>();
        // 将令牌UUID放入声明中，作为用户标识
        claims.put(Constants.LOGIN_USER_KEY, token);
        // 调用私有方法生成最终的JWT令牌字符串
        return createToken(claims);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     * 这是令牌自动续期机制，防止用户在使用过程中突然过期
     *
     * @param loginUser 登录用户对象，包含过期时间信息
     */
    public void verifyToken(LoginUser loginUser)
    {
        // 获取令牌的过期时间（毫秒时间戳）
        long expireTime = loginUser.getExpireTime();
        // 获取当前系统时间（毫秒时间戳）
        long currentTime = System.currentTimeMillis();
        // 如果令牌剩余有效期不足20分钟，则自动刷新
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN)
        {
            // 刷新令牌，延长有效期
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     * 更新用户的登录时间和过期时间，并将最新信息存储到Redis缓存中
     *
     * @param loginUser 登录信息，包含用户令牌和基本信息
     */
    public void refreshToken(LoginUser loginUser)
    {
        // 设置登录时间为当前系统时间
        loginUser.setLoginTime(System.currentTimeMillis());
        // 计算并设置过期时间：登录时间 + 配置的有效期（分钟转毫秒）
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据令牌UUID构造Redis缓存键
        String userKey = getTokenKey(loginUser.getToken());
        // 将用户登录信息存储到Redis缓存中，设置过期时间
        redisCache.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);
    }

    /**
     * 设置用户代理信息
     * 解析HTTP请求中的User-Agent信息，获取用户的浏览器、操作系统、IP地址等信息
     *
     * @param loginUser 登录信息，用于存储解析出的用户代理信息
     */
    public void setUserAgent(LoginUser loginUser)
    {
        // 从HTTP请求头中获取User-Agent字符串并解析
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        // 获取客户端的真实IP地址
        String ip = IpUtils.getIpAddr();
        // 设置用户的IP地址
        loginUser.setIpaddr(ip);
        // 根据IP地址获取地理位置信息并设置
        loginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
        // 设置用户使用的浏览器名称
        loginUser.setBrowser(userAgent.getBrowser().getName());
        // 设置用户使用的操作系统名称
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    /**
     * 从数据声明生成令牌
     * 这是一个私有方法，使用JWT库根据声明信息生成最终的JWT令牌字符串
     *
     * @param claims 数据声明，包含要编码到JWT中的信息
     * @return 令牌，返回签名后的JWT令牌字符串
     */
    private String createToken(Map<String, Object> claims)
    {
        // 使用JWT构建器创建令牌
        String token = Jwts.builder()
                .setClaims(claims) // 设置声明信息
                .signWith(SignatureAlgorithm.HS512, secret).compact(); // 使用HS512算法和密钥签名并压缩
        // 返回生成的JWT令牌字符串
        return token;
    }

    /**
     * 从令牌中获取数据声明
     * 这是一个私有方法，解析JWT令牌并提取其中的声明信息
     *
     * @param token 令牌，要解析的JWT令牌字符串
     * @return 数据声明，返回JWT中包含的声明信息
     */
    private Claims parseToken(String token)
    {
        // 使用JWT解析器解析令牌
        return Jwts.parser()
                .setSigningKey(secret) // 设置签名密钥
                .parseClaimsJws(token) // 解析JWT令牌
                .getBody(); // 获取声明主体
    }

    /**
     * 从令牌中获取用户名
     * 解析JWT令牌并提取其中的用户名信息（主题字段）
     *
     * @param token 令牌，要解析的JWT令牌字符串
     * @return 用户名，返回令牌中存储的用户名
     */
    public String getUsernameFromToken(String token)
    {
        // 解析令牌获取声明信息
        Claims claims = parseToken(token);
        // 返回声明中的主题（通常是用户名）
        return claims.getSubject();
    }

    /**
     * 获取请求token
     * 从HTTP请求头中提取JWT令牌，并去除Bearer前缀
     *
     * @param request HTTP请求对象，包含请求头信息
     * @return token 返回提取并处理后的JWT令牌字符串
     */
    private String getToken(HttpServletRequest request)
    {
        // 从HTTP请求头中获取令牌，请求头名称由配置文件中的header属性指定（通常是Authorization）
        String token = request.getHeader(header);
        // 检查令牌是否不为空且以指定前缀开头（通常是"Bearer "）
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX))
        {
            // 去除令牌前缀，提取纯净的JWT令牌字符串
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        // 返回处理后的令牌字符串
        return token;
    }

    /**
     * 获取令牌缓存键
     * 根据令牌UUID生成在Redis中存储用户信息的缓存键
     *
     * @param uuid 令牌的唯一标识符
     * @return 返回完整的Redis缓存键，格式为：login_tokens: + uuid
     */
    private String getTokenKey(String uuid)
    {
        // 拼接缓存键前缀和UUID，生成完整的Redis缓存键
        return CacheConstants.LOGIN_TOKEN_KEY + uuid;
    }
} // 类结束标记
