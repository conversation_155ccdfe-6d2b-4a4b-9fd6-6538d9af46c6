// 包声明：定义当前类所属的包路径
package com.dkd.framework.web.service;

// 导入Java注解Resource，用于依赖注入
import javax.annotation.Resource;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring Security认证管理器
import org.springframework.security.authentication.AuthenticationManager;
// 导入Spring Security认证失败异常
import org.springframework.security.authentication.BadCredentialsException;
// 导入Spring Security用户名密码认证令牌
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
// 导入Spring Security认证接口
import org.springframework.security.core.Authentication;
// 导入Spring组件注解
import org.springframework.stereotype.Component;
// 导入缓存常量类
import com.dkd.common.constant.CacheConstants;
// 导入系统常量类
import com.dkd.common.constant.Constants;
// 导入用户常量类
import com.dkd.common.constant.UserConstants;
// 导入系统用户实体类
import com.dkd.common.core.domain.entity.SysUser;
// 导入登录用户模型类
import com.dkd.common.core.domain.model.LoginUser;
// 导入Redis缓存操作类
import com.dkd.common.core.redis.RedisCache;
// 导入服务异常类
import com.dkd.common.exception.ServiceException;
// 导入黑名单异常类
import com.dkd.common.exception.user.BlackListException;
// 导入验证码异常类
import com.dkd.common.exception.user.CaptchaException;
// 导入验证码过期异常类
import com.dkd.common.exception.user.CaptchaExpireException;
// 导入用户不存在异常类
import com.dkd.common.exception.user.UserNotExistsException;
// 导入用户密码不匹配异常类
import com.dkd.common.exception.user.UserPasswordNotMatchException;
// 导入日期工具类
import com.dkd.common.utils.DateUtils;
// 导入消息工具类，用于国际化消息处理
import com.dkd.common.utils.MessageUtils;
// 导入字符串工具类
import com.dkd.common.utils.StringUtils;
// 导入IP工具类
import com.dkd.common.utils.ip.IpUtils;
// 导入异步管理器
import com.dkd.framework.manager.AsyncManager;
// 导入异步工厂类
import com.dkd.framework.manager.factory.AsyncFactory;
// 导入认证上下文持有者
import com.dkd.framework.security.context.AuthenticationContextHolder;
// 导入系统配置服务接口
import com.dkd.system.service.ISysConfigService;
// 导入系统用户服务接口
import com.dkd.system.service.ISysUserService;

/**
 * 登录校验方法
 * 这个服务类负责处理用户登录的完整流程，包括：
 * 1. 验证码校验
 * 2. 用户名密码验证
 * 3. 登录前置检查（黑名单、参数格式等）
 * 4. 生成登录令牌
 * 5. 记录登录信息
 *
 * <AUTHOR>
 */
@Component // 将此类标记为Spring组件，由Spring容器管理
public class SysLoginService
{
    // 注入Token服务，用于生成和管理用户令牌
    @Autowired
    private TokenService tokenService;

    // 注入Spring Security认证管理器，用于用户身份验证
    @Resource
    private AuthenticationManager authenticationManager;

    // 注入Redis缓存服务，用于验证码等临时数据存储
    @Autowired
    private RedisCache redisCache;

    // 注入用户服务，用于用户相关操作
    @Autowired
    private ISysUserService userService;

    // 注入配置服务，用于获取系统配置参数
    @Autowired
    private ISysConfigService configService;

    /**
     * 登录验证
     * 这是系统登录的核心方法，处理完整的用户登录流程
     *
     * @param username 用户名，用户输入的登录账号
     * @param password 密码，用户输入的登录密码
     * @param code 验证码，用户输入的图形验证码
     * @param uuid 唯一标识，验证码对应的唯一标识符
     * @return 结果，返回生成的JWT令牌字符串
     */
    public String login(String username, String password, String code, String uuid)
    {
        // 第一步：验证码校验，检查用户输入的验证码是否正确
        validateCaptcha(username, code, uuid);
        // 第二步：登录前置校验，检查用户名密码格式、IP黑名单等
        loginPreCheck(username, password);
        // 第三步：用户验证，声明认证对象变量
        Authentication authentication = null;
        try
        {
            // 创建用户名密码认证令牌，封装用户输入的凭据
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            // 将认证令牌设置到认证上下文中
            AuthenticationContextHolder.setContext(authenticationToken);
            // 调用认证管理器进行身份验证，该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e) // 捕获认证过程中的所有异常
        {
            // 判断是否为认证凭据错误异常（用户名或密码错误）
            if (e instanceof BadCredentialsException)
            {
                // 异步记录登录失败日志，记录密码不匹配错误
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                // 抛出用户密码不匹配异常
                throw new UserPasswordNotMatchException();
            }
            else // 其他类型的异常
            {
                // 异步记录登录失败日志，记录具体的异常信息
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                // 抛出服务异常，包装原始异常信息
                throw new ServiceException(e.getMessage());
            }
        }
        finally // 无论认证成功还是失败，都要清理认证上下文
        {
            // 清除认证上下文，防止内存泄漏
            AuthenticationContextHolder.clearContext();
        }
        // 第四步：认证成功后的处理
        // 异步记录登录成功日志
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        // 从认证结果中获取登录用户信息
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        // 记录用户登录信息（IP地址、登录时间等）
        recordLoginInfo(loginUser.getUserId());
        // 第五步：生成并返回JWT令牌
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     * 验证用户输入的图形验证码是否正确
     *
     * @param username 用户名，用于记录日志
     * @param code 验证码，用户输入的验证码
     * @param uuid 唯一标识，验证码对应的唯一标识符
     * @return 结果，无返回值，验证失败会抛出异常
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        // 从系统配置中获取验证码是否启用的设置
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        // 如果启用了验证码功能
        if (captchaEnabled)
        {
            // 构造验证码在Redis中的缓存键，格式为：captcha_codes: + uuid
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            // 从Redis缓存中获取存储的验证码
            String captcha = redisCache.getCacheObject(verifyKey);
            // 立即删除缓存中的验证码，确保验证码只能使用一次
            redisCache.deleteObject(verifyKey);
            // 如果缓存中没有找到验证码，说明验证码已过期或不存在
            if (captcha == null)
            {
                // 异步记录登录失败日志，记录验证码过期错误
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                // 抛出验证码过期异常
                throw new CaptchaExpireException();
            }
            // 比较用户输入的验证码与缓存中的验证码（忽略大小写）
            if (!code.equalsIgnoreCase(captcha))
            {
                // 异步记录登录失败日志，记录验证码错误
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                // 抛出验证码错误异常
                throw new CaptchaException();
            }
        }
        // 如果验证码功能未启用或验证通过，方法正常结束
    }

    /**
     * 登录前置校验
     * 在进行身份验证之前，对用户输入的参数进行基础校验
     * @param username 用户名，用户输入的登录账号
     * @param password 用户密码，用户输入的登录密码
     */
    public void loginPreCheck(String username, String password)
    {
        // 第一项检查：用户名或密码为空的情况
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            // 异步记录登录失败日志，记录参数为空错误
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            // 抛出用户不存在异常
            throw new UserNotExistsException();
        }
        // 第二项检查：密码长度是否在指定范围内
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            // 异步记录登录失败日志，记录密码格式错误
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            // 抛出用户密码不匹配异常
            throw new UserPasswordNotMatchException();
        }
        // 第三项检查：用户名长度是否在指定范围内
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            // 异步记录登录失败日志，记录用户名格式错误
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            // 抛出用户密码不匹配异常
            throw new UserPasswordNotMatchException();
        }
        // 第四项检查：IP黑名单校验
        // 从系统配置中获取登录IP黑名单
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        // 检查当前请求的IP地址是否在黑名单中
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            // 异步记录登录失败日志，记录IP被阻止错误
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            // 抛出黑名单异常
            throw new BlackListException();
        }
        // 所有前置检查通过，方法正常结束
    }

    /**
     * 记录登录信息
     * 用户登录成功后，更新用户的登录相关信息
     *
     * @param userId 用户ID，登录成功的用户标识
     */
    public void recordLoginInfo(Long userId)
    {
        // 创建系统用户对象，用于更新登录信息
        SysUser sysUser = new SysUser();
        // 设置用户ID
        sysUser.setUserId(userId);
        // 设置登录IP地址，获取当前请求的真实IP
        sysUser.setLoginIp(IpUtils.getIpAddr());
        // 设置登录时间，获取当前系统时间
        sysUser.setLoginDate(DateUtils.getNowDate());
        // 调用用户服务更新用户档案信息
        userService.updateUserProfile(sysUser);
    }
} // 类结束标记
