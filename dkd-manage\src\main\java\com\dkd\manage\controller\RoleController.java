package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Role;
import com.dkd.manage.service.IRoleService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 工单角色Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
// 定义REST控制器，处理工单角色管理相关的HTTP请求
@RestController
@RequestMapping("/manage/role")
public class RoleController extends BaseController
{
    // 注入工单角色业务层接口
    @Autowired
    private IRoleService roleService;

    /**
     * 查询工单角色列表接口
     * 需要'manage:role:list'权限
     * 请求路径：GET /manage/role/list
     */
    @PreAuthorize("@ss.hasPermi('manage:role:list')")
    @GetMapping("/list")
    public TableDataInfo list(Role role)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询工单角色数据
        List<Role> list = roleService.selectRoleList(role);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出工单角色列表接口
     * 需要'manage:role:export'权限
     * 请求路径：POST /manage/role/export
     */
    @PreAuthorize("@ss.hasPermi('manage:role:export')")
    @Log(title = "工单角色", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Role role)
    {
        // 调用服务层方法查询需要导出的工单角色数据
        List<Role> list = roleService.selectRoleList(role);
        // 创建Excel工具类实例
        ExcelUtil<Role> util = new ExcelUtil<Role>(Role.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "工单角色数据");
    }

    /**
     * 获取工单角色详细信息接口
     * 需要'manage:role:query'权限
     * 请求路径：GET /manage/role/{roleId}
     */
    @PreAuthorize("@ss.hasPermi('manage:role:query')")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable("roleId") Long roleId)
    {
        // 调用服务层方法获取工单角色详情
        return success(roleService.selectRoleByRoleId(roleId));
    }

    /**
     * 新增工单角色接口
     * 需要'manage:role:add'权限
     * 请求路径：POST /manage/role
     */
    @PreAuthorize("@ss.hasPermi('manage:role:add')")
    @Log(title = "工单角色", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Role role)
    {
        // 调用服务层方法新增工单角色
        return toAjax(roleService.insertRole(role));
    }

    /**
     * 修改工单角色接口
     * 需要'manage:role:edit'权限
     * 请求路径：PUT /manage/role
     */
    @PreAuthorize("@ss.hasPermi('manage:role:edit')")
    @Log(title = "工单角色", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Role role)
    {
        // 调用服务层方法修改工单角色信息
        return toAjax(roleService.updateRole(role));
    }

    /**
     * 删除工单角色接口
     * 需要'manage:role:remove'权限
     * 请求路径：DELETE /manage/role/{roleIds}
     */
    @PreAuthorize("@ss.hasPermi('manage:role:remove')")
    @Log(title = "工单角色", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public AjaxResult remove(@PathVariable Long[] roleIds)
    {
        // 调用服务层方法删除指定ID的工单角色
        return toAjax(roleService.deleteRoleByRoleIds(roleIds));
    }
}



// @RestController
// @RequestMapping("/manage/role")
// public class RoleController extends BaseController
// {
//     @Autowired
//     private IRoleService roleService;

//     /**
//      * 查询工单角色列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:role:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Role role)
//     {
//         startPage();
//         List<Role> list = roleService.selectRoleList(role);
//         return getDataTable(list);
//     }

//     /**
//      * 导出工单角色列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:role:export')")
//     @Log(title = "工单角色", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Role role)
//     {
//         List<Role> list = roleService.selectRoleList(role);
//         ExcelUtil<Role> util = new ExcelUtil<Role>(Role.class);
//         util.exportExcel(response, list, "工单角色数据");
//     }

//     /**
//      * 获取工单角色详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:role:query')")
//     @GetMapping(value = "/{roleId}")
//     public AjaxResult getInfo(@PathVariable("roleId") Long roleId)
//     {
//         return success(roleService.selectRoleByRoleId(roleId));
//     }

//     /**
//      * 新增工单角色
//      */
//     @PreAuthorize("@ss.hasPermi('manage:role:add')")
//     @Log(title = "工单角色", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Role role)
//     {
//         return toAjax(roleService.insertRole(role));
//     }

//     /**
//      * 修改工单角色
//      */
//     @PreAuthorize("@ss.hasPermi('manage:role:edit')")
//     @Log(title = "工单角色", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Role role)
//     {
//         return toAjax(roleService.updateRole(role));
//     }

//     /**
//      * 删除工单角色
//      */
//     @PreAuthorize("@ss.hasPermi('manage:role:remove')")
//     @Log(title = "工单角色", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{roleIds}")
//     public AjaxResult remove(@PathVariable Long[] roleIds)
//     {
//         return toAjax(roleService.deleteRoleByRoleIds(roleIds));
//     }
// }
