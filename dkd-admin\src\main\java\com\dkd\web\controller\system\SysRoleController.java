package com.dkd.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysDept;
import com.dkd.common.core.domain.entity.SysRole;
import com.dkd.common.core.domain.entity.SysUser;
import com.dkd.common.core.domain.model.LoginUser;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.framework.web.service.SysPermissionService;
import com.dkd.framework.web.service.TokenService;
import com.dkd.system.domain.SysUserRole;
import com.dkd.system.service.ISysDeptService;
import com.dkd.system.service.ISysRoleService;
import com.dkd.system.service.ISysUserService;

/**
 * 角色信息
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块角色管理相关的HTTP请求
@RestController
@RequestMapping("/system/role")
public class SysRoleController extends BaseController
{
    // 注入角色管理业务层接口
    @Autowired
    private ISysRoleService roleService;

    // 注入Token服务类，用于更新缓存用户权限信息
    @Autowired
    private TokenService tokenService;

    // 注入权限校验服务类
    @Autowired
    private SysPermissionService permissionService;

    // 注入用户业务层接口
    @Autowired
    private ISysUserService userService;

    // 注入部门业务层接口
    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取角色列表接口
     * 需要'system:role:list'权限
     * 请求路径：GET /system/role/list
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysRole role)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询角色数据
        List<SysRole> list = roleService.selectRoleList(role);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出角色信息接口
     * 需要'system:role:export'权限
     * 请求路径：POST /system/role/export
     */
    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:role:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRole role)
    {
        // 调用服务层方法查询需要导出的角色数据
        List<SysRole> list = roleService.selectRoleList(role);
        // 创建Excel工具类实例
        ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "角色数据");
    }

    /**
     * 根据角色编号获取详细信息接口
     * 需要'system:role:query'权限
     * 请求路径：GET /system/role/{roleId}
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable Long roleId)
    {
        // 校验角色数据权限范围
        roleService.checkRoleDataScope(roleId);
        // 调用服务层方法获取角色详情
        return success(roleService.selectRoleById(roleId));
    }

    /**
     * 新增角色接口
     * 需要'system:role:add'权限
     * 请求路径：POST /system/role
     */
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysRole role)
    {
        // 校验角色名称是否唯一
        if (!roleService.checkRoleNameUnique(role))
        {
            return error("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        // 校验角色权限标识是否唯一
        else if (!roleService.checkRoleKeyUnique(role))
        {
            return error("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        // 设置创建人
        role.setCreateBy(getUsername());
        // 调用服务层方法新增角色
        return toAjax(roleService.insertRole(role));
    }

    /**
     * 修改保存角色接口
     * 需要'system:role:edit'权限
     * 请求路径：PUT /system/role
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysRole role)
    {
        // 校验角色修改权限
        roleService.checkRoleAllowed(role);
        // 校验角色数据权限范围
        roleService.checkRoleDataScope(role.getRoleId());

        // 校验角色名称是否唯一
        if (!roleService.checkRoleNameUnique(role))
        {
            return error("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        // 校验角色权限标识是否唯一
        else if (!roleService.checkRoleKeyUnique(role))
        {
            return error("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }

        // 设置更新人
        role.setUpdateBy(getUsername());

        // 调用服务层方法修改角色
        if (roleService.updateRole(role) > 0)
        {
            // 更新缓存中的用户权限信息
            LoginUser loginUser = getLoginUser();
            if (StringUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin())
            {
                loginUser.setPermissions(permissionService.getMenuPermission(loginUser.getUser()));
                loginUser.setUser(userService.selectUserByUserName(loginUser.getUser().getUserName()));
                tokenService.setLoginUser(loginUser);
            }
            // 返回操作成功结果
            return success();
        }
        // 返回操作失败结果
        return error("修改角色'" + role.getRoleName() + "'失败，请联系管理员");
    }

    /**
     * 修改角色数据权限接口
     * 需要'system:role:edit'权限
     * 请求路径：PUT /system/role/dataScope
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public AjaxResult dataScope(@RequestBody SysRole role)
    {
        // 校验角色修改权限
        roleService.checkRoleAllowed(role);
        // 校验角色数据权限范围
        roleService.checkRoleDataScope(role.getRoleId());
        // 调用服务层方法授权数据权限
        return toAjax(roleService.authDataScope(role));
    }

    /**
     * 修改角色状态接口
     * 需要'system:role:edit'权限
     * 请求路径：PUT /system/role/changeStatus
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysRole role)
    {
        // 校验角色修改权限
        roleService.checkRoleAllowed(role);
        // 校验角色数据权限范围
        roleService.checkRoleDataScope(role.getRoleId());
        // 设置更新人
        role.setUpdateBy(getUsername());
        // 调用服务层方法修改角色状态
        return toAjax(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色接口
     * 需要'system:role:remove'权限
     * 请求路径：DELETE /system/role/{roleIds}
     */
    @PreAuthorize("@ss.hasPermi('system:role:remove')")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public AjaxResult remove(@PathVariable Long[] roleIds)
    {
        // 调用服务层方法删除指定ID的角色
        return toAjax(roleService.deleteRoleByIds(roleIds));
    }

    /**
     * 获取角色选择框列表接口
     * 需要'system:role:query'权限
     * 请求路径：GET /system/role/optionselect
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        // 调用服务层方法获取所有角色
        return success(roleService.selectRoleAll());
    }

    /**
     * 查询已分配用户角色列表接口
     * 需要'system:role:list'权限
     * 请求路径：GET /system/role/authUser/allocatedList
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/allocatedList")
    public TableDataInfo allocatedList(SysUser user)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询已分配的用户列表
        List<SysUser> list = userService.selectAllocatedList(user);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 查询未分配用户角色列表接口
     * 需要'system:role:list'权限
     * 请求路径：GET /system/role/authUser/unallocatedList
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/unallocatedList")
    public TableDataInfo unallocatedList(SysUser user)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询未分配的用户列表
        List<SysUser> list = userService.selectUnallocatedList(user);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 取消授权用户接口
     * 需要'system:role:edit'权限
     * 请求路径：PUT /system/role/authUser/cancel
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public AjaxResult cancelAuthUser(@RequestBody SysUserRole userRole)
    {
        // 调用服务层方法取消单个用户授权
        return toAjax(roleService.deleteAuthUser(userRole));
    }

    /**
     * 批量取消授权用户接口
     * 需要'system:role:edit'权限
     * 请求路径：PUT /system/role/authUser/cancelAll
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public AjaxResult cancelAuthUserAll(Long roleId, Long[] userIds)
    {
        // 校验角色数据权限范围
        roleService.checkRoleDataScope(roleId);
        // 调用服务层方法批量取消授权
        return toAjax(roleService.deleteAuthUsers(roleId, userIds));
    }

    /**
     * 批量选择用户授权接口
     * 需要'system:role:edit'权限
     * 请求路径：PUT /system/role/authUser/selectAll
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public AjaxResult selectAuthUserAll(Long roleId, Long[] userIds)
    {
        // 校验角色数据权限范围
        roleService.checkRoleDataScope(roleId);
        // 调用服务层方法批量授权
        return toAjax(roleService.insertAuthUsers(roleId, userIds));
    }

    /**
     * 获取对应角色的部门树接口
     * 需要'system:role:query'权限
     * 请求路径：GET /system/role/deptTree/{roleId}
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/deptTree/{roleId}")
    public AjaxResult deptTree(@PathVariable("roleId") Long roleId)
    {
        // 创建成功响应对象
        AjaxResult ajax = AjaxResult.success();
        // 添加已分配的部门ID列表
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        // 添加完整的部门树结构
        ajax.put("depts", deptService.selectDeptTreeList(new SysDept()));
        // 返回响应结果
        return ajax;
    }
}






// @RestController
// @RequestMapping("/system/role")
// public class SysRoleController extends BaseController
// {
//     @Autowired
//     private ISysRoleService roleService;

//     @Autowired
//     private TokenService tokenService;

//     @Autowired
//     private SysPermissionService permissionService;

//     @Autowired
//     private ISysUserService userService;

//     @Autowired
//     private ISysDeptService deptService;

//     /**
//      * 获取角色列表
//      * @param role
//      * @return
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysRole role)
//     {
//         startPage();
//         List<SysRole> list = roleService.selectRoleList(role);
//         return getDataTable(list);
//     }

//     @Log(title = "角色管理", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('system:role:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysRole role)
//     {
//         List<SysRole> list = roleService.selectRoleList(role);
//         ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
//         util.exportExcel(response, list, "角色数据");
//     }

//     /**
//      * 根据角色编号获取详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:query')")
//     @GetMapping(value = "/{roleId}")
//     public AjaxResult getInfo(@PathVariable Long roleId)
//     {
//         roleService.checkRoleDataScope(roleId);
//         return success(roleService.selectRoleById(roleId));
//     }

//     /**
//      * 新增角色
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:add')")
//     @Log(title = "角色管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysRole role)
//     {
//         if (!roleService.checkRoleNameUnique(role))
//         {
//             return error("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
//         }
//         else if (!roleService.checkRoleKeyUnique(role))
//         {
//             return error("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
//         }
//         role.setCreateBy(getUsername());
//         return toAjax(roleService.insertRole(role));

//     }

//     /**
//      * 修改保存角色
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:edit')")
//     @Log(title = "角色管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysRole role)
//     {
//         roleService.checkRoleAllowed(role);
//         roleService.checkRoleDataScope(role.getRoleId());
//         if (!roleService.checkRoleNameUnique(role))
//         {
//             return error("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
//         }
//         else if (!roleService.checkRoleKeyUnique(role))
//         {
//             return error("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
//         }
//         role.setUpdateBy(getUsername());
        
//         if (roleService.updateRole(role) > 0)
//         {
//             // 更新缓存用户权限
//             LoginUser loginUser = getLoginUser();
//             if (StringUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin())
//             {
//                 loginUser.setPermissions(permissionService.getMenuPermission(loginUser.getUser()));
//                 loginUser.setUser(userService.selectUserByUserName(loginUser.getUser().getUserName()));
//                 tokenService.setLoginUser(loginUser);
//             }
//             return success();
//         }
//         return error("修改角色'" + role.getRoleName() + "'失败，请联系管理员");
//     }

//     /**
//      * 修改保存数据权限
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:edit')")
//     @Log(title = "角色管理", businessType = BusinessType.UPDATE)
//     @PutMapping("/dataScope")
//     public AjaxResult dataScope(@RequestBody SysRole role)
//     {
//         roleService.checkRoleAllowed(role);
//         roleService.checkRoleDataScope(role.getRoleId());
//         return toAjax(roleService.authDataScope(role));
//     }

//     /**
//      * 状态修改
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:edit')")
//     @Log(title = "角色管理", businessType = BusinessType.UPDATE)
//     @PutMapping("/changeStatus")
//     public AjaxResult changeStatus(@RequestBody SysRole role)
//     {
//         roleService.checkRoleAllowed(role);
//         roleService.checkRoleDataScope(role.getRoleId());
//         role.setUpdateBy(getUsername());
//         return toAjax(roleService.updateRoleStatus(role));
//     }

//     /**
//      * 删除角色
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:remove')")
//     @Log(title = "角色管理", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{roleIds}")
//     public AjaxResult remove(@PathVariable Long[] roleIds)
//     {
//         return toAjax(roleService.deleteRoleByIds(roleIds));
//     }

//     /**
//      * 获取角色选择框列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:query')")
//     @GetMapping("/optionselect")
//     public AjaxResult optionselect()
//     {
//         return success(roleService.selectRoleAll());
//     }

//     /**
//      * 查询已分配用户角色列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:list')")
//     @GetMapping("/authUser/allocatedList")
//     public TableDataInfo allocatedList(SysUser user)
//     {
//         startPage();
//         List<SysUser> list = userService.selectAllocatedList(user);
//         return getDataTable(list);
//     }

//     /**
//      * 查询未分配用户角色列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:list')")
//     @GetMapping("/authUser/unallocatedList")
//     public TableDataInfo unallocatedList(SysUser user)
//     {
//         startPage();
//         List<SysUser> list = userService.selectUnallocatedList(user);
//         return getDataTable(list);
//     }

//     /**
//      * 取消授权用户
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:edit')")
//     @Log(title = "角色管理", businessType = BusinessType.GRANT)
//     @PutMapping("/authUser/cancel")
//     public AjaxResult cancelAuthUser(@RequestBody SysUserRole userRole)
//     {
//         return toAjax(roleService.deleteAuthUser(userRole));
//     }

//     /**
//      * 批量取消授权用户
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:edit')")
//     @Log(title = "角色管理", businessType = BusinessType.GRANT)
//     @PutMapping("/authUser/cancelAll")
//     public AjaxResult cancelAuthUserAll(Long roleId, Long[] userIds)
//     {
//         return toAjax(roleService.deleteAuthUsers(roleId, userIds));
//     }

//     /**
//      * 批量选择用户授权
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:edit')")
//     @Log(title = "角色管理", businessType = BusinessType.GRANT)
//     @PutMapping("/authUser/selectAll")
//     public AjaxResult selectAuthUserAll(Long roleId, Long[] userIds)
//     {
//         roleService.checkRoleDataScope(roleId);
//         return toAjax(roleService.insertAuthUsers(roleId, userIds));
//     }

//     /**
//      * 获取对应角色部门树列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:role:query')")
//     @GetMapping(value = "/deptTree/{roleId}")
//     public AjaxResult deptTree(@PathVariable("roleId") Long roleId)
//     {
//         AjaxResult ajax = AjaxResult.success();
//         ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
//         ajax.put("depts", deptService.selectDeptTreeList(new SysDept()));
//         return ajax;
//     }
// }
