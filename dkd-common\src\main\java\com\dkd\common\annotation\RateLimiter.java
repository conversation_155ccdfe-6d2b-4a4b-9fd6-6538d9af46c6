// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.Documented; // 用于标记注解是否包含在JavaDoc中
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型
import com.dkd.common.constant.CacheConstants; // 导入缓存常量类
import com.dkd.common.enums.LimitType; // 导入限流类型枚举

/**
 * 限流注解
 * 用于控制接口的访问频率，防止恶意攻击和系统过载
 * 通过AOP切面拦截带有此注解的方法，基于Redis实现分布式限流
 * 支持按IP、用户、全局等不同维度进行限流控制
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD) // 指定注解只能应用于方法
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Documented // 指定注解会被包含在JavaDoc中
public @interface RateLimiter // 定义限流注解接口
{
    /**
     * 限流key
     * 用于在Redis中存储限流计数器的键名
     * 默认使用系统预定义的限流键
     * 可以自定义键名以实现不同接口的独立限流
     *
     * @return 限流键名
     */
    public String key() default CacheConstants.RATE_LIMIT_KEY; // 定义限流键属性，默认值为系统限流键

    /**
     * 限流时间,单位秒
     * 指定限流的时间窗口大小
     * 在指定时间内，访问次数不能超过设定的限制
     * 时间窗口结束后，计数器重置
     *
     * @return 限流时间窗口（秒）
     */
    public int time() default 60; // 定义限流时间属性，默认值为60秒

    /**
     * 限流次数
     * 指定在时间窗口内允许的最大访问次数
     * 超过此次数的请求将被拒绝
     * 需要根据系统性能和业务需求合理设置
     *
     * @return 限流次数
     */
    public int count() default 100; // 定义限流次数属性，默认值为100次

    /**
     * 限流类型
     * 指定限流的维度和策略
     * DEFAULT：默认限流，通常按接口限流
     * IP：按IP地址限流，防止单个IP恶意访问
     * USER：按用户限流，防止单个用户过度使用
     *
     * @return 限流类型枚举
     */
    public LimitType limitType() default LimitType.DEFAULT; // 定义限流类型属性，默认值为默认类型
}
