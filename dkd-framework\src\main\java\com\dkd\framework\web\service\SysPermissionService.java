// 包声明：定义当前类所属的包路径
package com.dkd.framework.web.service;

// 导入Java集合框架中的HashSet类，用于存储不重复的权限集合
import java.util.HashSet;
// 导入Java集合框架中的List接口，用于存储角色列表
import java.util.List;
// 导入Java集合框架中的Set接口，用于存储权限集合
import java.util.Set;
// 导入Spring自动装配注解
import org.springframework.beans.factory.annotation.Autowired;
// 导入Spring组件注解
import org.springframework.stereotype.Component;
// 导入Spring工具类，用于判断集合是否为空
import org.springframework.util.CollectionUtils;
// 导入系统角色实体类
import com.dkd.common.core.domain.entity.SysRole;
// 导入系统用户实体类
import com.dkd.common.core.domain.entity.SysUser;
// 导入系统菜单服务接口
import com.dkd.system.service.ISysMenuService;
// 导入系统角色服务接口
import com.dkd.system.service.ISysRoleService;

/**
 * 用户权限处理
 * 这个服务类负责处理用户权限相关的业务逻辑，包括：
 * 1. 获取用户的角色权限
 * 2. 获取用户的菜单权限
 * 3. 处理管理员特殊权限
 * 4. 处理多角色权限合并
 *
 * <AUTHOR>
 */
@Component // 将此类标记为Spring组件，由Spring容器管理
public class SysPermissionService
{
    // 注入角色服务，用于获取角色相关信息
    @Autowired
    private ISysRoleService roleService;

    // 注入菜单服务，用于获取菜单权限信息
    @Autowired
    private ISysMenuService menuService;

    /**
     * 获取角色数据权限
     * 根据用户信息获取该用户拥有的所有角色权限标识
     *
     * @param user 用户信息，包含用户ID和管理员标识
     * @return 角色权限信息，返回角色标识的集合
     */
    public Set<String> getRolePermission(SysUser user)
    {
        // 创建角色权限集合，使用HashSet确保权限不重复
        Set<String> roles = new HashSet<String>();
        // 判断是否为管理员用户
        if (user.isAdmin())
        {
            // 管理员拥有所有权限，添加admin角色标识
            roles.add("admin");
        }
        else // 普通用户
        {
            // 通过角色服务根据用户ID查询该用户的所有角色权限，并添加到集合中
            roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
        }
        // 返回角色权限集合
        return roles;
    }

    /**
     * 获取菜单数据权限
     * 根据用户信息获取该用户拥有的所有菜单权限标识
     *
     * @param user 用户信息，包含用户ID、角色列表和管理员标识
     * @return 菜单权限信息，返回权限标识的集合，格式如："system:user:add"
     */
    public Set<String> getMenuPermission(SysUser user)
    {
        // 创建权限集合，使用HashSet确保权限不重复
        Set<String> perms = new HashSet<String>();
        // 判断是否为管理员用户
        if (user.isAdmin())
        {
            // 管理员拥有所有权限，添加超级权限标识
            perms.add("*:*:*"); // 格式：模块:功能:操作，*表示所有
        }
        else // 普通用户
        {
            // 获取用户的角色列表
            List<SysRole> roles = user.getRoles();
            // 检查角色列表是否不为空
            if (!CollectionUtils.isEmpty(roles))
            {
                // 遍历用户的每个角色，获取角色对应的菜单权限
                for (SysRole role : roles)
                {
                    // 通过菜单服务根据角色ID查询该角色的所有菜单权限
                    Set<String> rolePerms = menuService.selectMenuPermsByRoleId(role.getRoleId());
                    // 将权限集合设置到角色对象中，以便数据权限匹配时使用
                    role.setPermissions(rolePerms);
                    // 将该角色的所有权限添加到用户权限集合中
                    perms.addAll(rolePerms);
                }
            }
            else // 用户没有角色，直接根据用户ID获取权限
            {
                // 通过菜单服务根据用户ID直接查询该用户的菜单权限
                perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
            }
        }
        // 返回用户的所有菜单权限集合
        return perms;
    }
} // 类结束标记
