// 包声明：定义当前类所属的包路径，位于安全框架的上下文包中
package com.dkd.framework.security.context;

// 导入Spring Security的认证接口，用于存储用户认证信息
import org.springframework.security.core.Authentication;

/**
 * 身份验证信息上下文持有者
 * 这个工具类使用ThreadLocal技术为每个线程独立存储认证信息，
 * 主要用于在认证过程中临时存储用户的认证令牌，确保线程安全。
 *
 * 主要功能：
 * 1. 线程级别的认证信息存储
 * 2. 认证信息的设置和获取
 * 3. 认证信息的清理，防止内存泄漏
 *
 * <AUTHOR>
 */
public class AuthenticationContextHolder
{
    /**
     * 线程本地变量，用于存储每个线程的认证信息
     * ThreadLocal确保每个线程都有自己独立的Authentication实例，
     * 避免多线程环境下的数据混乱和安全问题
     */
    private static final ThreadLocal<Authentication> contextHolder = new ThreadLocal<>();

    /**
     * 获取当前线程的认证上下文
     * 从ThreadLocal中获取当前线程存储的认证信息
     *
     * @return Authentication 当前线程的认证信息，如果没有设置则返回null
     */
    public static Authentication getContext()
    {
        // 从ThreadLocal中获取当前线程的认证信息
        return contextHolder.get();
    }

    /**
     * 设置当前线程的认证上下文
     * 将认证信息存储到当前线程的ThreadLocal中
     *
     * @param context 要设置的认证信息，包含用户凭据和权限等
     */
    public static void setContext(Authentication context)
    {
        // 将认证信息设置到当前线程的ThreadLocal中
        contextHolder.set(context);
    }

    /**
     * 清除当前线程的认证上下文
     * 从ThreadLocal中移除当前线程的认证信息，防止内存泄漏
     * 通常在认证完成后或请求结束时调用
     */
    public static void clearContext()
    {
        // 从ThreadLocal中移除当前线程的认证信息，释放内存
        contextHolder.remove();
    }
} // 类结束标记
