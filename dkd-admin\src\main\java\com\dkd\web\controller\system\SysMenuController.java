package com.dkd.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.constant.UserConstants;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysMenu;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.StringUtils;
import com.dkd.system.service.ISysMenuService;

/**
 * 菜单信息
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块菜单管理相关的HTTP请求
@RestController
@RequestMapping("/system/menu")
public class SysMenuController extends BaseController
{
    // 注入菜单业务层接口
    @Autowired
    private ISysMenuService menuService;

    /**
     * 获取菜单列表接口
     * 需要'system:menu:list'权限
     * 请求路径：GET /system/menu/list
     */
    @PreAuthorize("@ss.hasPermi('system:menu:list')")
    @GetMapping("/list")
    public AjaxResult list(SysMenu menu)
    {
        // 调用服务层方法查询菜单数据
        List<SysMenu> menus = menuService.selectMenuList(menu, getUserId());
        // 返回查询结果
        return success(menus);
    }

    /**
     * 根据菜单编号获取详细信息接口
     * 需要'system:menu:query'权限
     * 请求路径：GET /system/menu/{menuId}
     */
    @PreAuthorize("@ss.hasPermi('system:menu:query')")
    @GetMapping(value = "/{menuId}")
    public AjaxResult getInfo(@PathVariable Long menuId)
    {
        // 调用服务层方法获取菜单详情
        return success(menuService.selectMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表接口
     * 请求路径：GET /system/menu/treeselect
     */
    @GetMapping("/treeselect")
    public AjaxResult treeselect(SysMenu menu)
    {
        // 查询所有菜单数据
        List<SysMenu> menus = menuService.selectMenuList(menu, getUserId());
        // 构建并返回菜单树结构
        return success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树接口
     * 请求路径：GET /system/menu/roleMenuTreeselect/{roleId}
     */
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId)
    {
        // 查询所有菜单数据
        List<SysMenu> menus = menuService.selectMenuList(getUserId());
        // 创建成功响应对象
        AjaxResult ajax = AjaxResult.success();
        // 添加角色已分配的菜单ID列表
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        // 添加菜单树结构
        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
        // 返回响应结果
        return ajax;
    }

    /**
     * 新增菜单接口
     * 需要'system:menu:add'权限
     * 请求路径：POST /system/menu
     */
    @PreAuthorize("@ss.hasPermi('system:menu:add')")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysMenu menu)
    {
        // 校验菜单名称是否唯一
        if (!menuService.checkMenuNameUnique(menu))
        {
            return error("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        // 校验外链地址格式
        else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
        {
            return error("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }
        // 设置创建人
        menu.setCreateBy(getUsername());
        // 调用服务层方法新增菜单
        return toAjax(menuService.insertMenu(menu));
    }

    /**
     * 修改菜单接口
     * 需要'system:menu:edit'权限
     * 请求路径：PUT /system/menu
     */
    @PreAuthorize("@ss.hasPermi('system:menu:edit')")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysMenu menu)
    {
        // 校验菜单名称是否唯一
        if (!menuService.checkMenuNameUnique(menu))
        {
            return error("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        // 校验外链地址格式
        else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
        {
            return error("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }
        // 判断上级菜单不能选择自己
        else if (menu.getMenuId().equals(menu.getParentId()))
        {
            return error("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        // 设置更新人
        menu.setUpdateBy(getUsername());
        // 调用服务层方法修改菜单
        return toAjax(menuService.updateMenu(menu));
    }

    /**
     * 删除菜单接口
     * 需要'system:menu:remove'权限
     * 请求路径：DELETE /system/menu/{menuId}
     */
    @PreAuthorize("@ss.hasPermi('system:menu:remove')")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    public AjaxResult remove(@PathVariable("menuId") Long menuId)
    {
        // 判断是否存在子菜单
        if (menuService.hasChildByMenuId(menuId))
        {
            return warn("存在子菜单,不允许删除");
        }
        // 判断菜单是否已分配给角色
        if (menuService.checkMenuExistRole(menuId))
        {
            return warn("菜单已分配,不允许删除");
        }
        // 调用服务层方法删除菜单
        return toAjax(menuService.deleteMenuById(menuId));
    }
}





// @RestController
// @RequestMapping("/system/menu")
// public class SysMenuController extends BaseController
// {
//     @Autowired
//     private ISysMenuService menuService;

//     /**
//      * 获取菜单列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:menu:list')")
//     @GetMapping("/list")
//     public AjaxResult list(SysMenu menu)
//     {
//         List<SysMenu> menus = menuService.selectMenuList(menu, getUserId());
//         return success(menus);
//     }

//     /**
//      * 根据菜单编号获取详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('system:menu:query')")
//     @GetMapping(value = "/{menuId}")
//     public AjaxResult getInfo(@PathVariable Long menuId)
//     {
//         return success(menuService.selectMenuById(menuId));
//     }

//     /**
//      * 获取菜单下拉树列表
//      */
//     @GetMapping("/treeselect")
//     public AjaxResult treeselect(SysMenu menu)
//     {
//         List<SysMenu> menus = menuService.selectMenuList(menu, getUserId());
//         return success(menuService.buildMenuTreeSelect(menus));
//     }

//     /**
//      * 加载对应角色菜单列表树
//      */
//     @GetMapping(value = "/roleMenuTreeselect/{roleId}")
//     public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId)
//     {
//         List<SysMenu> menus = menuService.selectMenuList(getUserId());
//         AjaxResult ajax = AjaxResult.success();
//         ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
//         ajax.put("menus", menuService.buildMenuTreeSelect(menus));
//         return ajax;
//     }

//     /**
//      * 新增菜单
//      */
//     @PreAuthorize("@ss.hasPermi('system:menu:add')")
//     @Log(title = "菜单管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysMenu menu)
//     {
//         if (!menuService.checkMenuNameUnique(menu))
//         {
//             return error("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
//         }
//         else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
//         {
//             return error("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
//         }
//         menu.setCreateBy(getUsername());
//         return toAjax(menuService.insertMenu(menu));
//     }

//     /**
//      * 修改菜单
//      */
//     @PreAuthorize("@ss.hasPermi('system:menu:edit')")
//     @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysMenu menu)
//     {
//         if (!menuService.checkMenuNameUnique(menu))
//         {
//             return error("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
//         }
//         else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
//         {
//             return error("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
//         }
//         else if (menu.getMenuId().equals(menu.getParentId()))
//         {
//             return error("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
//         }
//         menu.setUpdateBy(getUsername());
//         return toAjax(menuService.updateMenu(menu));
//     }

//     /**
//      * 删除菜单
//      */
//     @PreAuthorize("@ss.hasPermi('system:menu:remove')")
//     @Log(title = "菜单管理", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{menuId}")
//     public AjaxResult remove(@PathVariable("menuId") Long menuId)
//     {
//         if (menuService.hasChildByMenuId(menuId))
//         {
//             return warn("存在子菜单,不允许删除");
//         }
//         if (menuService.checkMenuExistRole(menuId))
//         {
//             return warn("菜单已分配,不允许删除");
//         }
//         return toAjax(menuService.deleteMenuById(menuId));
//     }
// }