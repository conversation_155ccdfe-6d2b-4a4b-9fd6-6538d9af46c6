# 投放工单完成后自动更新货道库存功能实现说明

## 🎯 **功能需求**

当投放工单完成后，系统需要根据工单详情中的补货数量自动更新对应货道的当前容量：
- **原来的商品数量** + **新增的补货数量** = **新的当前容量**
- 但不能超过货道的最大容量

## 🔧 **实现方案**

### 1. 修改工单完成逻辑

在 `TaskServiceImpl.completeTask()` 方法中，为投放工单和补货工单添加货道库存更新功能：

```java
if (DkdContants.TASK_TYPE_DEPLOY.equals(productTypeId)) {
    // 投放工单：将设备状态改为运营，并更新货道库存
    vendingMachine.setVmStatus(DkdContants.VM_STATUS_RUNNING);
    vendingMachineService.updateVendingMachine(vendingMachine);
    
    // 更新货道库存
    updateChannelCapacity(taskId, task.getInnerCode());

} else if (DkdContants.TASK_TYPE_SUPPLY.equals(productTypeId)) {
    // 补货工单：更新货道库存
    updateChannelCapacity(taskId, task.getInnerCode());
}
```

### 2. 新增货道库存更新方法

添加 `updateChannelCapacity()` 私有方法来处理货道库存更新：

```java
/**
 * 更新货道库存
 * 根据工单详情中的补货数量更新对应货道的当前容量
 *
 * @param taskId 工单ID
 * @param innerCode 设备编号
 */
private void updateChannelCapacity(Long taskId, String innerCode) {
    // 1. 查询工单详情
    TaskDetails taskDetailsParam = new TaskDetails();
    taskDetailsParam.setTaskId(taskId);
    List<TaskDetails> taskDetailsList = taskDetailsService.selectTaskDetailsList(taskDetailsParam);
    
    if (CollUtil.isEmpty(taskDetailsList)) {
        return; // 没有工单详情，直接返回
    }
    
    // 2. 遍历工单详情，更新对应货道的库存
    for (TaskDetails taskDetail : taskDetailsList) {
        try {
            // 3. 根据设备编号和货道编号查询货道信息
            Channel channel = channelService.getChannelInfo(innerCode, taskDetail.getChannelCode());
            if (channel == null) {
                continue; // 货道不存在，跳过
            }
            
            // 4. 计算新的当前容量
            Long currentCapacity = channel.getCurrentCapacity() != null ? channel.getCurrentCapacity() : 0L;
            Long expectCapacity = taskDetail.getExpectCapacity() != null ? taskDetail.getExpectCapacity() : 0L;
            Long maxCapacity = channel.getMaxCapacity() != null ? channel.getMaxCapacity() : 0L;
            
            // 新容量 = 当前容量 + 补货数量，但不能超过最大容量
            Long newCapacity = Math.min(currentCapacity + expectCapacity, maxCapacity);
            
            // 5. 更新货道当前容量
            channel.setCurrentCapacity(newCapacity);
            channel.setLastSupplyTime(DateUtils.getNowDate()); // 更新最后补货时间
            channelService.updateChannel(channel);
            
        } catch (Exception e) {
            // 记录错误但不中断流程
            System.err.println("更新货道库存失败：" + e.getMessage());
        }
    }
}
```

### 3. 扩展货道服务接口

在 `IChannelService` 接口中添加新方法：

```java
/**
 * 根据售货机编号和货道编号查询货道信息
 * @param innerCode 售货机编号
 * @param channelCode 货道编号
 * @return 售货机货道
 */
Channel getChannelInfo(String innerCode, String channelCode);
```

在 `ChannelServiceImpl` 中实现该方法：

```java
@Override
public Channel getChannelInfo(String innerCode, String channelCode) {
    return channelMapper.getChannelInfo(innerCode, channelCode);
}
```

## 📊 **业务流程**

### 投放工单完成流程
```
1. 用户点击"完成工单"
    ↓
2. 系统检查工单状态和类型
    ↓
3. 投放工单处理：
   - 更新设备状态为"运营"
   - 调用updateChannelCapacity()更新货道库存
    ↓
4. 查询工单详情列表
    ↓
5. 遍历每个货道：
   - 查询货道当前信息
   - 计算新容量 = 当前容量 + 补货数量
   - 确保不超过最大容量
   - 更新货道容量和补货时间
    ↓
6. 更新工单状态为"完成"
```

### 补货工单完成流程
```
1. 用户点击"完成工单"
    ↓
2. 系统检查工单状态和类型
    ↓
3. 补货工单处理：
   - 直接调用updateChannelCapacity()更新货道库存
    ↓
4. 后续流程同投放工单
```

## 🔒 **安全特性**

### 1. 容量限制
- 新容量不会超过货道的最大容量
- 使用 `Math.min(currentCapacity + expectCapacity, maxCapacity)` 确保安全

### 2. 异常处理
- 单个货道更新失败不会影响其他货道
- 记录详细的错误日志便于排查问题
- 工单完成流程不会因为库存更新失败而中断

### 3. 数据一致性
- 使用 `@Transactional` 注解确保事务一致性
- 工单状态更新和货道库存更新在同一事务中

## 🧪 **测试场景**

### 1. 正常场景测试
- **投放工单**：创建投放工单 → 添加补货详情 → 完成工单 → 检查货道库存是否正确更新
- **补货工单**：创建补货工单 → 添加补货详情 → 完成工单 → 检查货道库存是否正确更新

### 2. 边界场景测试
- **超出最大容量**：补货数量 + 当前容量 > 最大容量，验证是否限制在最大容量
- **空货道**：当前容量为0的货道，验证补货后容量是否正确
- **满货道**：当前容量已达最大容量，验证补货后容量不变

### 3. 异常场景测试
- **货道不存在**：工单详情中的货道编号不存在，验证是否跳过处理
- **无工单详情**：工单没有详情记录，验证是否正常完成
- **数据库异常**：模拟数据库连接异常，验证事务回滚

## 📈 **预期效果**

### 功能效果
- ✅ 投放工单完成后，货道库存自动更新
- ✅ 补货工单完成后，货道库存自动更新
- ✅ 库存不会超过货道最大容量
- ✅ 最后补货时间自动更新

### 用户体验
- ✅ 无需手动更新货道库存
- ✅ 库存数据实时准确
- ✅ 操作流程简化

### 系统稳定性
- ✅ 异常情况下不影响工单完成
- ✅ 详细的日志记录便于问题排查
- ✅ 事务保证数据一致性

## 🚀 **部署建议**

1. **备份数据**：部署前备份相关数据表
2. **测试验证**：在测试环境充分验证功能
3. **监控日志**：部署后关注相关日志输出
4. **数据检查**：定期检查货道库存数据的准确性

现在当投放工单或补货工单完成时，系统会自动根据工单详情更新对应货道的库存数量！
