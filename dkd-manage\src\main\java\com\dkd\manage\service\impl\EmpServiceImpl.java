package com.dkd.manage.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import javax.validation.Validator;
import com.dkd.common.utils.DateUtils;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.bean.BeanValidators;
import com.dkd.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.manage.mapper.EmpMapper;
import com.dkd.manage.domain.Emp;
import com.dkd.manage.service.IEmpService;

/**
 * 人员列表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class EmpServiceImpl implements IEmpService
{
    private static final Logger log = LoggerFactory.getLogger(EmpServiceImpl.class);

    @Autowired
    private EmpMapper empMapper;

    @Autowired
    protected Validator validator;

    /**
     * 查询人员列表
     * 
     * @param id 人员列表主键
     * @return 人员列表
     */
    @Override
    public Emp selectEmpById(Long id)
    {
        return empMapper.selectEmpById(id);
    }

    /**
     * 查询人员列表列表
     * 
     * @param emp 人员列表
     * @return 人员列表
     */
    @Override
    public List<Emp> selectEmpList(Emp emp)
    {
        return empMapper.selectEmpList(emp);
    }

    /**
     * 新增人员列表
     * 
     * @param emp 人员列表
     * @return 结果
     */
    @Override
    public int insertEmp(Emp emp)
    {
        emp.setCreateTime(DateUtils.getNowDate());
        return empMapper.insertEmp(emp);
    }

    /**
     * 修改人员列表
     * 
     * @param emp 人员列表
     * @return 结果
     */
    @Override
    public int updateEmp(Emp emp)
    {
        emp.setUpdateTime(DateUtils.getNowDate());
        return empMapper.updateEmp(emp);
    }

    /**
     * 批量删除人员列表
     * 
     * @param ids 需要删除的人员列表主键
     * @return 结果
     */
    @Override
    public int deleteEmpByIds(Long[] ids)
    {
        return empMapper.deleteEmpByIds(ids);
    }

    /**
     * 删除人员列表信息
     * 
     * @param id 人员列表主键
     * @return 结果
     */
    @Override
    public int deleteEmpById(Long id)
    {
        return empMapper.deleteEmpById(id);
    }

    /**
     * 获取人员角色分布统计数据
     * 用于生成人员角色分布统计图表
     *
     * @return 人员角色分布统计结果
     */
    @Override
    public Map<String, Object> getEmpRoleStats() {
        // 创建返回结果Map
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询所有人员
            List<Emp> empList = empMapper.selectEmpList(new Emp());

            // 按区域分组统计
            Map<Long, Long> regionCount = empList.stream()
                .filter(emp -> emp.getRegionId() != null)
                .collect(Collectors.groupingBy(Emp::getRegionId, Collectors.counting()));

            // 按角色分组统计
            Map<Long, Long> roleCount = empList.stream()
                .filter(emp -> emp.getRoleId() != null)
                .collect(Collectors.groupingBy(Emp::getRoleId, Collectors.counting()));

            // 构建返回数据
            result.put("regionCount", regionCount);
            result.put("roleCount", roleCount);
            result.put("total", empList.size());

        } catch (Exception e) {
            // 异常处理，返回空数据
            result.put("regionCount", new HashMap<>());
            result.put("roleCount", new HashMap<>());
            result.put("total", 0);
        }

        return result;
    }

    /**
     * 导入员工数据
     *
     * @param empList 员工数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importEmp(List<Emp> empList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(empList) || empList.size() == 0)
        {
            throw new ServiceException("导入员工数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (Emp emp : empList)
        {
            try
            {
                // 验证是否存在这个员工（通过用户名）
                Emp existingEmp = empMapper.selectEmpByUserName(emp.getUserName());
                if (StringUtils.isNull(existingEmp))
                {
                    // 验证数据
                    BeanValidators.validateWithException(validator, emp);
                    emp.setCreateBy(operName);
                    emp.setCreateTime(DateUtils.getNowDate());
                    empMapper.insertEmp(emp);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、员工 " + emp.getUserName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    // 更新现有员工
                    BeanValidators.validateWithException(validator, emp);
                    emp.setId(existingEmp.getId());
                    emp.setUpdateBy(operName);
                    emp.setUpdateTime(DateUtils.getNowDate());
                    empMapper.updateEmp(emp);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、员工 " + emp.getUserName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、员工 " + emp.getUserName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、员工 " + emp.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
