package com.dkd.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.model.RegisterBody;
import com.dkd.common.utils.StringUtils;
import com.dkd.framework.web.service.SysRegisterService;
import com.dkd.system.service.ISysConfigService;

/**
 * 注册验证
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块用户注册相关的HTTP请求
@RestController
public class SysRegisterController extends BaseController
{
    // 注入用户注册业务层接口
    @Autowired
    private SysRegisterService registerService;

    // 注入系统参数配置业务层接口
    @Autowired
    private ISysConfigService configService;

    /**
     * 用户注册接口
     * 请求路径：POST /register
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody RegisterBody user)
    {
        // 校验系统是否开启注册功能
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return error("当前系统没有开启注册功能！");
        }

        // 调用注册服务执行注册逻辑
        String msg = registerService.register(user);

        // 根据返回信息判断注册是否成功
        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }
}



// @RestController
// public class SysRegisterController extends BaseController
// {
//     @Autowired
//     private SysRegisterService registerService;

//     @Autowired
//     private ISysConfigService configService;

//     @PostMapping("/register")
//     public AjaxResult register(@RequestBody RegisterBody user)
//     {
//         if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
//         {
//             return error("当前系统没有开启注册功能！");
//         }
//         String msg = registerService.register(user);
//         return StringUtils.isEmpty(msg) ? success() : error(msg);
//     }
// }
