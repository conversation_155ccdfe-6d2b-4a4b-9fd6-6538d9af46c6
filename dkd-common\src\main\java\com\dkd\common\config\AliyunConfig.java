// 定义包路径
package com.dkd.common.config;

// 导入Lombok注解
import lombok.Data; // 自动生成getter、setter、toString等方法
// 导入Spring Boot配置相关类
import org.springframework.boot.context.properties.ConfigurationProperties; // 配置属性绑定注解
import org.springframework.context.annotation.Configuration; // 配置类注解

/**
 * 阿里云百炼AI服务配置类
 * 用于读取和管理阿里云百炼AI服务的配置参数
 * 通过@ConfigurationProperties注解自动绑定配置文件中的属性
 * 支持API密钥、模型名称、服务端点、区域ID等配置
 */
@Configuration // 标记为Spring配置类，会被Spring容器管理
@ConfigurationProperties(prefix = "aliyun.bailian") // 绑定配置文件中以aliyun.bailian为前缀的属性
@Data // Lombok注解，自动生成getter、setter、toString、equals、hashCode方法
public class AliyunConfig {

    /**
     * API密钥
     * 用于访问阿里云百炼AI服务的认证密钥
     * 需要在阿里云控制台申请获取
     */
    private String apiKey; // API访问密钥

    /**
     * 模型名称
     * 指定要使用的AI模型
     * 例如：qwen-plus、qwen-max等
     */
    private String model; // AI模型名称

    /**
     * 服务端点
     * 阿里云百炼AI服务的访问地址
     * 通常为https://dashscope.aliyuncs.com
     */
    private String endpoint; // 服务端点地址

    /**
     * 区域ID
     * 指定阿里云服务的区域
     * 例如：cn-hangzhou、cn-beijing等
     */
    private String regionId; // 区域标识
}
