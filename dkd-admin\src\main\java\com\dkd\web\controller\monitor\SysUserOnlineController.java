package com.dkd.web.controller.monitor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.constant.CacheConstants;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.model.LoginUser;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.core.redis.RedisCache;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.StringUtils;
import com.dkd.system.domain.SysUserOnline;
import com.dkd.system.service.ISysUserOnlineService;

/**
 * 在线用户监控
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理监控模块在线用户相关的HTTP请求
@RestController
// 设置基础请求路径为"/monitor/online"
@RequestMapping("/monitor/online")
public class SysUserOnlineController extends BaseController
{
    // 注入在线用户业务层接口
    @Autowired
    private ISysUserOnlineService userOnlineService;

    // 注入Redis缓存服务，用于管理用户在线状态
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询在线用户列表接口
     * 需要'monitor:online:list'权限
     * 请求路径：GET /monitor/online/list
     */
    @PreAuthorize("@ss.hasPermi('monitor:online:list')")
    @GetMapping("/list")
    public TableDataInfo list(String ipaddr, String userName)
    {
        // 获取所有登录用户的缓存键
        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        // 初始化在线用户列表
        List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();
        // 遍历所有缓存键获取对应的用户信息
        for (String key : keys)
        {
            // 从缓存中获取登录用户对象
            LoginUser user = redisCache.getCacheObject(key);
            // 根据IP地址和用户名进行筛选
            if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName))
            {
                userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
            }
            else if (StringUtils.isNotEmpty(ipaddr)) // 根据IP地址筛选
            {
                userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user));
            }
            else if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getUser())) // 根据用户名筛选
            {
                userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
            }
            else // 添加所有在线用户
            {
                userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
            }
        }
        // 反转列表以显示最新登录的用户在最前面
        Collections.reverse(userOnlineList);
        // 移除列表中的空值
        userOnlineList.removeAll(Collections.singleton(null));
        // 返回分页数据
        return getDataTable(userOnlineList);
    }

    /**
     * 强制退出用户接口
     * 需要'monitor:online:forceLogout'权限
     * 请求路径：DELETE /monitor/online/{tokenId}
     */
    @PreAuthorize("@ss.hasPermi('monitor:online:forceLogout')")
    @Log(title = "在线用户", businessType = BusinessType.FORCE)
    @DeleteMapping("/{tokenId}")
    public AjaxResult forceLogout(@PathVariable String tokenId)
    {
        // 删除指定token的缓存记录
        redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
        // 返回操作成功结果
        return success();
    }
}





// @RestController
// @RequestMapping("/monitor/online")
// public class SysUserOnlineController extends BaseController
// {
//     @Autowired
//     private ISysUserOnlineService userOnlineService;

//     @Autowired
//     private RedisCache redisCache;

//     @PreAuthorize("@ss.hasPermi('monitor:online:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(String ipaddr, String userName)
//     {
//         Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
//         List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();
//         for (String key : keys)
//         {
//             LoginUser user = redisCache.getCacheObject(key);
//             if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName))
//             {
//                 userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
//             }
//             else if (StringUtils.isNotEmpty(ipaddr))
//             {
//                 userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user));
//             }
//             else if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getUser()))
//             {
//                 userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
//             }
//             else
//             {
//                 userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
//             }
//         }
//         Collections.reverse(userOnlineList);
//         userOnlineList.removeAll(Collections.singleton(null));
//         return getDataTable(userOnlineList);
//     }

//     /**
//      * 强退用户
//      */
//     @PreAuthorize("@ss.hasPermi('monitor:online:forceLogout')")
//     @Log(title = "在线用户", businessType = BusinessType.FORCE)
//     @DeleteMapping("/{tokenId}")
//     public AjaxResult forceLogout(@PathVariable String tokenId)
//     {
//         redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
//         return success();
//     }
// }
