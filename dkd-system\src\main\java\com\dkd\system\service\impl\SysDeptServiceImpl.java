package com.dkd.system.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.common.annotation.DataScope;
import com.dkd.common.constant.UserConstants;
import com.dkd.common.core.domain.TreeSelect;
import com.dkd.common.core.domain.entity.SysDept;
import com.dkd.common.core.domain.entity.SysRole;
import com.dkd.common.core.domain.entity.SysUser;
import com.dkd.common.core.text.Convert;
import com.dkd.common.exception.ServiceException;
import com.dkd.common.utils.SecurityUtils;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.spring.SpringUtils;
import com.dkd.system.mapper.SysDeptMapper;
import com.dkd.system.mapper.SysRoleMapper;
import com.dkd.system.service.ISysDeptService;

/**
 * 部门管理 服务实现
 * 
 * <AUTHOR>
 */
// 定义部门管理服务实现类
@Service
public class SysDeptServiceImpl implements ISysDeptService
{
    // 注入部门信息数据库操作Mapper
    @Autowired
    private SysDeptMapper deptMapper;

    // 注入角色信息数据库操作Mapper，用于关联角色和部门
    @Autowired
    private SysRoleMapper roleMapper;

    /**
     * 查询部门列表数据
     * 使用@DataScope注解进行数据权限控制，别名为"d"
     * 
     * @param dept 查询条件封装对象
     * @return 符合条件的部门列表
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept)
    {
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 查询部门树结构数据
     * 
     * @param dept 查询条件封装对象
     * @return 部门树结构集合
     */
    @Override
    public List<TreeSelect> selectDeptTreeList(SysDept dept)
    {
        // 获取平级部门列表并转换为树形结构
        List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
        return buildDeptTreeSelect(depts);
    }

    /**
     * 构建前端展示需要的树形部门结构
     * 
     * @param depts 扁平化部门列表
     * @return 树形结构部门列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts)
    {
        List<SysDept> returnList = new ArrayList<SysDept>();
        // 提取所有部门ID到临时列表中
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        
        for (SysDept dept : depts)
        {
            // 查找顶级节点（父节点不在当前列表中）
            if (!tempList.contains(dept.getParentId()))
            {
                recursionFn(depts, dept);  // 递归构建子树
                returnList.add(dept);      // 添加顶级节点
            }
        }
        
        // 如果没有找到顶级节点，则返回原始列表
        if (returnList.isEmpty())
        {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端下拉框使用的树形部门结构
     * 
     * @param depts 扁平化部门列表
     * @return 下拉选择用的树形结构
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts)
    {
        // 先构建树形结构
        List<SysDept> deptTrees = buildDeptTree(depts);
        // 转换为下拉选择专用的TreeSelect格式
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询关联的部门列表
     * 
     * @param roleId 角色ID
     * @return 关联的部门ID列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId)
    {
        // 查询角色详情
        SysRole role = roleMapper.selectRoleById(roleId);
        // 查询角色关联的部门列表
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询部门详细信息
     * 
     * @param deptId 部门ID
     * @return 部门实体对象
     */
    @Override
    public SysDept selectDeptById(Long deptId)
    {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 查询指定部门下的正常状态子部门数量
     * 
     * @param deptId 父部门ID
     * @return 正常状态的子部门数量
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId)
    {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 判断部门是否存在子部门
     * 
     * @param deptId 部门ID
     * @return true表示存在子部门，false表示不存在
     */
    @Override
    public boolean hasChildByDeptId(Long deptId)
    {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 检查部门是否关联了用户
     * 
     * @param deptId 部门ID
     * @return true表示有关联用户，false表示无关联
     */
    @Override
    public boolean checkDeptExistUser(Long deptId)
    {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称在同级中是否唯一
     * 
     * @param dept 待校验的部门对象
     * @return 是否唯一标识
     */
    @Override
    public boolean checkDeptNameUnique(SysDept dept)
    {
        // 获取当前部门ID，默认值为-1L
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        
        // 查询相同名称和父部门的记录
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        
        // 如果存在且不是当前记录，说明不唯一
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验当前用户是否有访问该部门的数据权限
     * 
     * @param deptId 部门ID
     */
    @Override
    public void checkDeptDataScope(Long deptId)
    {
        // 如果不是管理员
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            // 查询该部门是否符合当前用户的数据权限范围
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts))
            {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增部门信息
     * 
     * @param dept 待新增的部门对象
     * @return 插入影响行数
     */
    @Override
    public int insertDept(SysDept dept)
    {
        // 查询父部门信息
        SysDept info = deptMapper.selectDeptById(dept.getParentId());
        // 如果父部门非正常状态，不允许新增子部门
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
        {
            throw new ServiceException("部门停用，不允许新增");
        }
        
        // 设置祖先节点路径
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        return deptMapper.insertDept(dept);
    }

    /**
     * 修改部门信息
     * 
     * @param dept 待修改的部门对象
     * @return 修改影响行数
     */
    @Override
    public int updateDept(SysDept dept)
    {
        // 查询新父部门和旧部门信息
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept))
        {
            // 构建新的祖先路径
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            
            dept.setAncestors(newAncestors);
            // 更新所有子部门的祖先路径
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        
        // 执行更新操作
        int result = deptMapper.updateDept(dept);
        
        // 如果该部门是启用状态，则同时启用其所有上级部门
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors()))
        {
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 启用该部门的所有上级部门
     * 
     * @param dept 当前部门对象
     */
    private void updateParentDeptStatusNormal(SysDept dept)
    {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        // 更新指定ID数组的部门状态为正常
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 更新子部门的祖先路径
     * 
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的祖先路径
     * @param oldAncestors 旧的祖先路径
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors)
    {
        // 查询所有直接子部门
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        
        // 更新每个子部门的祖先路径
        for (SysDept child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        
        // 如果有子部门则批量更新
        if (children.size() > 0)
        {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 根据ID删除部门信息
     * 
     * @param deptId 部门ID
     * @return 删除影响行数
     */
    @Override
    public int deleteDeptById(Long deptId)
    {
        return deptMapper.deleteDeptById(deptId);
    }

    /**
     * 递归构造树形结构
     * 
     * @param list 全量部门列表
     * @param t 当前处理的部门节点
     */
    private void recursionFn(List<SysDept> list, SysDept t)
    {
        // 获取当前节点的直接子节点
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        
        // 对每个子节点递归处理
        for (SysDept tChild : childList)
        {
            if (hasChild(list, tChild))  // 判断是否有下一级子节点
            {
                recursionFn(list, tChild);  // 继续递归处理
            }
        }
    }

    /**
     * 获取当前部门的直接子部门列表
     * 
     * @param list 全量部门列表
     * @param t 当前部门节点
     * @return 子部门列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t)
    {
        List<SysDept> tlist = new ArrayList<>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext())
        {
            SysDept n = it.next();
            // 判断是否是当前部门的直接子部门
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断部门是否有子部门
     * 
     * @param list 全量部门列表
     * @param t 当前部门
     * @return true表示有子部门，false表示无
     */
    private boolean hasChild(List<SysDept> list, SysDept t)
    {
        return getChildList(list, t).size() > 0;
    }
}







// @Service
// public class SysDeptServiceImpl implements ISysDeptService
// {
//     @Autowired
//     private SysDeptMapper deptMapper;

//     @Autowired
//     private SysRoleMapper roleMapper;

//     /**
//      * 查询部门管理数据
//      * 
//      * @param dept 部门信息
//      * @return 部门信息集合
//      */
//     @Override
//     @DataScope(deptAlias = "d")
//     public List<SysDept> selectDeptList(SysDept dept)
//     {
//         return deptMapper.selectDeptList(dept);
//     }

//     /**
//      * 查询部门树结构信息
//      * 
//      * @param dept 部门信息
//      * @return 部门树信息集合
//      */
//     @Override
//     public List<TreeSelect> selectDeptTreeList(SysDept dept)
//     {
//         List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
//         return buildDeptTreeSelect(depts);
//     }

//     /**
//      * 构建前端所需要树结构
//      * 
//      * @param depts 部门列表
//      * @return 树结构列表
//      */
//     @Override
//     public List<SysDept> buildDeptTree(List<SysDept> depts)
//     {
//         List<SysDept> returnList = new ArrayList<SysDept>();
//         List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
//         for (SysDept dept : depts)
//         {
//             // 如果是顶级节点, 遍历该父节点的所有子节点
//             if (!tempList.contains(dept.getParentId()))
//             {
//                 recursionFn(depts, dept);
//                 returnList.add(dept);
//             }
//         }
//         if (returnList.isEmpty())
//         {
//             returnList = depts;
//         }
//         return returnList;
//     }

//     /**
//      * 构建前端所需要下拉树结构
//      * 
//      * @param depts 部门列表
//      * @return 下拉树结构列表
//      */
//     @Override
//     public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts)
//     {
//         List<SysDept> deptTrees = buildDeptTree(depts);
//         return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
//     }

//     /**
//      * 根据角色ID查询部门树信息
//      * 
//      * @param roleId 角色ID
//      * @return 选中部门列表
//      */
//     @Override
//     public List<Long> selectDeptListByRoleId(Long roleId)
//     {
//         SysRole role = roleMapper.selectRoleById(roleId);
//         return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
//     }

//     /**
//      * 根据部门ID查询信息
//      * 
//      * @param deptId 部门ID
//      * @return 部门信息
//      */
//     @Override
//     public SysDept selectDeptById(Long deptId)
//     {
//         return deptMapper.selectDeptById(deptId);
//     }

//     /**
//      * 根据ID查询所有子部门（正常状态）
//      * 
//      * @param deptId 部门ID
//      * @return 子部门数
//      */
//     @Override
//     public int selectNormalChildrenDeptById(Long deptId)
//     {
//         return deptMapper.selectNormalChildrenDeptById(deptId);
//     }

//     /**
//      * 是否存在子节点
//      * 
//      * @param deptId 部门ID
//      * @return 结果
//      */
//     @Override
//     public boolean hasChildByDeptId(Long deptId)
//     {
//         int result = deptMapper.hasChildByDeptId(deptId);
//         return result > 0;
//     }

//     /**
//      * 查询部门是否存在用户
//      * 
//      * @param deptId 部门ID
//      * @return 结果 true 存在 false 不存在
//      */
//     @Override
//     public boolean checkDeptExistUser(Long deptId)
//     {
//         int result = deptMapper.checkDeptExistUser(deptId);
//         return result > 0;
//     }

//     /**
//      * 校验部门名称是否唯一
//      * 
//      * @param dept 部门信息
//      * @return 结果
//      */
//     @Override
//     public boolean checkDeptNameUnique(SysDept dept)
//     {
//         Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
//         SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
//         if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue())
//         {
//             return UserConstants.NOT_UNIQUE;
//         }
//         return UserConstants.UNIQUE;
//     }

//     /**
//      * 校验部门是否有数据权限
//      * 
//      * @param deptId 部门id
//      */
//     @Override
//     public void checkDeptDataScope(Long deptId)
//     {
//         if (!SysUser.isAdmin(SecurityUtils.getUserId()))
//         {
//             SysDept dept = new SysDept();
//             dept.setDeptId(deptId);
//             List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
//             if (StringUtils.isEmpty(depts))
//             {
//                 throw new ServiceException("没有权限访问部门数据！");
//             }
//         }
//     }

//     /**
//      * 新增保存部门信息
//      * 
//      * @param dept 部门信息
//      * @return 结果
//      */
//     @Override
//     public int insertDept(SysDept dept)
//     {
//         SysDept info = deptMapper.selectDeptById(dept.getParentId());
//         // 如果父节点不为正常状态,则不允许新增子节点
//         if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
//         {
//             throw new ServiceException("部门停用，不允许新增");
//         }
//         dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
//         return deptMapper.insertDept(dept);
//     }

//     /**
//      * 修改保存部门信息
//      * 
//      * @param dept 部门信息
//      * @return 结果
//      */
//     @Override
//     public int updateDept(SysDept dept)
//     {
//         SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
//         SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
//         if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept))
//         {
//             String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
//             String oldAncestors = oldDept.getAncestors();
//             dept.setAncestors(newAncestors);
//             updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
//         }
//         int result = deptMapper.updateDept(dept);
//         if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
//                 && !StringUtils.equals("0", dept.getAncestors()))
//         {
//             // 如果该部门是启用状态，则启用该部门的所有上级部门
//             updateParentDeptStatusNormal(dept);
//         }
//         return result;
//     }

//     /**
//      * 修改该部门的父级部门状态
//      * 
//      * @param dept 当前部门
//      */
//     private void updateParentDeptStatusNormal(SysDept dept)
//     {
//         String ancestors = dept.getAncestors();
//         Long[] deptIds = Convert.toLongArray(ancestors);
//         deptMapper.updateDeptStatusNormal(deptIds);
//     }

//     /**
//      * 修改子元素关系
//      * 
//      * @param deptId 被修改的部门ID
//      * @param newAncestors 新的父ID集合
//      * @param oldAncestors 旧的父ID集合
//      */
//     public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors)
//     {
//         List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
//         for (SysDept child : children)
//         {
//             child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
//         }
//         if (children.size() > 0)
//         {
//             deptMapper.updateDeptChildren(children);
//         }
//     }

//     /**
//      * 删除部门管理信息
//      * 
//      * @param deptId 部门ID
//      * @return 结果
//      */
//     @Override
//     public int deleteDeptById(Long deptId)
//     {
//         return deptMapper.deleteDeptById(deptId);
//     }

//     /**
//      * 递归列表
//      */
//     private void recursionFn(List<SysDept> list, SysDept t)
//     {
//         // 得到子节点列表
//         List<SysDept> childList = getChildList(list, t);
//         t.setChildren(childList);
//         for (SysDept tChild : childList)
//         {
//             if (hasChild(list, tChild))
//             {
//                 recursionFn(list, tChild);
//             }
//         }
//     }

//     /**
//      * 得到子节点列表
//      */
//     private List<SysDept> getChildList(List<SysDept> list, SysDept t)
//     {
//         List<SysDept> tlist = new ArrayList<SysDept>();
//         Iterator<SysDept> it = list.iterator();
//         while (it.hasNext())
//         {
//             SysDept n = (SysDept) it.next();
//             if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue())
//             {
//                 tlist.add(n);
//             }
//         }
//         return tlist;
//     }

//     /**
//      * 判断是否有子节点
//      */
//     private boolean hasChild(List<SysDept> list, SysDept t)
//     {
//         return getChildList(list, t).size() > 0;
//     }
// }
