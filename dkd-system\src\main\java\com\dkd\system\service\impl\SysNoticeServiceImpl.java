package com.dkd.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.system.domain.SysNotice;
import com.dkd.system.mapper.SysNoticeMapper;
import com.dkd.system.service.ISysNoticeService;

/**
 * 公告 服务层实现
 * 
 * <AUTHOR>
 */
// 定义公告服务实现类
@Service
public class SysNoticeServiceImpl implements ISysNoticeService
{
    // 注入公告信息数据库操作Mapper
    @Autowired
    private SysNoticeMapper noticeMapper;

    /**
     * 根据公告ID查询详细信息
     * 
     * @param noticeId 公告唯一标识
     * @return 公告实体对象
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId)
    {
        return noticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 根据查询条件筛选公告列表
     * 
     * @param notice 查询条件封装对象
     * @return 符合条件的公告列表
     */
    @Override
    public List<SysNotice> selectNoticeList(SysNotice notice)
    {
        return noticeMapper.selectNoticeList(notice);
    }

    /**
     * 新增公告信息到数据库
     * 
     * @param notice 待新增的公告对象
     * @return 插入影响行数
     */
    @Override
    public int insertNotice(SysNotice notice)
    {
        return noticeMapper.insertNotice(notice);
    }

    /**
     * 修改已有的公告信息
     * 
     * @param notice 待更新的公告对象
     * @return 更新影响行数
     */
    @Override
    public int updateNotice(SysNotice notice)
    {
        return noticeMapper.updateNotice(notice);
    }

    /**
     * 根据ID删除单个公告
     * 
     * @param noticeId 需要删除的公告ID
     * @return 删除影响行数
     */
    @Override
    public int deleteNoticeById(Long noticeId)
    {
        return noticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     * 
     * @param noticeIds 需要删除的公告ID数组
     * @return 删除影响行数
     */
    @Override
    public int deleteNoticeByIds(Long[] noticeIds)
    {
        return noticeMapper.deleteNoticeByIds(noticeIds);
    }
}



 
// @Service
// public class SysNoticeServiceImpl implements ISysNoticeService
// {
//     @Autowired
//     private SysNoticeMapper noticeMapper;

//     /**
//      * 查询公告信息
//      * 
//      * @param noticeId 公告ID
//      * @return 公告信息
//      */
//     @Override
//     public SysNotice selectNoticeById(Long noticeId)
//     {
//         return noticeMapper.selectNoticeById(noticeId);
//     }

//     /**
//      * 查询公告列表
//      * 
//      * @param notice 公告信息
//      * @return 公告集合
//      */
//     @Override
//     public List<SysNotice> selectNoticeList(SysNotice notice)
//     {
//         return noticeMapper.selectNoticeList(notice);
//     }

//     /**
//      * 新增公告
//      * 
//      * @param notice 公告信息
//      * @return 结果
//      */
//     @Override
//     public int insertNotice(SysNotice notice)
//     {
//         return noticeMapper.insertNotice(notice);
//     }

//     /**
//      * 修改公告
//      * 
//      * @param notice 公告信息
//      * @return 结果
//      */
//     @Override
//     public int updateNotice(SysNotice notice)
//     {
//         return noticeMapper.updateNotice(notice);
//     }

//     /**
//      * 删除公告对象
//      * 
//      * @param noticeId 公告ID
//      * @return 结果
//      */
//     @Override
//     public int deleteNoticeById(Long noticeId)
//     {
//         return noticeMapper.deleteNoticeById(noticeId);
//     }

//     /**
//      * 批量删除公告信息
//      * 
//      * @param noticeIds 需要删除的公告ID
//      * @return 结果
//      */
//     @Override
//     public int deleteNoticeByIds(Long[] noticeIds)
//     {
//         return noticeMapper.deleteNoticeByIds(noticeIds);
//     }
// }
