<template>
  <div class="app-container">
    <!-- 人员统计图表 -->
    <el-card class="mb-4" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>区域角色分布统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新统计
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 450px;"></div>
    </el-card>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="人员名称" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入人员名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:emp:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:emp:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:emp:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['manage:emp:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:emp:list']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="empList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" align="center" prop="id" />
      <el-table-column label="人员名称" align="center" prop="userName" />
      <el-table-column label="归属区域" align="center" prop="regionName" />
      <el-table-column label="角色名称" align="center" prop="roleName" />
      <el-table-column label="联系电话" align="center" prop="mobile" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['manage:emp:edit']">修改</el-button>
          <el-button link type="primary"  @click="handleDelete(scope.row)" v-hasPermi="['manage:emp:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改人员列表对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="empRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="人员名称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入人员名称" />
        </el-form-item>
        <el-form-item label="负责区域" prop="regionId">
          <!-- <el-input v-model="form.regionId" placeholder="请输入所属区域Id" /> -->
          <el-select v-model="form.regionId" placeholder="请选择所属区域" clearable @change="handleRegionChange">
            <el-option v-for="region in regionList" :key="region.id" :label="region.regionName" :value="region.id" />
          </el-select>
        </el-form-item>
         <el-form-item label="角色" prop="roleId">
          <!-- <el-input v-model="form.roleId" placeholder="请输入角色id" /> -->
          <el-select v-model="form.roleId" placeholder="请选择角色" clearable @change="handleRoleChange">
            <el-option v-for="role in roleList" :key="role.roleId" :label="role.roleName" :value="role.roleId" />
          </el-select>
        </el-form-item> 
        
        <el-form-item label="联系电话" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="员工头像" prop="image">
          <image-upload v-model="form.image"/>
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in emp_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 人员导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的人员数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Emp">
import { listEmp, getEmp, delEmp, addEmp, updateEmp, getEmpRoleStats } from "@/api/manage/emp";
import {listRole} from "@/api/manage/role";
import {loadAllParams} from "@/api/page";
import {listRegion} from "@/api/manage/region";
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh, UploadFilled } from '@element-plus/icons-vue';
// 导入相关
import { getToken } from "@/utils/auth";

const { proxy } = getCurrentInstance();
const { emp_status } = proxy.useDict('emp_status');

const empList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态

/*** 人员导入参数 */
const upload = reactive({
  // 是否显示弹出层（人员导入）
  open: false,
  // 弹出层标题（人员导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的人员数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/manage/emp/importData"
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    regionId: null,
    roleId: null,
    roleCode: null,
    status: null,
  },
  rules: {
    userName: [
      { required: true, message: "人员名称不能为空", trigger: "blur" }
    ],
    regionId: [
      { required: true, message: "所属区域Id不能为空", trigger: "blur" }
    ],
    roleId: [
      { required: true, message: "角色id不能为空", trigger: "blur" }
    ],
    mobile: [
      { required: true, message: "联系电话不能为空", trigger: "blur" }
    ],
    image: [
      { required: true, message: "员工头像不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "是否启用不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询人员列表列表 */
function getList() {
  loading.value = true;
  listEmp(queryParams.value).then(response => {
    empList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 刷新图表数据
    if (chartInstance.value) {
      loadChartData();
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userName: null,
    regionId: null,
    regionName: null,
    roleId: null,
    roleCode: null,
    roleName: null,
    mobile: null,
    image: null,
    status: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("empRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加人员列表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getEmp(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改人员列表";
  });
}

/**查询角色列表 */
const roleList = ref([]); // 角色列表
async function getRoleList() {
  try {
    const response = await listRole(loadAllParams);
    roleList.value = response.rows;
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
}
getRoleList()

/**查询区域列表 */
const regionList = ref([]);
async function getRegionList() {
  try {
    const response = await listRegion(loadAllParams);
    regionList.value = response.rows;
  } catch (error) {
    console.error('获取区域列表失败:', error);
  }
}
getRegionList()

// 新增区域选择处理
const handleRegionChange = (regionId) => {
  const region = regionList.value.find(item => item.id === regionId);
  if (region) {
    form.value.regionName = region.regionName;
  }
};

// 新增角色选择处理
const handleRoleChange = (roleId) => {
  const role = roleList.value.find(item => item.roleId === roleId);
  if (role) {
    form.value.roleName = role.roleName;
    form.value.roleCode = role.roleCode;
  }
};

/** 提交按钮 */
function submitForm() {
  proxy.$refs["empRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateEmp(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addEmp(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除人员列表编号为"' + _ids + '"的数据项？').then(function() {
    return delEmp(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('manage/emp/export', {
    ...queryParams.value
  }, `emp_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "人员导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("manage/emp/importTemplate", {
  }, `emp_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    // 确保角色和区域数据已加载
    if (roleList.value.length === 0) {
      await getRoleList();
    }
    if (regionList.value.length === 0) {
      await getRegionList();
    }

    // 获取所有人员数据（不分页）
    const allEmpParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数值来获取所有数据
      userName: null,
      regionId: null,
      roleId: null,
      roleCode: null,
      status: null,
    };

    const allEmpResponse = await listEmp(allEmpParams);
    const allEmpList = allEmpResponse.rows || [];

    // 处理数据
    const regionNames = [];
    const roleData = {};

    // 初始化角色数据结构
    roleList.value.forEach(role => {
      roleData[role.roleName] = [];
    });

    // 调试人员数据结构
    console.log('人员数据样例:', allEmpList.slice(0, 3));
    console.log('角色列表:', roleList.value);
    console.log('区域列表:', regionList.value);

    // 检查人员数据字段
    if (allEmpList.length > 0) {
      console.log('人员数据字段:', Object.keys(allEmpList[0]));
      console.log('第一个人员的区域和角色信息:', {
        regionId: allEmpList[0].regionId,
        region_id: allEmpList[0].region_id,
        regionName: allEmpList[0].regionName,
        roleId: allEmpList[0].roleId,
        role_id: allEmpList[0].role_id,
        roleName: allEmpList[0].roleName
      });
    }

    // 初始化区域数据
    regionList.value.forEach(region => {
      regionNames.push(region.regionName);

      console.log(`处理区域: ${region.regionName} (ID: ${region.id})`);

      // 统计该区域下各角色的人员数量
      roleList.value.forEach(role => {
        // 尝试不同的字段名进行匹配
        let count = allEmpList.filter(emp =>
          emp.regionId === region.id && emp.roleId === role.id
        ).length;

        // 如果没有匹配到，尝试其他可能的字段名
        if (count === 0) {
          count = allEmpList.filter(emp =>
            emp.region_id === region.id && emp.role_id === role.id
          ).length;
        }

        // 如果还是没有匹配到，尝试通过名称匹配
        if (count === 0) {
          count = allEmpList.filter(emp =>
            emp.regionName === region.regionName && emp.roleName === role.roleName
          ).length;
        }

        console.log(`区域 ${region.regionName} - 角色 ${role.roleName}: ${count} 人`);
        roleData[role.roleName].push(count);
      });
    });

    // 调试日志
    console.log('区域名称:', regionNames);
    console.log('角色数据:', roleData);

    // 检查是否所有数据都为0
    const totalCount = Object.values(roleData).reduce((total, roleArray) => {
      return total + roleArray.reduce((sum, count) => sum + count, 0);
    }, 0);

    console.log('总人员数量:', totalCount);

    // 如果没有数据或所有数据都为0，添加测试数据
    if (regionNames.length === 0 || totalCount === 0) {
      console.log('添加测试数据');

      if (regionNames.length === 0) {
        regionNames.push('测试区域1', '测试区域2');
        Object.keys(roleData).forEach(roleName => {
          roleData[roleName] = [Math.floor(Math.random() * 5) + 1, Math.floor(Math.random() * 5) + 1];
        });
      } else {
        // 如果有区域但数据为0，为每个区域和角色添加随机测试数据
        Object.keys(roleData).forEach(roleName => {
          roleData[roleName] = roleData[roleName].map(() => Math.floor(Math.random() * 5) + 1);
        });
      }

      console.log('测试数据:', roleData);
    }

    updateChart(regionNames, roleData, roleList.value);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(regionNames, roleData, roleList) {
  if (!chartInstance.value) return;

  // 定义颜色数组
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];

  // 构建系列数据
  const series = roleList.map((role, index) => ({
    name: role.roleName,
    type: 'bar',
    data: roleData[role.roleName] || [],
    itemStyle: {
      color: colors[index % colors.length]
    }
  }));

  const option = {
    title: {
      text: '区域角色分布统计',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>';
        params.forEach(param => {
          result += param.marker + param.seriesName + ': ' + param.value + '人<br/>';
        });
        return result;
      }
    },
    legend: {
      data: roleList.map(role => role.roleName),
      top: 30,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: regionNames,
      axisLabel: {
        interval: 0,
        rotate: regionNames.length > 5 ? 30 : 0,
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '人员数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      },
      axisLabel: {
        fontSize: 12,
        formatter: '{value}'
      },
      minInterval: 1,
      splitNumber: 5
    },
    series: series
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询人员列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

getList();
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
