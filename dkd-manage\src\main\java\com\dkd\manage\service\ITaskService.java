package com.dkd.manage.service;

import java.util.List;
import java.util.Map;
import com.dkd.manage.domain.Task;
import com.dkd.manage.domain.TaskDetails;
import com.dkd.manage.domain.dto.TaskDto;
import com.dkd.manage.domain.vo.TaskVo;

/**
 * 工单Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ITaskService 
{
    /**
     * 查询工单
     * 
     * @param taskId 工单主键
     * @return 工单
     */
    public Task selectTaskByTaskId(Long taskId);

    /**
     * 查询工单列表
     * 
     * @param task 工单
     * @return 工单集合
     */
    public List<Task> selectTaskList(Task task);

    /**
     * 新增工单
     * 
     * @param task 工单
     * @return 结果
     */
    public int insertTask(Task task);

    /**
     * 修改工单
     * 
     * @param task 工单
     * @return 结果
     */
    public int updateTask(Task task);

    /**
     * 批量删除工单
     * 
     * @param taskIds 需要删除的工单主键集合
     * @return 结果
     */
    public int deleteTaskByTaskIds(Long[] taskIds);

    /**
     * 删除工单信息
     * 
     * @param taskId 工单主键
     * @return 结果
     */
    public int deleteTaskByTaskId(Long taskId);

    /**
     * 查询运维工单列表
     *
     * @param task 运维工单
     * @return TaskVo集合
     */
    List<TaskVo> selectTaskVoList(Task task);

    /**
     * 新增运营、运维工单
     *
     * @param taskDto
     * @return 结果
     */
    int insertTaskDto(TaskDto taskDto);

    /**
     * 完成工单
     * 根据工单类型自动更新设备状态
     *
     * @param taskId 工单ID
     * @return 结果
     */
    int completeTask(Long taskId);

    /**
     * 取消工单
     * @param task
     * @return 结果
     */
    int cancelTask(Task task);

    /**
     * 获取工单状态统计数据
     * 用于生成工单状态趋势图表
     *
     * @param task 查询条件
     * @return 工单状态统计结果
     */
    Map<String, Object> getTaskStatusStats(Task task);
}
