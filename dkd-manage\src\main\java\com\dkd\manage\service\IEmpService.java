package com.dkd.manage.service;

import java.util.List;
import java.util.Map;
import com.dkd.manage.domain.Emp;

/**
 * 人员列表Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IEmpService 
{
    /**
     * 查询人员列表
     * 
     * @param id 人员列表主键
     * @return 人员列表
     */
    public Emp selectEmpById(Long id);

    /**
     * 查询人员列表列表
     * 
     * @param emp 人员列表
     * @return 人员列表集合
     */
    public List<Emp> selectEmpList(Emp emp);

    /**
     * 新增人员列表
     * 
     * @param emp 人员列表
     * @return 结果
     */
    public int insertEmp(Emp emp);

    /**
     * 修改人员列表
     * 
     * @param emp 人员列表
     * @return 结果
     */
    public int updateEmp(Emp emp);

    /**
     * 批量删除人员列表
     * 
     * @param ids 需要删除的人员列表主键集合
     * @return 结果
     */
    public int deleteEmpByIds(Long[] ids);

    /**
     * 删除人员列表信息
     *
     * @param id 人员列表主键
     * @return 结果
     */
    public int deleteEmpById(Long id);

    /**
     * 获取人员角色分布统计数据
     * 用于生成人员角色分布统计图表
     *
     * @return 人员角色分布统计结果
     */
    public Map<String, Object> getEmpRoleStats();

    /**
     * 导入员工数据
     *
     * @param empList 员工数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importEmp(List<Emp> empList, Boolean isUpdateSupport, String operName);
}
