package com.dkd.manage.service;

import java.util.List;
import java.util.Map;
import com.dkd.manage.domain.VendingMachine;

/**
 * 设备管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IVendingMachineService 
{
    /**
     * 查询设备管理
     * 
     * @param id 设备管理主键
     * @return 设备管理
     */
    public VendingMachine selectVendingMachineById(Long id);

    /**
     * 查询设备管理列表
     * 
     * @param vendingMachine 设备管理
     * @return 设备管理集合
     */
    public List<VendingMachine> selectVendingMachineList(VendingMachine vendingMachine);

    /**
     * 新增设备管理
     * 
     * @param vendingMachine 设备管理
     * @return 结果
     */
    public int insertVendingMachine(VendingMachine vendingMachine);

    /**
     * 修改设备管理
     * 
     * @param vendingMachine 设备管理
     * @return 结果
     */
    public int updateVendingMachine(VendingMachine vendingMachine);

    /**
     * 批量删除设备管理
     * 
     * @param ids 需要删除的设备管理主键集合
     * @return 结果
     */
    public int deleteVendingMachineByIds(Long[] ids);

    /**
     * 删除设备管理信息
     * 
     * @param id 设备管理主键
     * @return 结果
     */
    public int deleteVendingMachineById(Long id);

    /**
     * 根据设备编号查询设备信息
     *
     * @param innerCode
     * @return VendingMachine
     */
    VendingMachine selectVendingMachineByInnerCode(String innerCode);

    /**
     * 获取设备状态统计数据
     * 用于生成设备状态统计图表
     *
     * @return 设备状态统计结果
     */
    public Map<String, Object> getVmStatusStats();
}
