<template>
  <div class="test-route-container">
    <div class="test-header">
      <h1>🧪 路由测试页面</h1>
      <p>这是一个用于测试路由跳转和页面渲染的测试页面</p>
    </div>
    
    <div class="test-content">
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>路由信息</span>
          </div>
        </template>
        
        <div class="route-info">
          <p><strong>当前路径:</strong> {{ route.path }}</p>
          <p><strong>完整路径:</strong> {{ route.fullPath }}</p>
          <p><strong>路由名称:</strong> {{ route.name }}</p>
          <p><strong>页面标题:</strong> {{ route.meta?.title }}</p>
          <p><strong>渲染时间:</strong> {{ renderTime }}</p>
        </div>
      </el-card>
      
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>快速导航测试</span>
          </div>
        </template>
        
        <div class="navigation-buttons">
          <el-button type="primary" @click="navigateTo('/')">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button type="success" @click="navigateTo('/manage/node')">
            <el-icon><Location /></el-icon>
            点位管理
          </el-button>
          <el-button type="info" @click="navigateTo('/manage/vm')">
            <el-icon><Monitor /></el-icon>
            设备管理
          </el-button>
          <el-button type="warning" @click="navigateTo('/manage/sku')">
            <el-icon><Document /></el-icon>
            商品管理
          </el-button>
        </div>
      </el-card>
      
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>组件状态</span>
          </div>
        </template>
        
        <div class="component-status">
          <p><strong>组件挂载时间:</strong> {{ mountTime }}</p>
          <p><strong>刷新次数:</strong> {{ refreshCount }}</p>
          <p><strong>页面可见性:</strong> {{ isVisible ? '可见' : '隐藏' }}</p>
          
          <el-button type="danger" @click="forceRefresh">
            <el-icon><Refresh /></el-icon>
            强制刷新
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="TestRoute">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  House, Location, Monitor, Document, Refresh 
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const renderTime = ref('')
const mountTime = ref('')
const refreshCount = ref(0)
const isVisible = ref(true)

// 更新渲染时间
const updateRenderTime = () => {
  renderTime.value = new Date().toLocaleString('zh-CN')
}

// 导航函数
const navigateTo = async (path) => {
  console.log('测试页面导航到:', path)
  try {
    await router.push(path)
    console.log('导航成功')
  } catch (error) {
    console.error('导航失败:', error)
  }
}

// 强制刷新
const forceRefresh = () => {
  refreshCount.value++
  updateRenderTime()
  console.log('强制刷新页面，次数:', refreshCount.value)
}

// 页面可见性检测
const handleVisibilityChange = () => {
  isVisible.value = !document.hidden
}

// 生命周期
onMounted(() => {
  console.log('测试页面已挂载')
  mountTime.value = new Date().toLocaleString('zh-CN')
  updateRenderTime()
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 定时更新渲染时间
  const timer = setInterval(updateRenderTime, 1000)
  
  onUnmounted(() => {
    console.log('测试页面已卸载')
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    clearInterval(timer)
  })
})
</script>

<style scoped lang="scss">
.test-route-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .test-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      color: #409eff;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
      font-size: 1.1rem;
    }
  }
  
  .test-content {
    display: grid;
    gap: 20px;
    
    .test-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
      }
      
      .route-info {
        p {
          margin: 10px 0;
          padding: 8px;
          background: #f5f7fa;
          border-radius: 4px;
          
          strong {
            color: #409eff;
          }
        }
      }
      
      .navigation-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        
        .el-button {
          flex: 1;
          min-width: 120px;
        }
      }
      
      .component-status {
        p {
          margin: 10px 0;
          padding: 8px;
          background: #f0f9ff;
          border-radius: 4px;
          border-left: 4px solid #409eff;
          
          strong {
            color: #409eff;
          }
        }
        
        .el-button {
          margin-top: 15px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .test-route-container {
    padding: 10px;
    
    .navigation-buttons {
      .el-button {
        flex: 1 1 100%;
      }
    }
  }
}
</style>
