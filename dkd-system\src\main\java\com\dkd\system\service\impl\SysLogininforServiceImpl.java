package com.dkd.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.system.domain.SysLogininfor;
import com.dkd.system.mapper.SysLogininforMapper;
import com.dkd.system.service.ISysLogininforService;

/**
 * 系统访问日志情况信息 服务层处理
 * 
 * <AUTHOR>
 */
// 定义系统登录日志服务实现类
@Service
public class SysLogininforServiceImpl implements ISysLogininforService
{
    // 注入登录日志数据库操作Mapper
    @Autowired
    private SysLogininforMapper logininforMapper;

    /**
     * 新增系统登录日志记录
     * 
     * @param logininfor 待插入的登录日志对象
     */
    @Override
    public void insertLogininfor(SysLogininfor logininfor)
    {
        logininforMapper.insertLogininfor(logininfor);
    }

    /**
     * 查询系统登录日志列表
     * 
     * @param logininfor 查询条件封装对象
     * @return 符合条件的登录日志列表
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor)
    {
        return logininforMapper.selectLogininforList(logininfor);
    }

    /**
     * 批量删除登录日志
     * 
     * @param infoIds 需要删除的登录日志ID数组
     * @return 删除影响行数
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return logininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空所有登录日志数据
     * 用于彻底清理日志记录
     */
    @Override
    public void cleanLogininfor()
    {
        logininforMapper.cleanLogininfor();
    }
}





// @Service
// public class SysLogininforServiceImpl implements ISysLogininforService
// {

//     @Autowired
//     private SysLogininforMapper logininforMapper;

//     /**
//      * 新增系统登录日志
//      * 
//      * @param logininfor 访问日志对象
//      */
//     @Override
//     public void insertLogininfor(SysLogininfor logininfor)
//     {
//         logininforMapper.insertLogininfor(logininfor);
//     }

//     /**
//      * 查询系统登录日志集合
//      * 
//      * @param logininfor 访问日志对象
//      * @return 登录记录集合
//      */
//     @Override
//     public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor)
//     {
//         return logininforMapper.selectLogininforList(logininfor);
//     }

//     /**
//      * 批量删除系统登录日志
//      * 
//      * @param infoIds 需要删除的登录日志ID
//      * @return 结果
//      */
//     @Override
//     public int deleteLogininforByIds(Long[] infoIds)
//     {
//         return logininforMapper.deleteLogininforByIds(infoIds);
//     }

//     /**
//      * 清空系统登录日志
//      */
//     @Override
//     public void cleanLogininfor()
//     {
//         logininforMapper.cleanLogininfor();
//     }
// }
