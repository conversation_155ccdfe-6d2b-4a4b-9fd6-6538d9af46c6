package com.dkd.manage.controller;

import cn.hutool.json.JSONObject;
import com.dkd.common.annotation.Anonymous;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysUser;
import com.dkd.common.core.domain.model.LoginUser;
import com.dkd.common.utils.SecurityUtils;

import com.dkd.framework.web.service.SysLoginService;
import com.dkd.framework.web.service.SysPermissionService;

import com.dkd.common.utils.HttpUtils;

import com.dkd.framework.web.service.TokenService;
import com.dkd.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;


import org.apache.commons.lang.StringUtils;

/**
 * 短信发送控制器
 */
@RestController
@RequestMapping("/sms")
@CrossOrigin
public class SmsSendController {

    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private SysPermissionService permissionService;

    /**
     * 手机号登录接口
     *
     * @param params 包含phone和smsCode参数的Map
     *
     * @return 统一响应结果
     */
    @Anonymous
    @PostMapping("/phone/login")
    public AjaxResult phoneLogin(@RequestBody Map<String, Object> params) {
        String phone = (String) params.get("phone");
        String smsCode = (String) params.get("smsCode");
        
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.error("手机号不能为空");
        }
        
        if (StringUtils.isBlank(smsCode)) {
            return AjaxResult.error("短信验证码不能为空");
        }
        
        // 验证短信验证码
        String storedCode = sysUserMapper.getPhoneCode(phone);
        String expireTime = sysUserMapper.getPhoneCodeExpireTime(phone);
        
        if (StringUtils.isBlank(storedCode)) {
            return AjaxResult.error("验证码已过期，请重新获取");
        }
        
        // 检查验证码是否过期
        if (StringUtils.isNotBlank(expireTime)) {
            LocalDateTime expireDateTime = LocalDateTime.parse(expireTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (LocalDateTime.now().isAfter(expireDateTime)) {
                // 清除过期验证码
                sysUserMapper.clearPhoneCode(phone);
                return AjaxResult.error("验证码已过期，请重新获取");
            }
        }
        
        if (!smsCode.equals(storedCode)) {
            return AjaxResult.error("验证码错误");
        }
        
        // 根据手机号查找用户
        SysUser user = sysUserMapper.selectUserByPhone(phone);
        if (user == null) {
            return AjaxResult.error("该手机号未注册");
        }
        
        // 创建LoginUser对象
        LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
        
        // 生成token
        String token = tokenService.createToken(loginUser);
        
        // 清除验证码
        sysUserMapper.clearPhoneCode(phone);
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);
        
        return AjaxResult.success("登录成功", result);
    }

    /**
     * 发送短信接口 - POST方式，支持JSON格式
     *
     * @param params 包含phone参数的Map
     *
     * @return 统一响应结果
     */
    @Anonymous
    @PostMapping("/send")
    public AjaxResult sendSms(@RequestBody Map<String, Object> params) {
        // 从请求体中获取phone参数
        String phone = (String) params.get("phone");
        
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.error("手机号不能为空");
        }

        return sendSmsInternal(phone);
    }

    /**
     * 发送短信接口 - GET方式，支持URL参数
     *
     * @param phone 手机号
     *
     * @return 统一响应结果
     */
    @Anonymous // 允许匿名访问
    @GetMapping("/send")
    public AjaxResult sendSmsGet(@RequestParam("phone") String phone) {
        if (StringUtils.isBlank(phone)) {
            return AjaxResult.error("手机号不能为空");
        }

        return sendSmsInternal(phone);
    }

    /**
     * 内部短信发送方法
     *
     * @param phone 手机号
     *
     * @return 统一响应结果
     */
    private AjaxResult sendSmsInternal(String phone) {
        // 短信服务配置（建议从配置文件读取）
        String host = "https://gyytz.market.alicloudapi.com";
        String path = "/sms/smsSend";
        String method = "POST";
        String appcode = "1e082348d5e645ebbcdb44d731209624";

        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + appcode);

        //随机生成6位数数字验证码
        String code = String.valueOf((int)((Math.random() * 9 + 1) * 100000));

        // 计算5分钟后的过期时间
        LocalDateTime expireTime = LocalDateTime.now().plusMinutes(5);
        String expireTimeStr = expireTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        //将验证码和过期时间存入数据库
        sysUserMapper.updatePhoneCode(phone, code, expireTimeStr);

        // 构建请求参数
        Map<String, String> querys = new HashMap<>();
        querys.put("mobile", phone);
        querys.put("param", "**code**:" + code + ",**minute**:" + 5);
        querys.put("smsSignId", "2e65b1bb3d054466b82f0c9d125465e2"); // 短信签名ID
        querys.put("templateId", "908e94ccf08b4476ba6c876d13f084ad"); // 短信模板ID

        Map<String, String> bodys = new HashMap<>();

        try {
            // 调用HttpUtils工具类发送POST请求
            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, bodys);

            // 处理响应结果
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());

                // 解析实际响应数据（假设响应为JSON格式）
                JSONObject jsonResponse = new JSONObject(responseBody);

                // 提取响应中的关键信息
                String msg = jsonResponse.getStr("msg", "短信发送成功");
                String smsId = jsonResponse.getStr("smsid", "");
                String codeStr = jsonResponse.getStr("code", "0");
                String balance = jsonResponse.getStr("balance", "0");

                // 构建结果数据
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("smsid", smsId);
                resultData.put("code", codeStr);
                resultData.put("balance", balance);

                return AjaxResult.success(msg, resultData);
            } else {
                String errorMsg = "短信发送失败，状态码：" +
                        (response != null ? response.getStatusLine().getStatusCode() : "未知");
                if (response != null) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    if (StringUtils.isNotBlank(responseBody)) {
                        errorMsg += "，响应内容：" + responseBody;
                    }
                }
                return AjaxResult.error(errorMsg);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("短信发送异常：" + e.getMessage());
        }
    }
}