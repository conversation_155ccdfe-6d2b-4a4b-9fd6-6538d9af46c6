package com.dkd.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysDept;
import com.dkd.common.core.domain.entity.SysRole;
import com.dkd.common.core.domain.entity.SysUser;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.SecurityUtils;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.system.service.ISysDeptService;
import com.dkd.system.service.ISysPostService;
import com.dkd.system.service.ISysRoleService;
import com.dkd.system.service.ISysUserService;

/**
 * 用户信息
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块用户管理相关的HTTP请求
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController
{
    // 注入用户业务层接口
    @Autowired
    private ISysUserService userService;

    // 注入角色业务层接口
    @Autowired
    private ISysRoleService roleService;

    // 注入部门业务层接口
    @Autowired
    private ISysDeptService deptService;

    // 注入岗位业务层接口
    @Autowired
    private ISysPostService postService;

    /**
     * 获取用户列表接口
     * 需要'system:user:list'权限
     * 请求路径：GET /system/user/list
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询用户数据
        List<SysUser> list = userService.selectUserList(user);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出用户列表接口
     * 需要'system:user:export'权限
     * 请求路径：POST /system/user/export
     */
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user)
    {
        // 调用服务层方法查询需要导出的用户数据
        List<SysUser> list = userService.selectUserList(user);
        // 创建Excel工具类实例
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "用户数据");
    }

    /**
     * 导入用户列表接口
     * 需要'system:user:import'权限
     * 请求路径：POST /system/user/importData
     */
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        // 读取Excel文件中的用户数据
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        // 获取当前操作用户名
        String operName = getUsername();
        // 调用服务层方法导入用户数据
        String message = userService.importUser(userList, updateSupport, operName);
        // 返回导入结果信息
        return success(message);
    }

    /**
     * 下载用户导入模板接口
     * 请求路径：POST /system/user/importTemplate
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        // 创建Excel工具类实例
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        // 导出空模板供用户下载
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息接口
     * 需要'system:user:query'权限
     * 请求路径：GET /system/user/{userId}
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        // 校验用户数据权限范围
        userService.checkUserDataScope(userId);

        // 创建成功响应对象
        AjaxResult ajax = AjaxResult.success();

        // 查询所有可用角色
        List<SysRole> roles = roleService.selectRoleAll();

        // 过滤掉管理员角色（非管理员用户）
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : 
            roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));

        // 查询所有岗位信息
        ajax.put("posts", postService.selectPostAll());

        // 如果有用户ID，则查询用户详细信息
        if (StringUtils.isNotNull(userId))
        {
            // 查询用户基本信息
            SysUser sysUser = userService.selectUserById(userId);
            // 添加用户信息到响应数据
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            // 查询并添加用户所属岗位ID
            ajax.put("postIds", postService.selectPostListByUserId(userId));

            // 查询并添加用户所属角色ID
            List<SysRole> userRoles = sysUser.getRoles();
            if (userRoles != null && !userRoles.isEmpty())
            {
                ajax.put("roleIds", userRoles.stream()
                    .map(SysRole::getRoleId)
                    .collect(Collectors.toList()));
            }
            else
            {
                ajax.put("roleIds", new ArrayList<>());
            }
        }

        // 返回响应结果
        return ajax;
    }

    /**
     * 新增用户接口
     * 需要'system:user:add'权限
     * 请求路径：POST /system/user
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user)
    {
        // 校验登录账号是否唯一
        if (!userService.checkUserNameUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        // 校验手机号是否唯一
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        // 校验邮箱是否唯一
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }

        // 设置创建人
        user.setCreateBy(getUsername());
        // 加密用户密码
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        // 调用服务层方法新增用户
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户接口
     * 需要'system:user:edit'权限
     * 请求路径：PUT /system/user
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user)
    {
        // 校验用户修改权限
        userService.checkUserAllowed(user);
        // 校验用户数据权限范围
        userService.checkUserDataScope(user.getUserId());

        // 校验登录账号是否唯一
        if (!userService.checkUserNameUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        // 校验手机号是否唯一
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        // 校验邮箱是否唯一
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }

        // 设置更新人
        user.setUpdateBy(getUsername());
        // 调用服务层方法修改用户信息
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户接口
     * 需要'system:user:remove'权限
     * 请求路径：DELETE /system/user/{userIds}
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        // 禁止删除当前登录用户
        if (ArrayUtils.contains(userIds, getUserId()))
        {
            return error("当前用户不能删除");
        }
        // 调用服务层方法删除指定ID的用户
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置用户密码接口
     * 需要'system:user:resetPwd'权限
     * 请求路径：PUT /system/user/resetPwd
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user)
    {
        // 校验用户修改权限
        userService.checkUserAllowed(user);
        // 校验用户数据权限范围
        userService.checkUserDataScope(user.getUserId());

        // 加密新密码
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        // 设置更新人
        user.setUpdateBy(getUsername());
        // 调用服务层方法重置密码
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 修改用户状态接口
     * 需要'system:user:edit'权限
     * 请求路径：PUT /system/user/changeStatus
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user)
    {
        // 校验用户修改权限
        userService.checkUserAllowed(user);
        // 校验用户数据权限范围
        userService.checkUserDataScope(user.getUserId());

        // 设置更新人
        user.setUpdateBy(getUsername());
        // 调用服务层方法修改用户状态
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色接口
     * 需要'system:user:query'权限
     * 请求路径：GET /system/user/authRole/{userId}
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId)
    {
        // 创建成功响应对象
        AjaxResult ajax = AjaxResult.success();

        // 查询用户信息和角色信息
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);

        // 添加用户信息
        ajax.put("user", user);
        // 添加角色信息（过滤掉管理员角色）
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : 
            roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));

        // 返回响应结果
        return ajax;
    }

    /**
     * 用户授权角色接口
     * 需要'system:user:edit'权限
     * 请求路径：PUT /system/user/authRole
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
    {
        // 校验用户数据权限范围
        userService.checkUserDataScope(userId);
        // 调用服务层方法执行用户授权
        userService.insertUserAuth(userId, roleIds);
        // 返回操作成功结果
        return success();
    }

    /**
     * 获取部门树列表接口
     * 需要'system:user:list'权限
     * 请求路径：GET /system/user/deptTree
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept)
    {
        // 调用服务层方法获取部门树结构
        return success(deptService.selectDeptTreeList(dept));
    }
}






// @RestController
// @RequestMapping("/system/user")
// public class SysUserController extends BaseController
// {
//     @Autowired
//     private ISysUserService userService;

//     @Autowired
//     private ISysRoleService roleService;

//     @Autowired
//     private ISysDeptService deptService;

//     @Autowired
//     private ISysPostService postService;

//     /**
//      * 获取用户列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysUser user)
//     {
//         startPage();
//         List<SysUser> list = userService.selectUserList(user);
//         return getDataTable(list);
//     }

//     /**
//      * 导出用户列表
//      * @param response
//      * @param user
//      */
//     @Log(title = "用户管理", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('system:user:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysUser user)
//     {
//         List<SysUser> list = userService.selectUserList(user);
//         ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
//         util.exportExcel(response, list, "用户数据");
//     }

//     /**
//      * 导入用户列表
//      * @param file
//      * @param updateSupport
//      * @return
//      * @throws Exception
//      */

//     @Log(title = "用户管理", businessType = BusinessType.IMPORT)
//     @PreAuthorize("@ss.hasPermi('system:user:import')")
//     @PostMapping("/importData")
//     public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
//     {
//         ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
//         List<SysUser> userList = util.importExcel(file.getInputStream());
//         String operName = getUsername();
//         String message = userService.importUser(userList, updateSupport, operName);
//         return success(message);
//     }

//     /**
//      * 导入用户数据
//      * @param response
//      */
//     @PostMapping("/importTemplate")
//     public void importTemplate(HttpServletResponse response)
//     {
//         ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
//         util.importTemplateExcel(response, "用户数据");
//     }

//     /**
//      * 根据用户编号获取详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:query')")
//     @GetMapping(value = { "/", "/{userId}" })
//     public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
//     {
//         userService.checkUserDataScope(userId);
//         AjaxResult ajax = AjaxResult.success();
//         List<SysRole> roles = roleService.selectRoleAll();
//         ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
//         ajax.put("posts", postService.selectPostAll());
//         if (StringUtils.isNotNull(userId))
//         {
//             SysUser sysUser = userService.selectUserById(userId);
//             ajax.put(AjaxResult.DATA_TAG, sysUser);
//             ajax.put("postIds", postService.selectPostListByUserId(userId));
//             // 检查用户角色是否为空，避免空指针异常
//             List<SysRole> userRoles = sysUser.getRoles();
//             if (userRoles != null && !userRoles.isEmpty())
//             {
//                 ajax.put("roleIds", userRoles.stream().map(SysRole::getRoleId).collect(Collectors.toList()));
//             }
//             else
//             {
//                 ajax.put("roleIds", new ArrayList<>());
//             }
//         }
//         return ajax;
//     }

//     /**
//      * 新增用户
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:add')")
//     @Log(title = "用户管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysUser user)
//     {
//         if (!userService.checkUserNameUnique(user))
//         {
//             return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
//         }
//         else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
//         {
//             return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
//         }
//         else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
//         {
//             return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
//         }
//         user.setCreateBy(getUsername());
//         user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
//         return toAjax(userService.insertUser(user));
//     }

//     /**
//      * 修改用户
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:edit')")
//     @Log(title = "用户管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysUser user)
//     {
//         userService.checkUserAllowed(user);
//         userService.checkUserDataScope(user.getUserId());
//         if (!userService.checkUserNameUnique(user))
//         {
//             return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
//         }
//         else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
//         {
//             return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
//         }
//         else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
//         {
//             return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
//         }
//         user.setUpdateBy(getUsername());
//         return toAjax(userService.updateUser(user));
//     }

//     /**
//      * 删除用户
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:remove')")
//     @Log(title = "用户管理", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{userIds}")
//     public AjaxResult remove(@PathVariable Long[] userIds)
//     {
//         if (ArrayUtils.contains(userIds, getUserId()))
//         {
//             return error("当前用户不能删除");
//         }
//         return toAjax(userService.deleteUserByIds(userIds));
//     }

//     /**
//      * 重置密码
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
//     @Log(title = "用户管理", businessType = BusinessType.UPDATE)
//     @PutMapping("/resetPwd")
//     public AjaxResult resetPwd(@RequestBody SysUser user)
//     {
//         userService.checkUserAllowed(user);
//         userService.checkUserDataScope(user.getUserId());
//         user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
//         user.setUpdateBy(getUsername());
//         return toAjax(userService.resetPwd(user));
//     }

//     /**
//      * 状态修改
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:edit')")
//     @Log(title = "用户管理", businessType = BusinessType.UPDATE)
//     @PutMapping("/changeStatus")
//     public AjaxResult changeStatus(@RequestBody SysUser user)
//     {
//         userService.checkUserAllowed(user);
//         userService.checkUserDataScope(user.getUserId());
//         user.setUpdateBy(getUsername());
//         return toAjax(userService.updateUserStatus(user));
//     }

//     /**
//      * 根据用户编号获取授权角色
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:query')")
//     @GetMapping("/authRole/{userId}")
//     public AjaxResult authRole(@PathVariable("userId") Long userId)
//     {
//         AjaxResult ajax = AjaxResult.success();
//         SysUser user = userService.selectUserById(userId);
//         List<SysRole> roles = roleService.selectRolesByUserId(userId);
//         ajax.put("user", user);
//         ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
//         return ajax;
//     }

//     /**
//      * 用户授权角色
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:edit')")
//     @Log(title = "用户管理", businessType = BusinessType.GRANT)
//     @PutMapping("/authRole")
//     public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
//     {
//         userService.checkUserDataScope(userId);
//         userService.insertUserAuth(userId, roleIds);
//         return success();
//     }

//     /**
//      * 获取部门树列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:user:list')")
//     @GetMapping("/deptTree")
//     public AjaxResult deptTree(SysDept dept)
//     {
//         return success(deptService.selectDeptTreeList(dept));
//     }
// }
