package com.dkd.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysDictData;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.StringUtils;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.system.service.ISysDictDataService;
import com.dkd.system.service.ISysDictTypeService;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块字典数据相关的HTTP请求
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController
{
    // 注入字典数据业务层接口
    @Autowired
    private ISysDictDataService dictDataService;

    // 注入字典类型业务层接口，用于关联操作
    @Autowired
    private ISysDictTypeService dictTypeService;

    /**
     * 获取字典数据列表接口
     * 需要'system:dict:list'权限
     * 请求路径：GET /system/dict/data/list
     */
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDictData dictData)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询字典数据
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出字典数据接口
     * 需要'system:dict:export'权限
     * 请求路径：POST /system/dict/data/export
     */
    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictData dictData)
    {
        // 调用服务层方法查询需要导出的字典数据
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        // 创建Excel工具类实例
        ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "字典数据");
    }

    /**
     * 查询字典数据详细信息接口
     * 需要'system:dict:query'权限
     * 请求路径：GET /system/dict/data/{dictCode}
     */
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode)
    {
        // 调用服务层方法获取字典数据详情
        return success(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息接口
     * 无需特殊权限
     * 请求路径：GET /system/dict/data/type/{dictType}
     */
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType)
    {
        // 调用服务层方法根据字典类型查询数据
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        // 如果结果为空则初始化空列表
        if (StringUtils.isNull(data))
        {
            data = new ArrayList<SysDictData>();
        }
        // 返回查询结果
        return success(data);
    }

    /**
     * 新增字典数据接口
     * 需要'system:dict:add'权限
     * 请求路径：POST /system/dict/data
     */
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictData dict)
    {
        // 设置创建人
        dict.setCreateBy(getUsername());
        // 调用服务层方法新增字典数据
        return toAjax(dictDataService.insertDictData(dict));
    }

    /**
     * 修改字典数据接口
     * 需要'system:dict:edit'权限
     * 请求路径：PUT /system/dict/data
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictData dict)
    {
        // 设置更新人
        dict.setUpdateBy(getUsername());
        // 调用服务层方法修改字典数据
        return toAjax(dictDataService.updateDictData(dict));
    }

    /**
     * 删除字典数据接口
     * 需要'system:dict:remove'权限
     * 请求路径：DELETE /system/dict/data/{dictCodes}
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable Long[] dictCodes)
    {
        // 调用服务层方法删除指定ID的字典数据
        dictDataService.deleteDictDataByIds(dictCodes);
        // 返回操作成功结果
        return success();
    }
}



// @RestController
// @RequestMapping("/system/dict/data")
// public class SysDictDataController extends BaseController
// {
//     @Autowired
//     private ISysDictDataService dictDataService;

//     @Autowired
//     private ISysDictTypeService dictTypeService;

//     /**
//      * 字典数据列表
//      * @param dictData
//      * @return
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysDictData dictData)
//     {
//         startPage();
//         List<SysDictData> list = dictDataService.selectDictDataList(dictData);
//         return getDataTable(list);
//     }

//     @Log(title = "字典数据", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('system:dict:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysDictData dictData)
//     {
//         List<SysDictData> list = dictDataService.selectDictDataList(dictData);
//         ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
//         util.exportExcel(response, list, "字典数据");
//     }

//     /**
//      * 查询字典数据详细
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:query')")
//     @GetMapping(value = "/{dictCode}")
//     public AjaxResult getInfo(@PathVariable Long dictCode)
//     {
//         return success(dictDataService.selectDictDataById(dictCode));
//     }

//     /**
//      * 根据字典类型查询字典数据信息
//      */
//     @GetMapping(value = "/type/{dictType}")
//     public AjaxResult dictType(@PathVariable String dictType)
//     {
//         List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
//         if (StringUtils.isNull(data))
//         {
//             data = new ArrayList<SysDictData>();
//         }
//         return success(data);
//     }

//     /**
//      * 新增字典类型
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:add')")
//     @Log(title = "字典数据", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysDictData dict)
//     {
//         dict.setCreateBy(getUsername());
//         return toAjax(dictDataService.insertDictData(dict));
//     }

//     /**
//      * 修改保存字典类型
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:edit')")
//     @Log(title = "字典数据", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysDictData dict)
//     {
//         dict.setUpdateBy(getUsername());
//         return toAjax(dictDataService.updateDictData(dict));
//     }

//     /**
//      * 删除字典类型
//      */
//     @PreAuthorize("@ss.hasPermi('system:dict:remove')")
//     @Log(title = "字典类型", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{dictCodes}")
//     public AjaxResult remove(@PathVariable Long[] dictCodes)
//     {
//         dictDataService.deleteDictDataByIds(dictCodes);
//         return success();
//     }
// }
