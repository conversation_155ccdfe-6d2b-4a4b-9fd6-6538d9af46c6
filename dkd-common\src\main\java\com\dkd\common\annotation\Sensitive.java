// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside; // Jackson注解内部标记
import com.fasterxml.jackson.databind.annotation.JsonSerialize; // Jackson序列化注解
import com.dkd.common.config.serializer.SensitiveJsonSerializer; // 导入敏感数据序列化器
import com.dkd.common.enums.DesensitizedType; // 导入脱敏类型枚举

/**
 * 数据脱敏注解
 * 用于标记需要进行数据脱敏处理的字段
 * 在JSON序列化时自动对敏感数据进行脱敏处理
 * 支持手机号、身份证号、邮箱、银行卡号等多种脱敏类型
 * 保护用户隐私，符合数据安全规范
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
@Target(ElementType.FIELD) // 指定注解只能应用于字段
@JacksonAnnotationsInside // 标记这是一个Jackson内部注解
@JsonSerialize(using = SensitiveJsonSerializer.class) // 指定使用自定义的敏感数据序列化器
public @interface Sensitive // 定义数据脱敏注解接口
{
    /**
     * 脱敏类型
     * 指定对应字段的脱敏处理方式
     * 不同类型的敏感数据有不同的脱敏规则
     * 例如：手机号中间4位用*替换，身份证号中间部分用*替换等
     *
     * @return 脱敏类型枚举
     */
    DesensitizedType desensitizedType(); // 定义脱敏类型属性，必须指定
}
