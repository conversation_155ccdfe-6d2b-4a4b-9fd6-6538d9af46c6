package com.dkd.web.controller.system;

import java.util.List;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.constant.UserConstants;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.domain.entity.SysDept;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.StringUtils;
import com.dkd.system.service.ISysDeptService;

/**
 * 部门信息
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块部门管理相关的HTTP请求
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController
{
    // 注入部门管理业务层接口
    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取部门列表接口
     * 需要'system:dept:list'权限
     * 请求路径：GET /system/dept/list
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    public AjaxResult list(SysDept dept)
    {
        // 调用服务层方法查询部门数据
        List<SysDept> depts = deptService.selectDeptList(dept);
        // 返回查询结果
        return success(depts);
    }

    /**
     * 查询部门列表（排除指定节点及其子节点）
     * 需要'system:dept:list'权限
     * 请求路径：GET /system/dept/list/exclude/{deptId}
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId)
    {
        // 查询所有部门数据
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        // 过滤掉当前部门及其子部门
        depts.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        // 返回过滤后的部门列表
        return success(depts);
    }

    /**
     * 根据部门编号获取详细信息接口
     * 需要'system:dept:query'权限
     * 请求路径：GET /system/dept/{deptId}
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId)
    {
        // 校验部门数据权限范围
        deptService.checkDeptDataScope(deptId);
        // 调用服务层方法获取部门详情
        return success(deptService.selectDeptById(deptId));
    }

    /**
     * 新增部门接口
     * 需要'system:dept:add'权限
     * 请求路径：POST /system/dept
     */
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept)
    {
        // 校验部门名称是否唯一
        if (!deptService.checkDeptNameUnique(dept))
        {
            return error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        // 设置创建人
        dept.setCreateBy(getUsername());
        // 调用服务层方法新增部门
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门接口
     * 需要'system:dept:edit'权限
     * 请求路径：PUT /system/dept
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept)
    {
        // 获取部门ID
        Long deptId = dept.getDeptId();
        // 校验部门数据权限范围
        deptService.checkDeptDataScope(deptId);
        // 校验部门名称是否唯一
        if (!deptService.checkDeptNameUnique(dept))
        {
            return error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        // 判断上级部门不能是自己
        else if (dept.getParentId().equals(deptId))
        {
            return error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        }
        // 判断该部门是否包含未停用的子部门
        else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0)
        {
            return error("该部门包含未停用的子部门！");
        }
        // 设置更新人
        dept.setUpdateBy(getUsername());
        // 调用服务层方法修改部门
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门接口
     * 需要'system:dept:remove'权限
     * 请求路径：DELETE /system/dept/{deptId}
     */
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId)
    {
        // 判断是否存在下级部门
        if (deptService.hasChildByDeptId(deptId))
        {
            return warn("存在下级部门,不允许删除");
        }
        // 判断部门是否存在用户
        if (deptService.checkDeptExistUser(deptId))
        {
            return warn("部门存在用户,不允许删除");
        }
        // 校验部门数据权限范围
        deptService.checkDeptDataScope(deptId);
        // 调用服务层方法删除部门
        return toAjax(deptService.deleteDeptById(deptId));
    }
}





// @RestController
// @RequestMapping("/system/dept")
// public class SysDeptController extends BaseController
// {
//     @Autowired
//     private ISysDeptService deptService;

//     /**
//      * 获取部门列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:dept:list')")
//     @GetMapping("/list")
//     public AjaxResult list(SysDept dept)
//     {
//         List<SysDept> depts = deptService.selectDeptList(dept);
//         return success(depts);
//     }

//     /**
//      * 查询部门列表（排除节点）
//      */
//     @PreAuthorize("@ss.hasPermi('system:dept:list')")
//     @GetMapping("/list/exclude/{deptId}")
//     public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId)
//     {
//         List<SysDept> depts = deptService.selectDeptList(new SysDept());
//         depts.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
//         return success(depts);
//     }

//     /**
//      * 根据部门编号获取详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('system:dept:query')")
//     @GetMapping(value = "/{deptId}")
//     public AjaxResult getInfo(@PathVariable Long deptId)
//     {
//         deptService.checkDeptDataScope(deptId);
//         return success(deptService.selectDeptById(deptId));
//     }

//     /**
//      * 新增部门
//      */
//     @PreAuthorize("@ss.hasPermi('system:dept:add')")
//     @Log(title = "部门管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysDept dept)
//     {
//         if (!deptService.checkDeptNameUnique(dept))
//         {
//             return error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
//         }
//         dept.setCreateBy(getUsername());
//         return toAjax(deptService.insertDept(dept));
//     }

//     /**
//      * 修改部门
//      */
//     @PreAuthorize("@ss.hasPermi('system:dept:edit')")
//     @Log(title = "部门管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysDept dept)
//     {
//         Long deptId = dept.getDeptId();
//         deptService.checkDeptDataScope(deptId);
//         if (!deptService.checkDeptNameUnique(dept))
//         {
//             return error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
//         }
//         else if (dept.getParentId().equals(deptId))
//         {
//             return error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
//         }
//         else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0)
//         {
//             return error("该部门包含未停用的子部门！");
//         }
//         dept.setUpdateBy(getUsername());
//         return toAjax(deptService.updateDept(dept));
//     }

//     /**
//      * 删除部门
//      */
//     @PreAuthorize("@ss.hasPermi('system:dept:remove')")
//     @Log(title = "部门管理", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{deptId}")
//     public AjaxResult remove(@PathVariable Long deptId)
//     {
//         if (deptService.hasChildByDeptId(deptId))
//         {
//             return warn("存在下级部门,不允许删除");
//         }
//         if (deptService.checkDeptExistUser(deptId))
//         {
//             return warn("部门存在用户,不允许删除");
//         }
//         deptService.checkDeptDataScope(deptId);
//         return toAjax(deptService.deleteDeptById(deptId));
//     }
// }
