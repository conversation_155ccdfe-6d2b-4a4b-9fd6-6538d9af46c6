package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.dkd.manage.domain.vo.RegionVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Region;
import com.dkd.manage.service.IRegionService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 区域管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
// 定义REST控制器，处理区域管理相关的HTTP请求
@RestController
@RequestMapping("/manage/region")
public class RegionController extends BaseController
{
    // 注入区域业务层接口
    @Autowired
    private IRegionService regionService;

    /**
     * 查询区域列表接口
     * 需要'manage:region:list'权限
     * 请求路径：GET /manage/region/list
     */
    @PreAuthorize("@ss.hasPermi('manage:region:list')")
    @GetMapping("/list")
    public TableDataInfo list(Region region)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询区域数据并返回视图对象列表
        List<RegionVo> voList = regionService.selectRegionVoList(region);
        // 返回分页数据
        return getDataTable(voList);
    }

    /**
     * 导出区域列表接口
     * 需要'manage:region:export'权限
     * 请求路径：POST /manage/region/export
     */
    @PreAuthorize("@ss.hasPermi('manage:region:export')")
    @Log(title = "区域管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Region region)
    {
        // 调用服务层方法查询需要导出的区域数据
        List<Region> list = regionService.selectRegionList(region);
        // 创建Excel工具类实例
        ExcelUtil<Region> util = new ExcelUtil<Region>(Region.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "区域管理数据");
    }

    /**
     * 获取区域详细信息接口
     * 需要'manage:region:query'权限
     * 请求路径：GET /manage/region/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:region:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取区域详情
        return success(regionService.selectRegionById(id));
    }

    /**
     * 新增区域接口
     * 需要'manage:region:add'权限
     * 请求路径：POST /manage/region
     */
    @PreAuthorize("@ss.hasPermi('manage:region:add')")
    @Log(title = "区域管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Region region)
    {
        // 调用服务层方法新增区域
        return toAjax(regionService.insertRegion(region));
    }

    /**
     * 修改区域接口
     * 需要'manage:region:edit'权限
     * 请求路径：PUT /manage/region
     */
    @PreAuthorize("@ss.hasPermi('manage:region:edit')")
    @Log(title = "区域管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Region region)
    {
        // 调用服务层方法修改区域信息
        return toAjax(regionService.updateRegion(region));
    }

    /**
     * 删除区域接口
     * 需要'manage:region:remove'权限
     * 请求路径：DELETE /manage/region/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:region:remove')")
    @Log(title = "区域管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的区域
        return toAjax(regionService.deleteRegionByIds(ids));
    }

    /**
     * 获取区域点位统计数据
     * 用于生成区域点位统计图表
     * 需要'manage:region:list'权限
     * 请求路径：GET /manage/region/nodeStats
     */
    @PreAuthorize("@ss.hasPermi('manage:region:list')")
    @GetMapping("/nodeStats")
    public AjaxResult getRegionNodeStats()
    {
        // 调用服务层方法获取区域点位统计数据
        List<RegionVo> stats = regionService.getRegionNodeStats();
        // 返回成功结果，包含统计数据
        return AjaxResult.success(stats);
    }
}





// @RestController
// @RequestMapping("/manage/region")
// public class RegionController extends BaseController
// {
//     @Autowired
//     private IRegionService regionService;

//     /**
//      * 查询区域管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:region:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Region region)
//     {
//         startPage();
//         List<RegionVo> voList = regionService.selectRegionVoList(region);
//         return getDataTable(voList);
//     }

//     /**
//      * 导出区域管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:region:export')")
//     @Log(title = "区域管理", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Region region)
//     {
//         List<Region> list = regionService.selectRegionList(region);
//         ExcelUtil<Region> util = new ExcelUtil<Region>(Region.class);
//         util.exportExcel(response, list, "区域管理数据");
//     }

//     /**
//      * 获取区域管理详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:region:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(regionService.selectRegionById(id));
//     }

//     /**
//      * 新增区域管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:region:add')")
//     @Log(title = "区域管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Region region)
//     {
//         return toAjax(regionService.insertRegion(region));
//     }

//     /**
//      * 修改区域管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:region:edit')")
//     @Log(title = "区域管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Region region)
//     {
//         return toAjax(regionService.updateRegion(region));
//     }

//     /**
//      * 删除区域管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:region:remove')")
//     @Log(title = "区域管理", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(regionService.deleteRegionByIds(ids));
//     }
// }
