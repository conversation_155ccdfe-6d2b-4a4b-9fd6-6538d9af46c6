<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component
            v-if="!route.meta.link"
            :is="Component"
            :key="componentKey(route)"
          />
        </keep-alive>
      </transition>
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import iframeToggle from "./IframeToggle/index"
import useTagsViewStore from '@/store/modules/tagsView'

const route = useRoute()
const tagsViewStore = useTagsViewStore()

// 强制刷新计数器
const forceRefresh = ref(0)

// 智能组件key生成函数
const componentKey = (currentRoute) => {
  // 对于首页，使用特殊的key策略避免缓存问题
  if (currentRoute.path === '/' || currentRoute.path === '/index') {
    return `home_${forceRefresh.value}_${Date.now()}`
  }

  // 对于其他页面，使用路径+刷新计数器
  return `${currentRoute.path}_${forceRefresh.value}`
}

// 监听路由变化，智能刷新组件
watch(() => route.fullPath, (newPath, oldPath) => {
  console.log('路由变化:', oldPath, '->', newPath)

  if (newPath !== oldPath) {
    // 如果从首页跳转到其他页面，或者从其他页面跳转，强制刷新
    if (oldPath === '/' || oldPath === '/index' ||
        newPath === '/' || newPath === '/index' ||
        (oldPath && oldPath !== newPath)) {
      forceRefresh.value++
      console.log('强制刷新组件，确保正确渲染')
    }
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>

