<template>
  <div class="app-container">
    <!-- 区域点位统计图表 -->
    <el-card class="mb-4" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>区域点位统计</span>
          <el-button type="text" @click="refreshChart" :loading="chartLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 400px;"></div>
    </el-card>

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="区域名称" prop="regionName">
        <el-input
          v-model="queryParams.regionName"
          placeholder="请输入区域名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['manage:region:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:region:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:region:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['manage:region:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="regionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="区域名称" align="center" prop="regionName" />
      <el-table-column label="点位数" align="center" prop="nodeCount" />
      <el-table-column label="备注说明" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary"  @click="getRegionInfo(scope.row)" v-hasPermi="['manage:node:list']">查看详情</el-button>
          <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['manage:region:edit']">修改</el-button>
          <el-button link type="primary"  @click="handleDelete(scope.row)" v-hasPermi="['manage:region:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改区域管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="regionRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="区域名称" prop="regionName">
          <el-input v-model="form.regionName" placeholder="请输入区域名称" />
        </el-form-item>
        <el-form-item label="备注说明" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看区域管理详情对话框 -->
    <el-dialog title="区域详情" v-model="regionInfoOpen" width="500px" append-to-body>
      <el-form-item label="区域名称" prop="regionName">
          <el-input v-model="form.regionName" disabled />
      </el-form-item>
      <label>包含点位：</label>
      <el-table :data="nodeList">
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="点位名称" align="center" prop="nodeName" />
          <el-table-column label="设备数量" align="center" prop="vmCount" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Region">
// <!-- 导入区域管理相关的 API 方法 -->
import { listRegion, getRegion, delRegion, addRegion, updateRegion, getRegionNodeStats } from "@/api/manage/region";
import {listNode} from "@/api/manage/node";
import {loadAllParams} from "@/api/page";
// ECharts相关导入
import * as echarts from 'echarts';
import { Refresh } from '@element-plus/icons-vue';
/**
 * 获取当前组件实例，并解构出 proxy 属性
 * proxy 可以访问组件的上下文，如方法、属性等
 */
const { proxy } = getCurrentInstance();

// 定义响应式变量，用于存储区域管理列表数据
const regionList = ref([]);
// 定义响应式变量，控制添加或修改区域管理对话框的显示与隐藏
const open = ref(false);
// 定义响应式变量，控制表格加载状态，初始为加载中
const loading = ref(true);
// 定义响应式变量，控制搜索表单的显示与隐藏
const showSearch = ref(true);
// 定义响应式变量，用于存储多选框选中项的 ID 数组
const ids = ref([]);
// 定义响应式变量，判断是否只选中了一项数据
const single = ref(true);
// 定义响应式变量，判断是否没有选中任何数据
const multiple = ref(true);
// 定义响应式变量，存储区域管理列表的总记录数
const total = ref(0);
// 定义响应式变量，存储添加或修改区域管理对话框的标题
const title = ref("");

// ECharts相关变量
const chartContainer = ref(null); // 图表容器引用
const chartInstance = ref(null); // 图表实例
const chartLoading = ref(false); // 图表加载状态

// 定义响应式对象，包含表单数据、查询参数和表单验证规则
const data = reactive({
  // 存储表单数据
  form: {},
  // 存储查询参数
  queryParams: {
    // 当前页码，初始为第 1 页
    pageNum: 1,
    // 每页显示的记录数，初始为 10 条
    pageSize: 10,
    // 区域名称查询条件，初始为 null
    regionName: null,
  },
  // 定义表单验证规则
  rules: {
    // 区域名称的验证规则
    regionName: [
      // 区域名称为必填项，失去焦点时触发验证
      { required: true, message: "区域名称不能为空", trigger: "blur" }
    ],
    // 备注说明的验证规则
    remark: [
      // 备注说明为必填项，失去焦点时触发验证
      { required: true, message: "备注说明不能为空", trigger: "blur" }
    ]
  }
});

// 从响应式对象 data 中解构出查询参数、表单数据和验证规则，并转换为响应式引用
const { queryParams, form, rules } = toRefs(data);

/**
 * 查询区域管理列表
 * 调用 listRegion API 方法获取区域管理列表数据，并更新相关响应式变量
 */
function getList() {
  // 开始加载数据，设置加载状态为 true
  loading.value = true;
  // 调用 listRegion API 方法，传入查询参数
  listRegion(queryParams.value).then(response => {
    // 将响应数据中的列表数据赋值给 regionList
    regionList.value = response.rows;
    // 将响应数据中的总记录数赋值给 total
    total.value = response.total;
    // 数据加载完成，设置加载状态为 false
    loading.value = false;

    // 刷新图表数据
    if (chartInstance.value) {
      loadChartData();
    }
  });
}

/**
 * 取消按钮点击事件处理函数
 * 关闭添加或修改区域管理对话框，并重置表单数据
 */
function cancel() {
  // 关闭对话框
  open.value = false;
  // 重置表单数据
  reset();
}

/**
 * 表单重置函数
 * 重置表单数据为初始值，并调用 proxy 的 resetForm 方法重置表单验证状态
 */
function reset() {
  // 重置表单数据为初始值
  form.value = {
    id: null,
    regionName: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    remark: null
  };
  // 调用 proxy 的 resetForm 方法，重置名为 "regionRef" 的表单验证状态
  proxy.resetForm("regionRef");
}

/**
 * 搜索按钮操作处理函数
 * 将当前页码重置为第 1 页，并重新查询区域管理列表
 */
function handleQuery() {
  // 将当前页码重置为第 1 页
  queryParams.value.pageNum = 1;
  // 重新查询区域管理列表
  getList();
}

/**
 * 重置按钮操作处理函数
 * 调用 proxy 的 resetForm 方法重置查询表单，并重新查询区域管理列表
 */
function resetQuery() {
  // 调用 proxy 的 resetForm 方法，重置名为 "queryRef" 的查询表单
  proxy.resetForm("queryRef");
  // 重新查询区域管理列表
  handleQuery();
}

/**
 * 多选框选中数据变化处理函数
 * 根据选中项更新 ids、single 和 multiple 的值
 * @param {Array} selection - 多选框选中的行数据数组
 */
function handleSelectionChange(selection) {
  // 将选中项的 ID 提取出来，存储到 ids 中
  ids.value = selection.map(item => item.id);
  // 判断是否只选中了一项数据
  single.value = selection.length != 1;
  // 判断是否没有选中任何数据
  multiple.value = !selection.length;
}

/**
 * 新增按钮操作处理函数
 * 重置表单数据，打开添加区域管理对话框，并设置对话框标题
 */
function handleAdd() {
  // 重置表单数据
  reset();
  // 打开添加区域管理对话框
  open.value = true;
  // 设置对话框标题为 "添加区域管理"
  title.value = "添加区域管理";
}

/**
 * 修改按钮操作处理函数
 * 重置表单数据，根据传入的行数据或选中项 ID 获取区域管理详情，并打开修改对话框
 * @param {Object} row - 选中的行数据
 */
function handleUpdate(row) {
  // 重置表单数据
  reset();
  // 获取要修改的区域管理 ID
  const _id = row.id || ids.value;
  // 调用 getRegion API 方法获取区域管理详情
  getRegion(_id).then(response => {
    // 将获取到的详情数据赋值给表单数据
    form.value = response.data;
    // 打开修改区域管理对话框
    open.value = true;
    // 设置对话框标题为 "修改区域管理"
    title.value = "修改区域管理";
  });
}

/**
 * 获取区域管理详情
 * 调用 getRegion API 方法获取区域管理详情，并打开查看详情对话框
 * @param {Object} row - 选中的行数据
 * */
const nodeList = ref([]);
const regionInfoOpen = ref(false);
function getRegionInfo(row) {
  reset();
  const _id = row.id || ids.value;
  getRegion(_id).then(response => {
    form.value = response.data;
  });
  loadAllParams.regionId = row.id;
  listNode(loadAllParams).then(response => {
    nodeList.value = response.rows;
  });
  regionInfoOpen.value = true;
}

/**
 * 提交按钮操作处理函数
 * 验证表单数据，根据表单数据是否包含 ID 决定是新增还是修改区域管理数据
 */
function submitForm() {
  // 调用名为 "regionRef" 的表单的 validate 方法进行表单验证
  proxy.$refs["regionRef"].validate(valid => {
    // 如果表单验证通过
    if (valid) {
      // 判断表单数据是否包含 ID，如果包含则进行修改操作
      if (form.value.id != null) {
        // 调用 updateRegion API 方法进行修改操作
        updateRegion(form.value).then(response => {
          // 显示修改成功的提示信息
          proxy.$modal.msgSuccess("修改成功");
          // 关闭对话框
          open.value = false;
          // 重新查询区域管理列表
          getList();
        });
      } else {
        // 调用 addRegion API 方法进行新增操作
        addRegion(form.value).then(response => {
          // 显示新增成功的提示信息
          proxy.$modal.msgSuccess("新增成功");
          // 关闭对话框
          open.value = false;
          // 重新查询区域管理列表
          getList();
        });
      }
    }
  });
}

/**
 * 删除按钮操作处理函数
 * 弹出确认对话框，确认后调用 delRegion API 方法删除区域管理数据
 * @param {Object} row - 选中的行数据
 */
function handleDelete(row) {
  // 获取要删除的区域管理 ID
  const _ids = row.id || ids.value;
  // 弹出确认对话框，询问是否确认删除
  proxy.$modal.confirm('是否确认删除区域管理编号为"' + _ids + '"的数据项？').then(function() {
    // 确认后调用 delRegion API 方法进行删除操作
    return delRegion(_ids);
  }).then(() => {
    // 重新查询区域管理列表
    getList();
    // 显示删除成功的提示信息
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/**
 * 导出按钮操作处理函数
 * 调用 proxy 的 download 方法导出区域管理数据
 */
function handleExport() {
  // 调用 proxy 的 download 方法，传入导出接口地址、查询参数和文件名
  proxy.download('manage/region/export', {
    ...queryParams.value
  }, `region_${new Date().getTime()}.xlsx`)
}

// ECharts相关方法
/**
 * 初始化图表
 */
function initChart() {
  if (chartContainer.value) {
    chartInstance.value = echarts.init(chartContainer.value);
    loadChartData();
  }
}

/**
 * 加载图表数据
 */
async function loadChartData() {
  try {
    chartLoading.value = true;

    const response = await getRegionNodeStats();
    const data = response.data || [];

    // 处理数据
    const regionNames = [];
    const nodeCounts = [];

    // 如果后端API尚未实现，使用regionList的数据模拟
    if (data.length === 0 && regionList.value.length > 0) {
      regionList.value.forEach(region => {
        regionNames.push(region.regionName);
        nodeCounts.push(region.nodeCount || Math.floor(Math.random() * 20) + 1); // 使用nodeCount或随机数
      });
    } else {
      // 使用API返回的数据
      data.forEach(item => {
        regionNames.push(item.regionName);
        nodeCounts.push(item.nodeCount);
      });
    }

    updateChart(regionNames, nodeCounts);
  } catch (error) {
    console.error('加载图表数据失败:', error);
    proxy.$modal.msgError('加载图表数据失败');
  } finally {
    chartLoading.value = false;
  }
}

/**
 * 更新图表
 */
function updateChart(regionNames, nodeCounts) {
  if (!chartInstance.value) return;

  const option = {
    title: {
      text: '各区域点位数量统计',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c} 个点位'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: regionNames,
      axisLabel: {
        interval: 0,
        rotate: regionNames.length > 10 ? 30 : 0, // 当区域较多时旋转标签
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '点位数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40] // 调整y轴名称位置
      },
      axisLabel: {
        fontSize: 12,
        formatter: '{value}' // 确保显示整数
      },
      minInterval: 1, // 设置最小间隔为1，确保只显示整数刻度
      splitNumber: 5 // 建议分割段数，让刻度更合理
    },
    series: [
      {
        name: '点位数量',
        type: 'bar',
        barWidth: '60%',
        data: nodeCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }
    ]
  };

  chartInstance.value.setOption(option);
}

/**
 * 刷新图表
 */
function refreshChart() {
  loadChartData();
}

/**
 * 窗口大小改变时重新调整图表大小
 */
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
}

// 生命周期钩子
onMounted(() => {
  getList(); // 首次查询区域管理列表

  nextTick(() => {
    initChart(); // 初始化图表
  });

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
  }
}
</style>
