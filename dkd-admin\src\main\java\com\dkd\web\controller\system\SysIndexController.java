package com.dkd.web.controller.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.config.RuoYiConfig;
import com.dkd.common.utils.StringUtils;

/**
 * 首页
 *
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块首页相关的HTTP请求
@RestController
public class SysIndexController
{
    /** 系统基础配置 */
    @Autowired
    private RuoYiConfig ruoyiConfig;

    /**
     * 访问首页，提示语
     * 请求路径：GET /
     */
    @RequestMapping("/")
    public String index()
    {
        // 返回欢迎信息和当前系统版本号
        return StringUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", ruoyiConfig.getName(), ruoyiConfig.getVersion());
    }
}



// @RestController
// public class SysIndexController
// {
//     /** 系统基础配置 */
//     @Autowired
//     private RuoYiConfig ruoyiConfig;

//     /**
//      * 访问首页，提示语
//      */
//     @RequestMapping("/")
//     public String index()
//     {
//         return StringUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", ruoyiConfig.getName(), ruoyiConfig.getVersion());
//     }
// }
