package com.dkd.manage.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.VendingMachine;
import com.dkd.manage.service.IVendingMachineService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 设备管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
// 定义REST控制器，处理售货机（设备）管理相关的HTTP请求
@RestController
@RequestMapping("/manage/vm")
public class VendingMachineController extends BaseController
{
    // 注入售货机业务层接口
    @Autowired
    private IVendingMachineService vendingMachineService;

    /**
     * 查询售货机列表接口
     * 需要'manage:vm:list'权限
     * 请求路径：GET /manage/vm/list
     */
    @PreAuthorize("@ss.hasPermi('manage:vm:list')")
    @GetMapping("/list")
    public TableDataInfo list(VendingMachine vendingMachine)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询售货机数据
        List<VendingMachine> list = vendingMachineService.selectVendingMachineList(vendingMachine);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出售货机列表接口
     * 需要'manage:vm:export'权限
     * 请求路径：POST /manage/vm/export
     */
    @PreAuthorize("@ss.hasPermi('manage:vm:export')")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VendingMachine vendingMachine)
    {
        // 调用服务层方法查询需要导出的售货机数据
        List<VendingMachine> list = vendingMachineService.selectVendingMachineList(vendingMachine);
        // 创建Excel工具类实例
        ExcelUtil<VendingMachine> util = new ExcelUtil<VendingMachine>(VendingMachine.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "设备管理数据");
    }

    /**
     * 获取售货机详细信息接口
     * 需要'manage:vm:query'权限
     * 请求路径：GET /manage/vm/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:vm:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取售货机详情
        return success(vendingMachineService.selectVendingMachineById(id));
    }

    /**
     * 新增售货机接口
     * 需要'manage:vm:add'权限
     * 请求路径：POST /manage/vm
     */
    @PreAuthorize("@ss.hasPermi('manage:vm:add')")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VendingMachine vendingMachine)
    {
        // 调用服务层方法新增售货机
        return toAjax(vendingMachineService.insertVendingMachine(vendingMachine));
    }

    /**
     * 修改售货机接口
     * 需要'manage:vm:edit'权限
     * 请求路径：PUT /manage/vm
     */
    @PreAuthorize("@ss.hasPermi('manage:vm:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VendingMachine vendingMachine)
    {
        // 调用服务层方法修改售货机信息
        return toAjax(vendingMachineService.updateVendingMachine(vendingMachine));
    }

    /**
     * 删除售货机接口
     * 需要'manage:vm:remove'权限
     * 请求路径：DELETE /manage/vm/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:vm:remove')")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的售货机
        return toAjax(vendingMachineService.deleteVendingMachineByIds(ids));
    }

    /**
     * 获取设备状态统计数据
     * 用于生成设备状态统计图表
     * 需要'manage:vm:list'权限
     * 请求路径：GET /manage/vm/statusStats
     */
    @PreAuthorize("@ss.hasPermi('manage:vm:list')")
    @GetMapping("/statusStats")
    public AjaxResult getVmStatusStats()
    {
        // 调用服务层方法获取设备状态统计数据
        Map<String, Object> stats = vendingMachineService.getVmStatusStats();
        // 返回成功结果，包含统计数据
        return AjaxResult.success(stats);
    }
}



// @RestController
// @RequestMapping("/manage/vm")
// public class VendingMachineController extends BaseController
// {
//     @Autowired
//     private IVendingMachineService vendingMachineService;

//     /**
//      * 查询设备管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vm:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(VendingMachine vendingMachine)
//     {
//         startPage();
//         List<VendingMachine> list = vendingMachineService.selectVendingMachineList(vendingMachine);
//         return getDataTable(list);
//     }

//     /**
//      * 导出设备管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vm:export')")
//     @Log(title = "设备管理", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, VendingMachine vendingMachine)
//     {
//         List<VendingMachine> list = vendingMachineService.selectVendingMachineList(vendingMachine);
//         ExcelUtil<VendingMachine> util = new ExcelUtil<VendingMachine>(VendingMachine.class);
//         util.exportExcel(response, list, "设备管理数据");
//     }

//     /**
//      * 获取设备管理详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vm:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(vendingMachineService.selectVendingMachineById(id));
//     }

//     /**
//      * 新增设备管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vm:add')")
//     @Log(title = "设备管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody VendingMachine vendingMachine)
//     {
//         return toAjax(vendingMachineService.insertVendingMachine(vendingMachine));
//     }

//     /**
//      * 修改设备管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vm:edit')")
//     @Log(title = "设备管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody VendingMachine vendingMachine)
//     {
//         return toAjax(vendingMachineService.updateVendingMachine(vendingMachine));
//     }

//     /**
//      * 删除设备管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:vm:remove')")
//     @Log(title = "设备管理", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(vendingMachineService.deleteVendingMachineByIds(ids));
//     }
// }
