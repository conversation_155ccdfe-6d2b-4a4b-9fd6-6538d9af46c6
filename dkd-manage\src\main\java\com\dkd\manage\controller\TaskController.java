package com.dkd.manage.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.dkd.manage.domain.TaskDetails;
import com.dkd.manage.domain.dto.TaskDto;
import com.dkd.manage.domain.vo.TaskVo;
import com.dkd.manage.service.ITaskDetailsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Task;
import com.dkd.manage.service.ITaskService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 工单Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
// 定义REST控制器，处理工单管理相关的HTTP请求
@RestController
@RequestMapping("/manage/task")
public class TaskController extends BaseController {
    // 注入工单业务层接口
    @Autowired
    private ITaskService taskService;

    // 注入工单详情业务层接口，用于处理工单详细信息
    @Autowired
    private ITaskDetailsService taskDetailsService;

    /**
     * 查询工单列表接口
     * 需要'manage:task:list'权限
     * 请求路径：GET /manage/task/list
     */
    @PreAuthorize("@ss.hasPermi('manage:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(Task task) {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询工单数据并返回视图对象列表
        List<TaskVo> voList = taskService.selectTaskVoList(task);
        // 返回分页数据
        return getDataTable(voList);
    }

    /**
     * 导出工单列表接口
     * 需要'manage:task:export'权限
     * 请求路径：POST /manage/task/export
     */
    @PreAuthorize("@ss.hasPermi('manage:task:export')")
    @Log(title = "工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Task task) {
        // 调用服务层方法查询需要导出的工单数据
        List<Task> list = taskService.selectTaskList(task);
        // 创建Excel工具类实例
        ExcelUtil<Task> util = new ExcelUtil<Task>(Task.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "工单数据");
    }

    /**
     * 获取工单详细信息接口
     * 需要'manage:task:query'权限
     * 请求路径：GET /manage/task/{taskId}
     */
    @PreAuthorize("@ss.hasPermi('manage:task:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId) {
        // 调用服务层方法获取工单详情
        return success(taskService.selectTaskByTaskId(taskId));
    }

    /**
     * 新增工单接口
     * 需要'manage:task:add'权限
     * 请求路径：POST /manage/task
     */
    @PreAuthorize("@ss.hasPermi('manage:task:add')")
    @Log(title = "工单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskDto taskDto) {
        // 设置当前登录用户为指派人ID
        taskDto.setAssignorId(getUserId());
        // 调用服务层方法新增工单
        return toAjax(taskService.insertTaskDto(taskDto));
    }

    /**
     * 修改工单接口
     * 需要'manage:task:edit'权限
     * 请求路径：PUT /manage/task
     */
    @PreAuthorize("@ss.hasPermi('manage:task:edit')")
    @Log(title = "工单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Task task) {
        // 调用服务层方法修改工单信息
        return toAjax(taskService.updateTask(task));
    }

    /**
     * 删除工单接口
     * 需要'manage:task:remove'权限
     * 请求路径：DELETE /manage/task/{taskIds}
     */
    @PreAuthorize("@ss.hasPermi('manage:task:remove')")
    @Log(title = "工单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable Long[] taskIds) {
        // 调用服务层方法删除指定ID的工单
        return toAjax(taskService.deleteTaskByTaskIds(taskIds));
    }

    /**
     * 完成工单接口
     * 需要'manage:task:edit'权限
     * 请求路径：PUT /manage/task/complete/{taskId}
     */
    @PreAuthorize("@ss.hasPermi('manage:task:edit')")
    @Log(title = "工单", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{taskId}")
    public AjaxResult completeTask(@PathVariable Long taskId) {
        try {
            // 调用服务层方法完成工单
            int result = taskService.completeTask(taskId);
            return toAjax(result);
        } catch (Exception e) {
            return error("完成工单失败：" + e.getMessage());
        }
    }

    /**
     * 取消工单接口
     * 需要'manage:task:edit'权限
     * 请求路径：PUT /manage/task/cancel
     */
    @PreAuthorize("@ss.hasPermi('manage:task:edit')")
    @Log(title = "工单", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel")
    public AjaxResult cancelTask(@RequestBody Task task) {
        // 调用服务层方法执行取消工单操作
        return toAjax(taskService.cancelTask(task));
    }

    /**
     * 获取工单状态统计数据
     * 用于生成工单状态趋势图表
     *
     * @param task 查询条件
     * @return 工单状态统计结果
     */
    @PreAuthorize("@ss.hasPermi('manage:task:list')")
    @GetMapping("/statusStats")
    public AjaxResult getTaskStatusStats(Task task) {
        // 调用业务层方法获取工单状态统计数据
        Map<String, Object> stats = taskService.getTaskStatusStats(task);
        // 返回成功结果，包含统计数据
        return AjaxResult.success(stats);
    }
}

