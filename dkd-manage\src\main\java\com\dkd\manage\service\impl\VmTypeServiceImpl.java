package com.dkd.manage.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.manage.mapper.VmTypeMapper;
import com.dkd.manage.domain.VmType;
import com.dkd.manage.service.IVmTypeService;

/**
 * 设备类型管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class VmTypeServiceImpl implements IVmTypeService 
{
    @Autowired
    private VmTypeMapper vmTypeMapper;

    /**
     * 查询设备类型管理
     * 
     * @param id 设备类型管理主键
     * @return 设备类型管理
     */
    @Override
    public VmType selectVmTypeById(Long id)
    {
        return vmTypeMapper.selectVmTypeById(id);
    }

    /**
     * 查询设备类型管理列表
     * 
     * @param vmType 设备类型管理
     * @return 设备类型管理
     */
    @Override
    public List<VmType> selectVmTypeList(VmType vmType)
    {
        return vmTypeMapper.selectVmTypeList(vmType);
    }

    /**
     * 新增设备类型管理
     * 
     * @param vmType 设备类型管理
     * @return 结果
     */
    @Override
    public int insertVmType(VmType vmType)
    {
        return vmTypeMapper.insertVmType(vmType);
    }

    /**
     * 修改设备类型管理
     * 
     * @param vmType 设备类型管理
     * @return 结果
     */
    @Override
    public int updateVmType(VmType vmType)
    {
        return vmTypeMapper.updateVmType(vmType);
    }

    /**
     * 批量删除设备类型管理
     * 
     * @param ids 需要删除的设备类型管理主键
     * @return 结果
     */
    @Override
    public int deleteVmTypeByIds(Long[] ids)
    {
        return vmTypeMapper.deleteVmTypeByIds(ids);
    }

    /**
     * 删除设备类型管理信息
     * 
     * @param id 设备类型管理主键
     * @return 结果
     */
    @Override
    public int deleteVmTypeById(Long id)
    {
        return vmTypeMapper.deleteVmTypeById(id);
    }

    /**
     * 获取设备类型统计数据
     * 用于生成设备类型统计图表
     *
     * @return 设备类型统计结果
     */
    @Override
    public Map<String, Object> getVmTypeStats() {
        // 创建返回结果Map
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询所有设备类型
            List<VmType> vmTypeList = vmTypeMapper.selectVmTypeList(new VmType());

            // 构建返回数据
            result.put("vmTypeList", vmTypeList);
            result.put("total", vmTypeList.size());

        } catch (Exception e) {
            // 异常处理，返回空数据
            result.put("vmTypeList", new HashMap<>());
            result.put("total", 0);
        }

        return result;
    }
}
