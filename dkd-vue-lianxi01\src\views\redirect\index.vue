<template>
  <div class="redirect-container">
    <div class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>页面跳转中...</span>
    </div>
  </div>
</template>

<script setup>
import { nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const { params, query } = route
const { path } = params

// 使用nextTick确保DOM更新后再进行路由跳转
nextTick(() => {
  const targetPath = '/' + path
  console.log('Redirect页面跳转到:', targetPath)

  router.replace({
    path: targetPath,
    query
  }).catch(err => {
    console.error('重定向失败:', err)
    // 如果重定向失败，回到首页
    router.replace('/')
  })
})
</script>

<style scoped lang="scss">
.redirect-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #666;

    .el-icon {
      font-size: 2rem;
    }
  }
}
</style>