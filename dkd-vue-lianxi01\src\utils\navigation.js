/**
 * 路由导航工具函数
 * 解决页面空白和路由跳转问题
 */

import { ElMessage } from 'element-plus'
import { nextTick } from 'vue'

/**
 * 安全的路由导航函数
 * @param {Object} router - Vue Router实例
 * @param {string} path - 目标路径
 * @param {Object} options - 导航选项
 */
export async function safeNavigate(router, path, options = {}) {
  const {
    replace = false,
    force = false,
    showMessage = true
  } = options

  console.log('开始导航到:', path)

  try {
    // 1. 检查路由是否存在
    const route = router.resolve(path)
    if (route.matched.length === 0) {
      console.error('路由不存在:', path)
      if (showMessage) {
        ElMessage.error(`路由 ${path} 不存在，请检查路由配置`)
      }
      return false
    }

    // 2. 如果是当前路由，先跳转到首页再跳转到目标页面
    if (router.currentRoute.value.path === path) {
      console.log('当前已在目标路由，先跳转到首页再返回')
      await router.push('/')
      await nextTick()
    }

    // 3. 直接导航到目标路由
    console.log('直接导航到目标路由:', path)
    const navigationMethod = replace ? router.replace : router.push
    await navigationMethod(path)

    // 4. 等待组件渲染完成
    await nextTick()

    console.log('导航成功到:', path)
    return true

  } catch (error) {
    console.error('导航失败:', error)

    if (showMessage) {
      ElMessage.error('页面跳转失败，请重试')
    }

    return false
  }
}

/**
 * 批量预加载路由组件
 * @param {Object} router - Vue Router实例
 * @param {Array} paths - 路径数组
 */
export async function preloadRoutes(router, paths) {
  console.log('开始预加载路由:', paths)

  for (const path of paths) {
    try {
      const route = router.resolve(path)
      if (route.matched.length > 0) {
        // 预解析路由组件
        for (const match of route.matched) {
          if (match.components && match.components.default) {
            // 检查是否是函数（动态导入）
            if (typeof match.components.default === 'function') {
              try {
                await match.components.default()
                console.log('预加载组件成功:', path)
              } catch (loadError) {
                console.warn('组件加载失败:', path, loadError)
              }
            } else {
              console.log('组件已加载:', path)
            }
          }
        }
      } else {
        console.warn('路由不存在:', path)
      }
    } catch (error) {
      console.warn('预加载路由失败:', path, error)
    }
  }

  console.log('路由预加载完成')
}

/**
 * 检查路由健康状态
 * @param {Object} router - Vue Router实例
 * @returns {Object} 健康状态报告
 */
export function checkRouteHealth(router) {
  const routes = router.getRoutes()
  const report = {
    total: routes.length,
    valid: 0,
    invalid: 0,
    details: []
  }

  routes.forEach(route => {
    const isValid = route.path && route.component
    if (isValid) {
      report.valid++
    } else {
      report.invalid++
      report.details.push({
        path: route.path,
        name: route.name,
        issue: !route.path ? 'missing path' : 'missing component'
      })
    }
  })

  console.log('路由健康检查报告:', report)
  return report
}

/**
 * 清除路由缓存
 * @param {Object} tagsViewStore - 标签视图状态管理
 */
export function clearRouteCache(tagsViewStore) {
  console.log('清除路由缓存')
  if (tagsViewStore && tagsViewStore.delAllCachedViews) {
    tagsViewStore.delAllCachedViews()
  }
}
