package com.dkd.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.core.page.TableDataInfo;
import com.dkd.common.enums.BusinessType;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.system.domain.SysPost;
import com.dkd.system.service.ISysPostService;

/**
 * 岗位信息操作处理
 * 
 * <AUTHOR>
 */
// 定义REST控制器，处理系统模块岗位管理相关的HTTP请求
@RestController
@RequestMapping("/system/post")
public class SysPostController extends BaseController
{
    // 注入岗位管理业务层接口
    @Autowired
    private ISysPostService postService;

    /**
     * 获取岗位列表接口
     * 需要'system:post:list'权限
     * 请求路径：GET /system/post/list
     */
    @PreAuthorize("@ss.hasPermi('system:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysPost post)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询岗位数据
        List<SysPost> list = postService.selectPostList(post);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出岗位信息接口
     * 需要'system:post:export'权限
     * 请求路径：POST /system/post/export
     */
    @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:post:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPost post)
    {
        // 调用服务层方法查询需要导出的岗位数据
        List<SysPost> list = postService.selectPostList(post);
        // 创建Excel工具类实例
        ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "岗位数据");
    }

    /**
     * 根据岗位编号获取详细信息接口
     * 需要'system:post:query'权限
     * 请求路径：GET /system/post/{postId}
     */
    @PreAuthorize("@ss.hasPermi('system:post:query')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable Long postId)
    {
        // 调用服务层方法获取岗位详情
        return success(postService.selectPostById(postId));
    }

    /**
     * 新增岗位接口
     * 需要'system:post:add'权限
     * 请求路径：POST /system/post
     */
    @PreAuthorize("@ss.hasPermi('system:post:add')")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysPost post)
    {
        // 校验岗位名称是否唯一
        if (!postService.checkPostNameUnique(post))
        {
            return error("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        }
        // 校验岗位编码是否唯一
        else if (!postService.checkPostCodeUnique(post))
        {
            return error("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }
        // 设置创建人
        post.setCreateBy(getUsername());
        // 调用服务层方法新增岗位
        return toAjax(postService.insertPost(post));
    }

    /**
     * 修改岗位接口
     * 需要'system:post:edit'权限
     * 请求路径：PUT /system/post
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysPost post)
    {
        // 校验岗位名称是否唯一
        if (!postService.checkPostNameUnique(post))
        {
            return error("修改岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        }
        // 校验岗位编码是否唯一
        else if (!postService.checkPostCodeUnique(post))
        {
            return error("修改岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }
        // 设置更新人
        post.setUpdateBy(getUsername());
        // 调用服务层方法修改岗位
        return toAjax(postService.updatePost(post));
    }

    /**
     * 删除岗位接口
     * 需要'system:post:remove'权限
     * 请求路径：DELETE /system/post/{postIds}
     */
    @PreAuthorize("@ss.hasPermi('system:post:remove')")
    @Log(title = "岗位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds)
    {
        // 调用服务层方法删除指定ID的岗位
        return toAjax(postService.deletePostByIds(postIds));
    }

    /**
     * 获取岗位选择框列表接口
     * 无需特殊权限
     * 请求路径：GET /system/post/optionselect
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        // 调用服务层方法获取所有岗位
        List<SysPost> posts = postService.selectPostAll();
        // 返回查询结果
        return success(posts);
    }
}






// @RestController
// @RequestMapping("/system/post")
// public class SysPostController extends BaseController
// {
//     @Autowired
//     private ISysPostService postService;

//     /**
//      * 获取岗位列表
//      */
//     @PreAuthorize("@ss.hasPermi('system:post:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(SysPost post)
//     {
//         startPage();
//         List<SysPost> list = postService.selectPostList(post);
//         return getDataTable(list);
//     }
    
//     @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
//     @PreAuthorize("@ss.hasPermi('system:post:export')")
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, SysPost post)
//     {
//         List<SysPost> list = postService.selectPostList(post);
//         ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
//         util.exportExcel(response, list, "岗位数据");
//     }

//     /**
//      * 根据岗位编号获取详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('system:post:query')")
//     @GetMapping(value = "/{postId}")
//     public AjaxResult getInfo(@PathVariable Long postId)
//     {
//         return success(postService.selectPostById(postId));
//     }

//     /**
//      * 新增岗位
//      */
//     @PreAuthorize("@ss.hasPermi('system:post:add')")
//     @Log(title = "岗位管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@Validated @RequestBody SysPost post)
//     {
//         if (!postService.checkPostNameUnique(post))
//         {
//             return error("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
//         }
//         else if (!postService.checkPostCodeUnique(post))
//         {
//             return error("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
//         }
//         post.setCreateBy(getUsername());
//         return toAjax(postService.insertPost(post));
//     }

//     /**
//      * 修改岗位
//      */
//     @PreAuthorize("@ss.hasPermi('system:post:edit')")
//     @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@Validated @RequestBody SysPost post)
//     {
//         if (!postService.checkPostNameUnique(post))
//         {
//             return error("修改岗位'" + post.getPostName() + "'失败，岗位名称已存在");
//         }
//         else if (!postService.checkPostCodeUnique(post))
//         {
//             return error("修改岗位'" + post.getPostName() + "'失败，岗位编码已存在");
//         }
//         post.setUpdateBy(getUsername());
//         return toAjax(postService.updatePost(post));
//     }

//     /**
//      * 删除岗位
//      */
//     @PreAuthorize("@ss.hasPermi('system:post:remove')")
//     @Log(title = "岗位管理", businessType = BusinessType.DELETE)
//     @DeleteMapping("/{postIds}")
//     public AjaxResult remove(@PathVariable Long[] postIds)
//     {
//         return toAjax(postService.deletePostByIds(postIds));
//     }

//     /**
//      * 获取岗位选择框列表
//      */
//     @GetMapping("/optionselect")
//     public AjaxResult optionselect()
//     {
//         List<SysPost> posts = postService.selectPostAll();
//         return success(posts);
//     }
// }
