package com.dkd.manage.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.dkd.common.utils.SecurityUtils;
import com.dkd.manage.domain.vo.PartnerVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Partner;
import com.dkd.manage.service.IPartnerService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 合作商管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
// 定义REST控制器，处理合作商管理相关的HTTP请求
@RestController
@RequestMapping("/manage/partner")
public class PartnerController extends BaseController
{
    // 注入合作商业务层接口
    @Autowired
    private IPartnerService partnerService;

    /**
     * 查询合作商列表接口
     * 需要'manage:partner:list'权限
     * 请求路径：GET /manage/partner/list
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:list')")
    @GetMapping("/list")
    public TableDataInfo list(Partner partner)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询合作商数据并返回视图对象列表
        List<PartnerVo> list = partnerService.selectPartnerVoList(partner);
        // 返回分页数据
        return getDataTable(list);
    }

    /**
     * 导出合作商列表接口
     * 需要'manage:partner:export'权限
     * 请求路径：POST /manage/partner/export
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:export')")
    @Log(title = "合作商管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Partner partner)
    {
        // 调用服务层方法查询需要导出的合作商数据
        List<Partner> list = partnerService.selectPartnerList(partner);
        // 创建Excel工具类实例
        ExcelUtil<Partner> util = new ExcelUtil<Partner>(Partner.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "合作商管理数据");
    }

    /**
     * 获取合作商详细信息接口
     * 需要'manage:partner:query'权限
     * 请求路径：GET /manage/partner/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取合作商详情
        return success(partnerService.selectPartnerById(id));
    }

    /**
     * 新增合作商接口
     * 需要'manage:partner:add'权限
     * 请求路径：POST /manage/partner
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:add')")
    @Log(title = "合作商管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Partner partner)
    {
        // 调用服务层方法新增合作商
        return toAjax(partnerService.insertPartner(partner));
    }

    /**
     * 修改合作商接口
     * 需要'manage:partner:edit'权限
     * 请求路径：PUT /manage/partner
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:edit')")
    @Log(title = "合作商管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Partner partner)
    {
        // 调用服务层方法修改合作商信息
        return toAjax(partnerService.updatePartner(partner));
    }

    /**
     * 删除合作商接口
     * 需要'manage:partner:remove'权限
     * 请求路径：DELETE /manage/partner/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:remove')")
    @Log(title = "合作商管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的合作商
        return toAjax(partnerService.deletePartnerByIds(ids));
    }

    /**
     * 重置合作商密码接口
     * 需要'manage:partner:edit'权限
     * 请求路径：PUT /manage/partner/resetPwd/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:edit')")
    @Log(title = "重置合作商密码", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd/{id}")
    public AjaxResult resetPwd(@PathVariable Long id)
    {
        // 创建合作商对象并设置ID
        Partner partner = new Partner();
        partner.setId(id);
        // 设置加密后的初始密码
        partner.setPassword(SecurityUtils.encryptPassword("123456"));
        // 调用服务层方法更新合作商密码
        return toAjax(partnerService.updatePartner(partner));
    }

    /**
     * 获取合作商点位分布统计数据
     * 用于生成合作商点位分布统计图表
     * 需要'manage:partner:list'权限
     * 请求路径：GET /manage/partner/nodeStats
     */
    @PreAuthorize("@ss.hasPermi('manage:partner:list')")
    @GetMapping("/nodeStats")
    public AjaxResult getPartnerNodeStats()
    {
        // 调用服务层方法获取合作商点位分布统计数据
        Map<String, Object> stats = partnerService.getPartnerNodeStats();
        // 返回成功结果，包含统计数据
        return AjaxResult.success(stats);
    }
}



// @RestController
// @RequestMapping("/manage/partner")
// public class PartnerController extends BaseController
// {
//     @Autowired
//     private IPartnerService partnerService;

//     /**
//      * 查询合作商管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:partner:list')")
//     @GetMapping("/list")
//     public TableDataInfo list(Partner partner)
//     {
//         startPage();
//         List<PartnerVo> list = partnerService.selectPartnerVoList(partner);
//         return getDataTable(list);
//     }

//     /**
//      * 导出合作商管理列表
//      */
//     @PreAuthorize("@ss.hasPermi('manage:partner:export')")
//     @Log(title = "合作商管理", businessType = BusinessType.EXPORT)
//     @PostMapping("/export")
//     public void export(HttpServletResponse response, Partner partner)
//     {
//         List<Partner> list = partnerService.selectPartnerList(partner);
//         ExcelUtil<Partner> util = new ExcelUtil<Partner>(Partner.class);
//         util.exportExcel(response, list, "合作商管理数据");
//     }

//     /**
//      * 获取合作商管理详细信息
//      */
//     @PreAuthorize("@ss.hasPermi('manage:partner:query')")
//     @GetMapping(value = "/{id}")
//     public AjaxResult getInfo(@PathVariable("id") Long id)
//     {
//         return success(partnerService.selectPartnerById(id));
//     }

//     /**
//      * 新增合作商管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:partner:add')")
//     @Log(title = "合作商管理", businessType = BusinessType.INSERT)
//     @PostMapping
//     public AjaxResult add(@RequestBody Partner partner)
//     {
//         return toAjax(partnerService.insertPartner(partner));
//     }

//     /**
//      * 修改合作商管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:partner:edit')")
//     @Log(title = "合作商管理", businessType = BusinessType.UPDATE)
//     @PutMapping
//     public AjaxResult edit(@RequestBody Partner partner)
//     {
//         return toAjax(partnerService.updatePartner(partner));
//     }

//     /**
//      * 删除合作商管理
//      */
//     @PreAuthorize("@ss.hasPermi('manage:partner:remove')")
//     @Log(title = "合作商管理", businessType = BusinessType.DELETE)
// 	@DeleteMapping("/{ids}")
//     public AjaxResult remove(@PathVariable Long[] ids)
//     {
//         return toAjax(partnerService.deletePartnerByIds(ids));
//     }


//     /**
//      * 重置合作商密码
//      * @param id
//      * @return
//      */
//     @PreAuthorize("@ss.hasPermi('manage:partner:edit')")
//     @Log(title = "重置合作商密码", businessType = BusinessType.UPDATE)
//     @PutMapping("/resetPwd/{id}")
//     public AjaxResult resetPwd(@PathVariable Long id)
//     {
//         //2. 创建合作商对象
//         Partner partner = new Partner();
//         partner.setId(id);
//         // 设置加密后的初始密码
//         partner.setPassword(SecurityUtils.encryptPassword("123456"));
//         //3. 调用service更新密码
//         return toAjax(partnerService.updatePartner(partner));
//     }



// }
