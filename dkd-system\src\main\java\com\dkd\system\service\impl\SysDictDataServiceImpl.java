package com.dkd.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dkd.common.core.domain.entity.SysDictData;
import com.dkd.common.utils.DictUtils;
import com.dkd.system.mapper.SysDictDataMapper;
import com.dkd.system.service.ISysDictDataService;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
// 定义字典数据服务实现类
@Service
public class SysDictDataServiceImpl implements ISysDictDataService
{
    // 注入字典数据数据库操作Mapper
    @Autowired
    private SysDictDataMapper dictDataMapper;

    /**
     * 根据条件分页查询字典数据
     * 
     * @param dictData 查询条件封装对象
     * @return 符合条件的字典数据列表
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData)
    {
        return dictDataMapper.selectDictDataList(dictData);
    }

    /**
     * 根据字典类型和键值查询对应的标签
     * 
     * @param dictType 字典类型编码
     * @param dictValue 字典键值
     * @return 对应的字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue)
    {
        return dictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询详细信息
     * 
     * @param dictCode 字典数据ID
     * @return 字典数据实体对象
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode)
    {
        return dictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据
     * 删除后同步更新对应字典类型的缓存
     * 
     * @param dictCodes 需要删除的字典数据ID数组
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes)
    {
        for (Long dictCode : dictCodes)
        {
            // 查询当前字典数据
            SysDictData data = selectDictDataById(dictCode);
            
            // 从数据库中删除
            dictDataMapper.deleteDictDataById(dictCode);
            
            // 查询该字典类型下的所有数据
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            
            // 更新全局字典缓存
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
    }

    /**
     * 新增字典数据
     * 插入成功后同步更新字典缓存
     * 
     * @param data 待新增的字典数据
     * @return 插入影响行数
     */
    @Override
    public int insertDictData(SysDictData data)
    {
        int row = dictDataMapper.insertDictData(data);
        if (row > 0)
        {
            // 查询该字典类型下所有数据
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            
            // 更新全局字典缓存
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }

    /**
     * 修改字典数据
     * 更新成功后同步更新字典缓存
     * 
     * @param data 待修改的字典数据
     * @return 修改影响行数
     */
    @Override
    public int updateDictData(SysDictData data)
    {
        int row = dictDataMapper.updateDictData(data);
        if (row > 0)
        {
            // 查询该字典类型下所有数据
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            
            // 更新全局字典缓存
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }
}




// @Service
// public class SysDictDataServiceImpl implements ISysDictDataService
// {
//     @Autowired
//     private SysDictDataMapper dictDataMapper;

//     /**
//      * 根据条件分页查询字典数据
//      * 
//      * @param dictData 字典数据信息
//      * @return 字典数据集合信息
//      */
//     @Override
//     public List<SysDictData> selectDictDataList(SysDictData dictData)
//     {
//         return dictDataMapper.selectDictDataList(dictData);
//     }

//     /**
//      * 根据字典类型和字典键值查询字典数据信息
//      * 
//      * @param dictType 字典类型
//      * @param dictValue 字典键值
//      * @return 字典标签
//      */
//     @Override
//     public String selectDictLabel(String dictType, String dictValue)
//     {
//         return dictDataMapper.selectDictLabel(dictType, dictValue);
//     }

//     /**
//      * 根据字典数据ID查询信息
//      * 
//      * @param dictCode 字典数据ID
//      * @return 字典数据
//      */
//     @Override
//     public SysDictData selectDictDataById(Long dictCode)
//     {
//         return dictDataMapper.selectDictDataById(dictCode);
//     }

//     /**
//      * 批量删除字典数据信息
//      * 
//      * @param dictCodes 需要删除的字典数据ID
//      */
//     @Override
//     public void deleteDictDataByIds(Long[] dictCodes)
//     {
//         for (Long dictCode : dictCodes)
//         {
//             SysDictData data = selectDictDataById(dictCode);
//             dictDataMapper.deleteDictDataById(dictCode);
//             List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
//             DictUtils.setDictCache(data.getDictType(), dictDatas);
//         }
//     }

//     /**
//      * 新增保存字典数据信息
//      * 
//      * @param data 字典数据信息
//      * @return 结果
//      */
//     @Override
//     public int insertDictData(SysDictData data)
//     {
//         int row = dictDataMapper.insertDictData(data);
//         if (row > 0)
//         {
//             List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
//             DictUtils.setDictCache(data.getDictType(), dictDatas);
//         }
//         return row;
//     }

//     /**
//      * 修改保存字典数据信息
//      * 
//      * @param data 字典数据信息
//      * @return 结果
//      */
//     @Override
//     public int updateDictData(SysDictData data)
//     {
//         int row = dictDataMapper.updateDictData(data);
//         if (row > 0)
//         {
//             List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
//             DictUtils.setDictCache(data.getDictType(), dictDatas);
//         }
//         return row;
//     }
// }
