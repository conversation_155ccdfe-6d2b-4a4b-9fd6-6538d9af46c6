package com.dkd.web.controller.monitor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.constant.CacheConstants;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.utils.StringUtils;
import com.dkd.system.domain.SysCache;

/**
 * 缓存监控
 * 
 * 缓存监控控制器。
 * 提供 Redis 缓存信息查看与管理功能。
 */
@RestController
@RequestMapping("/monitor/cache")
public class CacheController
{
    /**
     * 自动注入 RedisTemplate 实例，用于操作 Redis 数据库。
     */
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 静态列表，保存系统中常用缓存键及其描述信息。
     */
    private final static List<SysCache> caches = new ArrayList<>();

    /**
     * 初始化缓存键集合。
     * 添加多个常见的系统缓存键及对应业务含义。
     */
    {
        caches.add(new SysCache(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
        caches.add(new SysCache(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
        caches.add(new SysCache(CacheConstants.SYS_DICT_KEY, "数据字典"));
        caches.add(new SysCache(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
        caches.add(new SysCache(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
        caches.add(new SysCache(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
        caches.add(new SysCache(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
    }

    /**
     * 获取 Redis 状态信息。
     * 包括服务器信息、数据库大小、命令调用统计等。
     *
     * @return AjaxResult 包含 Redis 信息的响应结果
     * @throws Exception 操作过程中可能抛出异常
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping()
    public AjaxResult getInfo() throws Exception
    {
        // 获取 Redis 基本信息
        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());

        // 获取 Redis 命令统计信息
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));

        // 获取当前数据库大小
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

        // 构建返回结果对象
        Map<String, Object> result = new HashMap<>(3);
        result.put("info", info);
        result.put("dbSize", dbSize);

        // 解析并转换命令调用统计数据为饼图格式
        List<Map<String, String>> pieList = new ArrayList<>();
        commandStats.stringPropertyNames().forEach(key -> {
            Map<String, String> data = new HashMap<>(2);
            String property = commandStats.getProperty(key);
            data.put("name", StringUtils.removeStart(key, "cmdstat_")); // 去除前缀 cmdstat_
            data.put("value", StringUtils.substringBetween(property, "calls=", ",usec")); // 提取调用次数
            pieList.add(data);
        });
        result.put("commandStats", pieList);

        return AjaxResult.success(result);
    }

    /**
     * 获取系统支持的缓存分类名称列表。
     *
     * @return AjaxResult 包含缓存分类信息的响应结果
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getNames")
    public AjaxResult cache()
    {
        return AjaxResult.success(caches);
    }

    /**
     * 获取指定缓存分类下的所有缓存键。
     *
     * @param cacheName 缓存分类名称
     * @return AjaxResult 包含缓存键列表的响应结果
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getKeys/{cacheName}")
    public AjaxResult getCacheKeys(@PathVariable String cacheName)
    {
        // 使用通配符匹配该分类下所有缓存键
        Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        return AjaxResult.success(cacheKeys);
    }

    /**
     * 获取指定缓存键的值。
     *
     * @param cacheName 缓存分类名称
     * @param cacheKey 缓存键
     * @return AjaxResult 包含缓存值信息的响应结果
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    public AjaxResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey)
    {
        // 从 Redis 中获取缓存值
        String cacheValue = redisTemplate.opsForValue().get(cacheKey);

        // 封装为 SysCache 对象返回
        SysCache sysCache = new SysCache(cacheName, cacheKey, cacheValue);
        return AjaxResult.success(sysCache);
    }

    /**
     * 清空指定缓存分类下的所有缓存键。
     *
     * @param cacheName 缓存分类名称
     * @return AjaxResult 成功响应
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheName/{cacheName}")
    public AjaxResult clearCacheName(@PathVariable String cacheName)
    {
        Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        redisTemplate.delete(cacheKeys); // 删除匹配的所有缓存键
        return AjaxResult.success();
    }

    /**
     * 清空指定缓存键的值。
     *
     * @param cacheKey 缓存键
     * @return AjaxResult 成功响应
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheKey/{cacheKey}")
    public AjaxResult clearCacheKey(@PathVariable String cacheKey)
    {
        redisTemplate.delete(cacheKey); // 删除单个缓存键
        return AjaxResult.success();
    }

    /**
     * 清空所有缓存键。
     *
     * @return AjaxResult 成功响应
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheAll")
    public AjaxResult clearCacheAll()
    {
        Collection<String> cacheKeys = redisTemplate.keys("*"); // 匹配所有缓存键
        redisTemplate.delete(cacheKeys); // 删除所有缓存键
        return AjaxResult.success();
    }
}


// @RestController
// @RequestMapping("/monitor/cache")
// public class CacheController
// {
//     @Autowired
//     private RedisTemplate<String, String> redisTemplate;

//     private final static List<SysCache> caches = new ArrayList<SysCache>();
//     {
//         caches.add(new SysCache(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
//         caches.add(new SysCache(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
//         caches.add(new SysCache(CacheConstants.SYS_DICT_KEY, "数据字典"));
//         caches.add(new SysCache(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
//         caches.add(new SysCache(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
//         caches.add(new SysCache(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
//         caches.add(new SysCache(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
//     @GetMapping()
//     public AjaxResult getInfo() throws Exception
//     {
//         Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());
//         Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
//         Object dbSize = redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

//         Map<String, Object> result = new HashMap<>(3);
//         result.put("info", info);
//         result.put("dbSize", dbSize);

//         List<Map<String, String>> pieList = new ArrayList<>();
//         commandStats.stringPropertyNames().forEach(key -> {
//             Map<String, String> data = new HashMap<>(2);
//             String property = commandStats.getProperty(key);
//             data.put("name", StringUtils.removeStart(key, "cmdstat_"));
//             data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
//             pieList.add(data);
//         });
//         result.put("commandStats", pieList);
//         return AjaxResult.success(result);
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
//     @GetMapping("/getNames")
//     public AjaxResult cache()
//     {
//         return AjaxResult.success(caches);
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
//     @GetMapping("/getKeys/{cacheName}")
//     public AjaxResult getCacheKeys(@PathVariable String cacheName)
//     {
//         Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
//         return AjaxResult.success(cacheKeys);
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
//     @GetMapping("/getValue/{cacheName}/{cacheKey}")
//     public AjaxResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey)
//     {
//         String cacheValue = redisTemplate.opsForValue().get(cacheKey);
//         SysCache sysCache = new SysCache(cacheName, cacheKey, cacheValue);
//         return AjaxResult.success(sysCache);
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
//     @DeleteMapping("/clearCacheName/{cacheName}")
//     public AjaxResult clearCacheName(@PathVariable String cacheName)
//     {
//         Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
//         redisTemplate.delete(cacheKeys);
//         return AjaxResult.success();
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
//     @DeleteMapping("/clearCacheKey/{cacheKey}")
//     public AjaxResult clearCacheKey(@PathVariable String cacheKey)
//     {
//         redisTemplate.delete(cacheKey);
//         return AjaxResult.success();
//     }

//     @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
//     @DeleteMapping("/clearCacheAll")
//     public AjaxResult clearCacheAll()
//     {
//         Collection<String> cacheKeys = redisTemplate.keys("*");
//         redisTemplate.delete(cacheKeys);
//         return AjaxResult.success();
//     }
// }
