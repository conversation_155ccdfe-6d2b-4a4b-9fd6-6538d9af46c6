package com.dkd.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.dkd.manage.domain.vo.NodeVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dkd.common.annotation.Log;
import com.dkd.common.core.controller.BaseController;
import com.dkd.common.core.domain.AjaxResult;
import com.dkd.common.enums.BusinessType;
import com.dkd.manage.domain.Node;
import com.dkd.manage.service.INodeService;
import com.dkd.common.utils.poi.ExcelUtil;
import com.dkd.common.core.page.TableDataInfo;

/**
 * 点位管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
// 定义REST控制器，处理点位管理相关的HTTP请求
@RestController
@RequestMapping("/manage/node")
public class NodeController extends BaseController
{
    // 注入点位管理业务层接口
    @Autowired
    private INodeService nodeService;

    /**
     * 查询点位管理列表接口
     * 需要'manage:node:list'权限
     * 请求路径：GET /manage/node/list
     */
    @PreAuthorize("@ss.hasPermi('manage:node:list')")
    @GetMapping("/list")
    public TableDataInfo list(Node node)
    {
        // 开启分页功能
        startPage();
        // 调用服务层方法查询点位数据并返回视图对象列表
        List<NodeVo> voList = nodeService.selectNodeVoList(node);
        // 返回分页数据
        return getDataTable(voList);
    }

    /**
     * 导出点位管理列表接口
     * 需要'manage:node:export'权限
     * 请求路径：POST /manage/node/export
     */
    @PreAuthorize("@ss.hasPermi('manage:node:export')")
    @Log(title = "点位管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Node node)
    {
        // 调用服务层方法查询需要导出的点位数据
        List<Node> list = nodeService.selectNodeList(node);
        // 创建Excel工具类实例
        ExcelUtil<Node> util = new ExcelUtil<Node>(Node.class);
        // 调用工具类方法进行Excel导出
        util.exportExcel(response, list, "点位管理数据");
    }

    /**
     * 获取点位详细信息接口
     * 需要'manage:node:query'权限
     * 请求路径：GET /manage/node/{id}
     */
    @PreAuthorize("@ss.hasPermi('manage:node:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 调用服务层方法获取点位详情
        return success(nodeService.selectNodeById(id));
    }

    /**
     * 新增点位接口
     * 需要'manage:node:add'权限
     * 请求路径：POST /manage/node
     */
    @PreAuthorize("@ss.hasPermi('manage:node:add')")
    @Log(title = "点位管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Node node)
    {
        // 调用服务层方法新增点位
        return toAjax(nodeService.insertNode(node));
    }

    /**
     * 修改点位接口
     * 需要'manage:node:edit'权限
     * 请求路径：PUT /manage/node
     */
    @PreAuthorize("@ss.hasPermi('manage:node:edit')")
    @Log(title = "点位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Node node)
    {
        // 调用服务层方法修改点位信息
        return toAjax(nodeService.updateNode(node));
    }

    /**
     * 删除点位接口
     * 需要'manage:node:remove'权限
     * 请求路径：DELETE /manage/node/{ids}
     */
    @PreAuthorize("@ss.hasPermi('manage:node:remove')")
    @Log(title = "点位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 调用服务层方法删除指定ID的点位
        return toAjax(nodeService.deleteNodeByIds(ids));
    }

    /**
     * 获取点位设备统计数据
     * 用于生成点位设备统计图表
     * 需要'manage:node:list'权限
     * 请求路径：GET /manage/node/vmStats
     */
    @PreAuthorize("@ss.hasPermi('manage:node:list')")
    @GetMapping("/vmStats")
    public AjaxResult getNodeVmStats()
    {
        // 调用服务层方法获取点位设备统计数据
        List<NodeVo> stats = nodeService.getNodeVmStats();
        // 返回成功结果，包含统计数据
        return AjaxResult.success(stats);
    }
}
