// 包声明：定义当前类所属的包路径
package com.dkd.manage.controller;

// 导入通用的Ajax响应结果类，用于统一API响应格式
import com.dkd.common.core.domain.AjaxResult;
// 导入RAG聊天服务类，提供智能体应用调用功能
import com.dkd.manage.service.RagChatService;

// 导入Jackson JSON处理相关类，用于解析JSON响应
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
// 导入Lombok日志注解，自动生成日志对象
import lombok.extern.slf4j.Slf4j;

// 导入Spring框架相关注解和类
import org.springframework.beans.factory.annotation.Autowired; // 依赖注入注解
import org.springframework.beans.factory.annotation.Value; // 配置值注入注解
import org.springframework.http.HttpEntity; // HTTP请求实体类
import org.springframework.http.HttpHeaders; // HTTP请求头类
import org.springframework.http.MediaType; // HTTP媒体类型类
import org.springframework.http.ResponseEntity; // HTTP响应实体类
import org.springframework.security.access.prepost.PreAuthorize; // 权限控制注解
import org.springframework.web.bind.annotation.*; // Web控制器相关注解
import org.springframework.web.client.RestTemplate; // HTTP客户端类

// 导入Java标准库类
import java.io.IOException; // IO异常类
import java.util.*; // 集合框架相关类
import java.util.concurrent.ConcurrentHashMap; // 线程安全的HashMap
import java.util.concurrent.ExecutorService; // 线程池执行器接口
import java.util.concurrent.Executors; // 线程池工具类
import java.util.concurrent.atomic.AtomicReference; // 原子引用类

// 导入Spring WebFlux相关类，用于响应式编程
import org.springframework.web.reactive.function.client.WebClient; // 响应式HTTP客户端
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter; // 服务器发送事件发射器
import reactor.core.publisher.Flux; // 响应式流发布者

/**
 * AI助手控制器类
 * 处理与智能体应用相关的HTTP请求，提供聊天、流式聊天、会话管理等功能
 * 支持智能路由，自动判断是否使用专业知识库
 */
@Slf4j // Lombok注解，自动生成log日志对象
@RestController // Spring注解，标识这是一个REST控制器
@RequestMapping("/ai") // 请求映射注解，所有请求都以/ai开头
@CrossOrigin // 跨域注解，允许跨域请求
public class AssistantController {

    // 从配置文件中注入阿里云百炼平台的API密钥
    @Value("${aliyun.bailian.api-key}")
    private String apiKey;

    // 使用@Autowired注解自动注入RAG聊天服务，提供智能体应用调用功能
    @Autowired
    private RagChatService ragChatService;

    // 存储每个会话的历史消息记录，使用ConcurrentHashMap确保线程安全
    // Key: 会话ID（String），Value: 消息历史列表（List<Map<String, String>>）
    private static final Map<String, List<Map<String, String>>> sessionHistories = new ConcurrentHashMap<>();

    // 定义每个会话历史消息的最大长度，防止内存溢出
    private static final int MAX_HISTORY_SIZE = 20;

    // 定义系统支持的最大会话数量，防止内存无限增长
    private static final int MAX_SESSIONS = 1000;

    // 创建缓存线程池，用于异步处理流式聊天请求，提高并发性能
    private final ExecutorService executor = Executors.newCachedThreadPool();

    // 创建WebClient实例，用于发送响应式HTTP请求，支持流式数据处理
    private final WebClient webClient = WebClient.create();

    /**
     * 流式聊天接口（SSE）
     * 使用Server-Sent Events协议提供实时流式聊天功能
     * 需要'manage:ai:list'权限
     * 请求路径：POST /ai/chat-stream
     *
     * @param params 请求参数，包含sessionId和input.messages
     * @return SseEmitter 服务器发送事件发射器，用于流式响应
     */
    @PreAuthorize("@ss.hasPermi('manage:ai:list')") // 权限控制注解，检查用户是否有AI列表权限
    @PostMapping(value = "/chat-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE) // 指定响应类型为SSE
    public SseEmitter streamChat(@RequestBody Map<String, Object> params) {
        // 创建SSE发射器，设置超时时间为60秒，用于向客户端发送流式数据
        SseEmitter emitter = new SseEmitter(60_000L);

        // 使用线程池异步执行流式请求，避免阻塞主线程
        executor.execute(() -> {
            try { // 使用try-catch块处理异步执行中的异常
                // 从请求参数中获取会话ID，用于维护对话连续性
                String sessionId = (String) params.get("sessionId");
                // 如果会话ID为空，则生成一个新的UUID作为会话ID
                if (sessionId == null || sessionId.isEmpty()) {
                    sessionId = UUID.randomUUID().toString();
                }

                // 获取或创建该会话的历史对话记录，使用computeIfAbsent确保线程安全
                List<Map<String, String>> history = sessionHistories.computeIfAbsent(sessionId, k -> new ArrayList<>());

                // 从请求参数中获取用户输入的新消息
                @SuppressWarnings("unchecked") // 抑制类型转换警告
                Map<String, Object> input = (Map<String, Object>) params.get("input");
                @SuppressWarnings("unchecked") // 抑制类型转换警告
                List<Map<String, String>> newMessages = (List<Map<String, String>>) input.get("messages");

                // 构建包含历史消息和新消息的完整消息列表
                List<Map<String, String>> fullMessages = new ArrayList<>(history);
                // 将新消息添加到完整消息列表中
                fullMessages.addAll(newMessages);

                // 构建发送给AI API的请求体
                Map<String, Object> requestBody = new HashMap<>();
                // 指定使用的AI模型
                requestBody.put("model", "qwen-turbo");
                // 设置输入消息
                requestBody.put("input", Map.of("messages", fullMessages));
                // 设置参数，启用流式输出
                requestBody.put("parameters", Map.of("stream", true));

                // 设置HTTP请求头信息
                HttpHeaders headers = new HttpHeaders();
                // 设置Bearer认证头，使用配置的API密钥
                headers.set("Authorization", "Bearer " + apiKey);
                // 设置内容类型为JSON
                headers.setContentType(MediaType.APPLICATION_JSON);

                // 设置阿里云百炼平台的API调用URL地址
                String apiUrl = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation";

                // 使用WebClient发起响应式流式HTTP请求
                Flux<String> responseFlux = webClient.post()
                        .uri(apiUrl) // 设置请求URI
                        .headers(h -> h.addAll(headers)) // 添加请求头
                        .bodyValue(requestBody) // 设置请求体
                        .retrieve() // 执行请求
                        .bodyToFlux(String.class); // 将响应体转换为字符串流

                // 使用AtomicReference保存上一次返回的完整内容，用于计算增量内容
                AtomicReference<String> lastContent = new AtomicReference<>("");

                // 创建最终的会话ID变量，供lambda表达式使用（lambda表达式要求变量为final或effectively final）
                String finalSessionId = sessionId;

                // 订阅响应数据流，处理流式响应
                responseFlux.subscribe(
                        data -> { // 处理每个数据块的回调函数
                            try {
                                // 创建JSON解析器，解析AI API的响应数据
                                ObjectMapper mapper = new ObjectMapper();
                                JsonNode jsonNode = mapper.readTree(data);
                                // 从响应中提取AI生成的文本内容
                                String currentContent = jsonNode.path("output").path("text").asText();

                                // 计算本次新增的内容（增量内容），避免重复发送
                                String delta = currentContent.substring(lastContent.get().length());
                                // 更新保存的完整内容
                                lastContent.set(currentContent);

                                // 构建发送给前端的事件数据
                                Map<String, Object> eventData = new HashMap<>();
                                eventData.put("content", delta); // 增量内容
                                eventData.put("isEnd", false); // 标识未结束
                                eventData.put("sessionId", finalSessionId); // 会话ID

                                // 通过SSE发射器发送事件数据给前端
                                emitter.send(SseEmitter.event().data(eventData));
                            } catch (Exception e) {
                                // 发生异常时结束SSE连接，并将异常传递给前端
                                emitter.completeWithError(e);
                            }
                        },
                        emitter::completeWithError, // 错误处理回调，直接传递给SSE发射器
                        () -> { // 流完成时的回调函数
                            // 响应完成时，将新消息添加到历史记录中
                            history.addAll(newMessages);
                            // 将AI的回答也添加到历史记录中
                            history.add(Map.of("role", "assistant", "content", lastContent.get()));

                            // 构建流结束的事件数据
                            Map<String, Object> endEvent = new HashMap<>();
                            endEvent.put("isEnd", true); // 标识流已结束
                            try {
                                // 发送结束标记事件给前端，通知流式响应已完成
                                emitter.send(SseEmitter.event().data(endEvent));
                            } catch (IOException e) {
                                // 如果发送结束事件失败，抛出运行时异常
                                throw new RuntimeException(e);
                            }
                            // 正常结束SSE连接
                            emitter.complete();
                        }
                ); // 结束responseFlux.subscribe调用
            } catch (Exception e) {
                // 如果在异步执行过程中出现任何错误，结束SSE连接并传递错误
                emitter.completeWithError(e);
            }
        }); // 结束executor.execute调用

        // 立即返回SSE发射器给客户端，客户端可以开始监听事件
        return emitter;
    }

    /**
     * RAG增强聊天接口
     * 提供智能路由功能，自动判断是否使用专业知识库
     * 需要'manage:ai:list'权限
     * 请求路径：POST /ai/rag-chat
     *
     * @param params 请求参数，包含sessionId和message
     * @return AjaxResult 包含AI回答和会话信息的响应结果
     */
    @PreAuthorize("@ss.hasPermi('manage:ai:list')") // 权限控制注解，检查用户是否有AI列表权限
    @PostMapping("/rag-chat") // 处理POST请求，路径为/ai/rag-chat
    public AjaxResult ragChat(@RequestBody Map<String, Object> params) {
        try { // 使用try-catch块处理可能的异常
            // 记录收到RAG聊天请求的日志
            log.info("收到RAG聊天请求");

            // 从请求参数中获取会话ID，用于维护对话连续性
            String sessionId = (String) params.get("sessionId");
            // 如果会话ID为空，则生成一个新的UUID作为会话ID
            if (sessionId == null || sessionId.isEmpty()) {
                sessionId = UUID.randomUUID().toString();
            }

            // 从请求参数中获取用户输入的消息内容
            String userMessage = (String) params.get("message");
            // 验证消息内容是否为空
            if (userMessage == null || userMessage.trim().isEmpty()) {
                return AjaxResult.error("消息内容不能为空");
            }

            // 获取或创建该会话的历史对话记录，使用computeIfAbsent确保线程安全
            List<Map<String, String>> history = sessionHistories.computeIfAbsent(sessionId, k -> new ArrayList<>());

            // 调用RAG聊天服务的智能聊天方法，自动判断是否使用专业知识库
            String response = ragChatService.intelligentChat(userMessage, history);

            // 创建用户消息对象，添加到会话历史中
            Map<String, String> userMsg = new HashMap<>();
            userMsg.put("role", "user"); // 设置角色为用户
            userMsg.put("content", userMessage); // 设置消息内容
            history.add(userMsg); // 添加到历史记录

            // 创建助手消息对象，添加到会话历史中
            Map<String, String> assistantMsg = new HashMap<>();
            assistantMsg.put("role", "assistant"); // 设置角色为助手
            assistantMsg.put("content", response); // 设置AI回答内容
            history.add(assistantMsg); // 添加到历史记录

            // 调用清理方法，限制历史长度并清理过期会话，防止内存溢出
            cleanupSessionHistory(sessionId, history);

            // 构建返回给前端的结果对象
            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId); // 返回会话ID
            result.put("content", response); // 返回AI生成的回答内容
            result.put("useRag", ragChatService.shouldUseRag(userMessage)); // 返回是否使用了RAG模式

            // 返回成功响应，包含结果数据
            return AjaxResult.success(result);

        } catch (Exception e) {
            // 记录RAG聊天处理失败的错误日志
            log.error("RAG聊天处理失败", e);
            // 返回错误响应，包含错误信息
            return AjaxResult.error("RAG聊天失败：" + e.getMessage());
        }
    }

    /**
     * 普通聊天接口
     * 需要'manage:ai:list'权限
     * 请求路径：POST /ai/chat
     */
    @PreAuthorize("@ss.hasPermi('manage:ai:list')")
    @PostMapping("/chat")
    public AjaxResult sendMessage(@RequestBody Map<String, Object> params) {
        try {
            // 获取或生成会话ID
            String sessionId = (String) params.get("sessionId");
            if (sessionId == null || sessionId.isEmpty()) {
                sessionId = UUID.randomUUID().toString();
            }

            // 获取历史对话记录
            List<Map<String, String>> history = sessionHistories.computeIfAbsent(sessionId, k -> new ArrayList<>());

            // 获取用户输入的新消息
            Map<String, Object> input = (Map<String, Object>) params.get("input");
            List<Map<String, String>> newMessages = (List<Map<String, String>>) input.get("messages");

            if (newMessages == null || newMessages.isEmpty()) {
                return AjaxResult.error("没有新消息");
            }

            // 构建完整的请求消息（包含历史+新消息）
            List<Map<String, String>> fullMessages = new ArrayList<>(history);
            fullMessages.addAll(newMessages);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "qwen-turbo");

            Map<String, Object> requestInput = new HashMap<>();
            requestInput.put("messages", fullMessages);
            requestBody.put("input", requestInput);
            requestBody.put("parameters", params.get("parameters"));

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiKey);
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 调用大模型API地址
            String apiUrl = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation";

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送HTTP请求
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);

            // 解析响应结果
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(response.getBody());
            String assistantMessage = jsonNode.path("output").path("text").asText();

            // 更新历史记录
            history.addAll(newMessages);
            history.add(Map.of("role", "assistant", "content", assistantMessage));

            // 控制最大历史长度（保留最近10轮对话）
            int MAX_HISTORY_LENGTH = 10;
            if (history.size() > MAX_HISTORY_LENGTH * 2) {
                history = history.subList(history.size() - MAX_HISTORY_LENGTH * 2, history.size());
                sessionHistories.put(sessionId, history);
            }

            // 构建返回给前端的数据
            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("content", assistantMessage);

            // 返回成功响应
            return AjaxResult.success(result);
        } catch (Exception e) {
            // 返回异常错误信息
            return AjaxResult.error("消息发送失败：" + e.getMessage());
        }
    }

    /**
     * 知识库搜索接口
     * 需要'manage:ai:list'权限
     * 请求路径：POST /ai/knowledge-search
     */
    @PreAuthorize("@ss.hasPermi('manage:ai:list')")
    @PostMapping("/knowledge-search")
    public AjaxResult knowledgeSearch(@RequestBody Map<String, Object> params) {
        try {
            String query = (String) params.get("query");
            if (query == null || query.trim().isEmpty()) {
                return AjaxResult.error("查询内容不能为空");
            }

            String knowledgeInfo = ragChatService.getKnowledgeInfo(query);

            Map<String, Object> result = new HashMap<>();
            result.put("query", query);
            result.put("knowledgeInfo", knowledgeInfo);

            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("知识库搜索失败", e);
            return AjaxResult.error("知识库搜索失败：" + e.getMessage());
        }
    }

    /**
     * 获取智能体应用信息接口
     * 需要'manage:ai:edit'权限
     * 请求路径：POST /ai/agent-info
     */
    @PreAuthorize("@ss.hasPermi('manage:ai:edit')")
    @PostMapping("/agent-info")
    public AjaxResult getAgentInfo(@RequestBody Map<String, Object> params) {
        try {
            String query = (String) params.getOrDefault("query", "系统功能");
            String info = ragChatService.getKnowledgeInfo(query);
            return AjaxResult.success("智能体应用信息获取成功", info);
        } catch (Exception e) {
            log.error("获取智能体应用信息失败", e);
            return AjaxResult.error("获取智能体应用信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取智能体应用状态接口
     * 需要'manage:ai:list'权限
     * 请求路径：GET /ai/agent-status
     */
    @PreAuthorize("@ss.hasPermi('manage:ai:list')")
    @GetMapping("/agent-status")
    public AjaxResult getAgentStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("initialized", true); // 智能体应用总是可用的
            status.put("stats", "智能体应用运行正常，已内置专业知识库");
            status.put("appId", "3cb90973609846038bba35b3eb73aaf4");

            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("获取智能体应用状态失败", e);
            return AjaxResult.error("获取智能体应用状态失败：" + e.getMessage());
        }
    }

    /**
     * 清空会话历史接口
     * 需要'manage:ai:list'权限
     * 请求路径：POST /ai/clear-session
     */
    @PreAuthorize("@ss.hasPermi('manage:ai:list')")
    @PostMapping("/clear-session")
    public AjaxResult clearSession(@RequestBody Map<String, Object> params) {
        try {
            String sessionId = (String) params.get("sessionId");
            if (sessionId != null && !sessionId.isEmpty()) {
                sessionHistories.remove(sessionId);
                return AjaxResult.success("会话历史已清空");
            } else {
                return AjaxResult.error("会话ID不能为空");
            }
        } catch (Exception e) {
            log.error("清空会话历史失败", e);
            return AjaxResult.error("清空会话历史失败：" + e.getMessage());
        }
    }

    /**
     * 清理会话历史，防止内存泄漏
     */
    private void cleanupSessionHistory(String sessionId, List<Map<String, String>> history) {
        // 限制单个会话的历史长度
        if (history.size() > MAX_HISTORY_SIZE) {
            List<Map<String, String>> newHistory = history.subList(history.size() - MAX_HISTORY_SIZE, history.size());
            sessionHistories.put(sessionId, new ArrayList<>(newHistory));
        }

        // 限制总会话数量，删除最老的会话
        if (sessionHistories.size() > MAX_SESSIONS) {
            // 简单的LRU策略：删除一些会话
            List<String> sessionIds = new ArrayList<>(sessionHistories.keySet());
            int toRemove = sessionHistories.size() - MAX_SESSIONS + 100; // 一次性删除多一些
            for (int i = 0; i < toRemove && i < sessionIds.size(); i++) {
                sessionHistories.remove(sessionIds.get(i));
            }
            log.info("清理了 {} 个过期会话，当前会话数: {}", toRemove, sessionHistories.size());
        }
    }
}