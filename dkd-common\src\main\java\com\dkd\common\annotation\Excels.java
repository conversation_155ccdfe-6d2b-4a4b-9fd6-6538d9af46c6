// 定义包路径
package com.dkd.common.annotation;

// 导入Java注解相关的类
import java.lang.annotation.ElementType; // 定义注解可以应用的程序元素类型
import java.lang.annotation.Retention; // 定义注解的保留策略
import java.lang.annotation.RetentionPolicy; // 定义注解保留策略的枚举
import java.lang.annotation.Target; // 定义注解的目标元素类型

/**
 * Excel注解集
 * 用于在一个字段上应用多个Excel注解
 * 当需要在不同的导出场景中使用不同的Excel配置时使用
 * 例如：同一个字段在不同的导出模板中有不同的列名或格式
 *
 * <AUTHOR>
 */
@Target(ElementType.FIELD) // 指定注解只能应用于字段
@Retention(RetentionPolicy.RUNTIME) // 指定注解在运行时保留，可以通过反射获取
public @interface Excels // 定义Excel注解集接口
{
    /**
     * Excel注解数组
     * 包含多个Excel注解配置
     * 每个Excel注解可以有不同的配置参数
     *
     * @return Excel注解数组
     */
    public Excel[] value(); // 定义Excel注解数组属性
}
